'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { useAppSelector } from '@/lib/redux/hooks';
import { useLanguage } from '@/lib/contexts/language-context';
import {
  canUserEditEvents,
  canUserSuperviseEvents,
  canUserValidateEventReports
} from '@/lib/utils/permissions-utils';
import {
  Workflow,
  Play,
  FileText,
  Send,
  CheckCircle,
  Clock,
  AlertTriangle
} from 'lucide-react';
import { toast } from 'sonner';

interface IEvent {
  _id: string;
  name: string;
  status: 'new' | 'in_progress' | 'cancelled' | 'processing_report' | 'awaiting_validation' | 'done';
  startDate: string;
  endDate: string;
}

interface IEventReport {
  _id: string;
  status: 'pending' | 'processing' | 'submitted' | 'validated';
  eventId: {
    _id: string;
    name: string;
  };
}

interface WorkflowStatusProps {
  event: IEvent;
  report?: IEventReport;
  onStatusUpdate?: () => void;
}

interface WorkflowAction {
  label: string;
  action: () => void;
  variant: 'default' | 'outline' | 'destructive';
  icon: React.ComponentType<{ className?: string }>;
  loading?: boolean;
}

export function WorkflowStatus({ event, report, onStatusUpdate }: WorkflowStatusProps) {
  const router = useRouter();
  const permissions = useAppSelector(state => state.permissions);
  const { t } = useLanguage();
  const [loading, setLoading] = useState<string | null>(null);

  const statusConfig = {
    new: { label: t('events.workflow.status.new') || 'New', color: 'bg-gray-500', progress: 0 },
    in_progress: { label: t('events.workflow.status.inProgress') || 'In Progress', color: 'bg-blue-500', progress: 25 },
    cancelled: { label: t('events.workflow.status.cancelled') || 'Cancelled', color: 'bg-red-500', progress: 0 },
    processing_report: { label: t('events.workflow.status.processingReport') || 'Processing Report', color: 'bg-orange-500', progress: 50 },
    awaiting_validation: { label: t('events.workflow.status.awaitingValidation') || 'Awaiting Validation', color: 'bg-purple-500', progress: 75 },
    done: { label: t('events.workflow.status.completed') || 'Completed', color: 'bg-green-500', progress: 100 }
  };

  const canEdit = permissions && canUserEditEvents(permissions);
  const canSupervise = permissions && canUserSuperviseEvents(permissions);
  const canValidate = permissions && canUserValidateEventReports(permissions);

  const updateEventStatus = async (eventId: string, newStatus: string) => {
    setLoading('status');
    try {
      const response = await fetch(`/api/events/${eventId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      });

      if (!response.ok) {
        throw new Error('Failed to update event status');
      }

      toast.success(t('events.workflow.messages.statusUpdateSuccess') || 'Event status updated successfully');
      onStatusUpdate?.();
    } catch (error) {
      console.error('Error updating event status:', error);
      toast.error(t('events.workflow.messages.statusUpdateFailed') || 'Failed to update event status');
    } finally {
      setLoading(null);
    }
  };

  const submitReport = async (reportId: string) => {
    setLoading('submit');
    try {
      const response = await fetch(`/api/event-reports/${reportId}/submit`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Submit validation error:', errorData);

        // Show detailed validation errors if available
        if (errorData.details && Array.isArray(errorData.details)) {
          const errorMessage = `${t('events.workflow.messages.validationFailed') || 'Validation failed'}:\n${errorData.details.join('\n')}`;
          toast.error(errorMessage);
        } else {
          toast.error(errorData.error || t('events.workflow.messages.reportSubmitFailed') || 'Failed to submit report');
        }
        return;
      }

      toast.success(t('events.workflow.messages.reportSubmitSuccess') || 'Report submitted successfully');
      onStatusUpdate?.();
    } catch (error) {
      console.error('Error submitting report:', error);
      toast.error(t('events.workflow.messages.reportSubmitFailed') || 'Failed to submit report');
    } finally {
      setLoading(null);
    }
  };

  const getNextActions = (): WorkflowAction[] => {
    const actions: WorkflowAction[] = [];

    switch (event.status) {
      case 'new':
        if (canEdit) {
          actions.push({
            label: t('events.workflow.actions.startEvent') || 'Start Event',
            action: () => updateEventStatus(event._id, 'in_progress'),
            variant: 'default',
            icon: Play,
            loading: loading === 'status'
          });
        }
        break;

      case 'in_progress':
        if (canSupervise) {
          actions.push({
            label: t('events.workflow.actions.beginReport') || 'Begin Report',
            action: () => router.push(`/events/${event._id}/report`),
            variant: 'default',
            icon: FileText
          });
        }
        break;

      case 'processing_report':
        if (canSupervise && report) {
          actions.push({
            label: t('events.workflow.actions.submitReport') || 'Submit Report',
            action: () => submitReport(report._id),
            variant: 'default',
            icon: Send,
            loading: loading === 'submit'
          });
        }
        break;

      case 'awaiting_validation':
        if (canValidate && report) {
          actions.push({
            label: t('events.workflow.actions.validateReport') || 'Validate Report',
            action: () => router.push(`/admin/event-reports/${report._id}/validate?return=${encodeURIComponent(`/events/${event._id}`)}`),
            variant: 'default',
            icon: CheckCircle
          });
        }
        break;
    }

    return actions;
  };

  const getWorkflowProgress = () => {
    const config = statusConfig[event.status];
    return config ? config.progress : 0;
  };

  const getStatusBadge = () => {
    const config = statusConfig[event.status];
    if (!config) return null;

    return (
      <Badge variant="secondary" className="flex items-center gap-1">
        <div className={`w-2 h-2 rounded-full ${config.color}`} />
        {config.label}
      </Badge>
    );
  };

  const nextActions = getNextActions();
  const progress = getWorkflowProgress();

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Workflow className="h-5 w-5" />
          {t('events.workflow.title') || 'Workflow Status'}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Current Status */}
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">{t('events.workflow.currentStatus') || 'Current Status:'}</span>
          {getStatusBadge()}
        </div>

        {/* Progress Bar */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span>{t('events.workflow.progress') || 'Progress'}</span>
            <span>{progress}%</span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>

        {/* Report Status */}
        {report && (
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">{t('events.workflow.reportStatusLabel') || 'Report Status:'}</span>
            <Badge variant="outline">
              {t(`events.workflow.reportStatus.${report.status}`) || report.status.charAt(0).toUpperCase() + report.status.slice(1)}
            </Badge>
          </div>
        )}

        {/* Next Actions */}
        {nextActions.length > 0 && (
          <div className="space-y-2">
            <span className="text-sm font-medium">{t('events.workflow.nextActions') || 'Next Actions:'}</span>
            <div className="flex flex-col gap-2">
              {nextActions.map((action, index) => {
                const Icon = action.icon;
                return (
                  <Button
                    key={index}
                    variant={action.variant}
                    onClick={action.action}
                    disabled={action.loading}
                    size="sm"
                    className="justify-start"
                  >
                    {action.loading ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                    ) : (
                      <Icon className="h-4 w-4 mr-2" />
                    )}
                    {action.label}
                  </Button>
                );
              })}
            </div>
          </div>
        )}

        {/* Status Information */}
        {event.status === 'awaiting_validation' && !canValidate && (
          <div className="flex items-center gap-2 p-2 bg-orange-50 rounded-md">
            <Clock className="h-4 w-4 text-orange-600" />
            <span className="text-sm text-orange-700">
              {t('events.workflow.messages.waitingValidation') || 'Waiting for validation by authorized personnel'}
            </span>
          </div>
        )}

        {event.status === 'cancelled' && (
          <div className="flex items-center gap-2 p-2 bg-red-50 rounded-md">
            <AlertTriangle className="h-4 w-4 text-red-600" />
            <span className="text-sm text-red-700">
              {t('events.workflow.messages.eventCancelled') || 'This event has been cancelled'}
            </span>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
