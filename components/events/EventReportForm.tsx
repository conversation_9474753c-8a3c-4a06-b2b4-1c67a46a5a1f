'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import {
  Clock,
  Users,
  Save,
  Send,
  User,
  X,
  ChefHat,
  Plus,
  Search,
  ChevronDown,
  ChevronRight,
  AlertTriangle
} from 'lucide-react';
import { toast } from 'sonner';
import { formatInTimeZone, fromZonedTime } from 'date-fns-tz';
import { TimeRangeSlider } from '@/components/ui/time-range-slider';
import { DateTimePicker } from '@/app/events/components/DateTimePicker';
import { useLanguage } from '@/lib/contexts/language-context';

interface IEvent {
  _id: string;
  name: string;
  startDate: string;
  endDate: string;
  location: string;
  supervisors: Array<{
    _id: string;
    name: string;
    email: string;
  }>;
}

interface IEventReport {
  _id: string;
  status: 'pending' | 'processing' | 'submitted' | 'validated';
  eventStartTime?: string;
  eventEndTime?: string;
  personnelData?: {
    supervisors: Array<{
      userId: string;
      name: string;
      email: string;
    }>;
    paps: Array<{
      userId: string;
      name: string;
      email: string;
      timeRange: {
        startTime: string;
        endTime: string;
      };
    }>;
    cooks: Array<{
      userId: string;
      name: string;
      email: string;
      timeRange: {
        startTime: string;
        endTime: string;
      };
      percentage: number;
    }>;
  };
  notes?: string;
}

interface EventReportFormProps {
  event: IEvent;
  report: IEventReport;
  canEdit: boolean;
  onSave: (data: Partial<IEventReport>) => Promise<void>;
  onSubmit: () => Promise<void>;
}

export function EventReportForm({ event, report, canEdit, onSave, onSubmit }: EventReportFormProps) {
  // Get user's timezone
  const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
  const { t } = useLanguage();

  const [formData, setFormData] = useState({
    eventStartTime: report?.eventStartTime || event?.startDate,
    eventEndTime: report?.eventEndTime || event?.endDate,
    paps: report?.personnelData?.paps || [],
    cooks: report?.personnelData?.cooks || [],
    notes: report?.notes || ''
  });

  const [saving, setSaving] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  // Available users state
  const [availablePAPs, setAvailablePAPs] = useState<Array<{_id: string, name: string, email: string}>>([]);
  const [availableCooks, setAvailableCooks] = useState<Array<{_id: string, name: string, email: string}>>([]);
  const [papSearch, setPapSearch] = useState('');
  const [cookSearch, setCookSearch] = useState('');

  // Expanded state for time details
  const [expandedPaps, setExpandedPaps] = useState<Set<string>>(new Set());
  const [expandedCooks, setExpandedCooks] = useState<Set<string>>(new Set());

  // Fetch available users
  useEffect(() => {
    fetchAvailableUsers();
  }, []);

  const fetchAvailableUsers = async () => {
    try {
      // Fetch PAPs and Cooks using the new events/users route
      const [papsResponse, cooksResponse] = await Promise.all([
        fetch('/api/events/users?role=pap'),
        fetch('/api/events/users?role=cook')
      ]);

      if (!papsResponse.ok || !cooksResponse.ok) {
        throw new Error('Failed to fetch users');
      }

      const [papsData, cooksData] = await Promise.all([
        papsResponse.json(),
        cooksResponse.json()
      ]);

      setAvailablePAPs(papsData.users || []);
      setAvailableCooks(cooksData.users || []);
    } catch (error) {
      console.error('Error fetching available users:', error);
    }
  };

  // Remove personnel functions
  const removePap = (userId: string) => {
    const updatedPaps = formData.paps.filter(pap => pap.userId !== userId);
    setFormData({ ...formData, paps: updatedPaps });
    toast.success(t('events.reports.form.papRemoved'));
  };

  const removeCook = (userId: string) => {
    const updatedCooks = formData.cooks.filter(cook => cook.userId !== userId);

    // Redistribute percentages equally among remaining cooks
    if (updatedCooks.length > 0) {
      const equalPercentage = Math.round(100 / updatedCooks.length);
      const redistributedCooks = updatedCooks.map((cook, index) => ({
        ...cook,
        percentage: index === updatedCooks.length - 1
          ? 100 - (equalPercentage * (updatedCooks.length - 1)) // Last cook gets remainder
          : equalPercentage
      }));
      setFormData({ ...formData, cooks: redistributedCooks });
    } else {
      setFormData({ ...formData, cooks: updatedCooks });
    }

    toast.success(t('events.reports.form.cookRemoved'));
  };

  // Add personnel functions
  const addPap = (user: {_id: string, name: string, email: string}) => {
    const newPap = {
      userId: user._id,
      name: user.name,
      email: user.email,
      timeRange: {
        startTime: formData.eventStartTime || '',
        endTime: formData.eventEndTime || ''
      }
    };

    setFormData({
      ...formData,
      paps: [...formData.paps, newPap]
    });
    toast.success(t('events.reports.form.papAdded'));
  };

  const addCook = (user: {_id: string, name: string, email: string}) => {
    const currentCooks = formData.cooks;
    const newCookCount = currentCooks.length + 1;
    const equalPercentage = Math.round(100 / newCookCount);

    // Redistribute percentages for existing cooks
    const redistributedCooks = currentCooks.map((cook, index) => ({
      ...cook,
      percentage: index === currentCooks.length - 1
        ? 100 - (equalPercentage * (newCookCount - 1)) // Last existing cook gets remainder
        : equalPercentage
    }));

    const newCook = {
      userId: user._id,
      name: user.name,
      email: user.email,
      timeRange: {
        startTime: formData.eventStartTime || '',
        endTime: formData.eventEndTime || ''
      },
      percentage: equalPercentage
    };

    setFormData({
      ...formData,
      cooks: [...redistributedCooks, newCook]
    });
    toast.success(t('events.reports.form.cookAdded'));
  };

  // Filter available users (exclude already assigned)
  const getAvailablePAPs = () => {
    return availablePAPs
      .filter(user => !formData.paps.some(pap => pap.userId === user._id))
      .filter(user =>
        user.name.toLowerCase().includes(papSearch.toLowerCase()) ||
        user.email.toLowerCase().includes(papSearch.toLowerCase())
      );
  };

  const getAvailableCooks = () => {
    return availableCooks
      .filter(user => !formData.cooks.some(cook => cook.userId === user._id))
      .filter(user =>
        user.name.toLowerCase().includes(cookSearch.toLowerCase()) ||
        user.email.toLowerCase().includes(cookSearch.toLowerCase())
      );
  };

  // Toggle expanded state functions
  const togglePapExpanded = (userId: string) => {
    setExpandedPaps(prev => {
      const newSet = new Set(prev);
      if (newSet.has(userId)) {
        newSet.delete(userId);
      } else {
        newSet.add(userId);
      }
      return newSet;
    });
  };

  const toggleCookExpanded = (userId: string) => {
    setExpandedCooks(prev => {
      const newSet = new Set(prev);
      if (newSet.has(userId)) {
        newSet.delete(userId);
      } else {
        newSet.add(userId);
      }
      return newSet;
    });
  };

  // Timezone utility functions
  const formatDateTimeLocal = (utcDateString: string) => {
    if (!utcDateString) return '';
    try {
      // Format UTC datetime in user's local timezone for display
      const utcDate = new Date(utcDateString);
      return formatInTimeZone(utcDate, userTimezone, "yyyy-MM-dd'T'HH:mm");
    } catch (error) {
      console.error('Error formatting datetime:', error);
      return '';
    }
  };

  const parseLocalDateTime = (localDateTimeString: string) => {
    if (!localDateTimeString) return '';
    try {
      // Parse local datetime input and convert to UTC for storage
      // The input is already in local time, so we need to interpret it as such
      const localDateTime = localDateTimeString + ':00'; // Add seconds if not present
      const utcDate = fromZonedTime(localDateTime, userTimezone);
      return utcDate.toISOString();
    } catch (error) {
      console.error('Error parsing datetime:', error);
      return '';
    }
  };

  const handleSave = async () => {
    setSaving(true);
    try {
      await onSave(formData);
      toast.success(t('events.reports.edit.reportSavedSuccessfully'));
    } catch (error: any) {
      console.error('Save error:', error);
      if (error?.response?.data?.details) {
        toast.error(t('events.reports.form.validationFailedDetails').replace('{details}', error.response.data.details.join(', ')));
      } else {
        toast.error(t('events.reports.edit.failedSaveReport'));
      }
    } finally {
      setSaving(false);
    }
  };

  const handleSubmit = async () => {
    // Validate before submitting
    if (!validateForm()) {
      toast.error(t('events.reports.form.fixValidationErrors'));
      return;
    }

    setSubmitting(true);
    try {
      // Save the current form data first
      console.log('Saving form data before submit:', formData);
      await onSave(formData);

      // Then submit
      await onSubmit();
      toast.success(t('events.reports.edit.reportSubmittedValidation'));
    } catch (error: any) {
      console.error('Submit error:', error);
      if (error?.response?.data?.details) {
        toast.error(t('events.reports.form.validationFailedDetails').replace('{details}', error.response.data.details.join(', ')));
      } else {
        toast.error(t('events.reports.edit.failedSubmitReport'));
      }
    } finally {
      setSubmitting(false);
    }
  };



  // Validation functions
  const validateEventTimes = () => {
    const errors: string[] = [];

    if (!formData.eventStartTime || !formData.eventEndTime) {
      errors.push('Event start and end times are required');
      return errors;
    }

    const startTime = new Date(formData.eventStartTime);
    const endTime = new Date(formData.eventEndTime);

    if (startTime >= endTime) {
      errors.push('Event start time must be before end time');
    }

    return errors;
  };

  const validatePersonnelTimes = () => {
    const errors: string[] = [];

    if (!formData.eventStartTime || !formData.eventEndTime) {
      return errors; // Can't validate personnel times without event times
    }

    const eventStart = new Date(formData.eventStartTime);
    const eventEnd = new Date(formData.eventEndTime);

    // Validate PAPs
    formData.paps.forEach((pap) => {
      if (!pap.timeRange.startTime || !pap.timeRange.endTime) {
        errors.push(`${pap.name} is missing time range`);
        return;
      }

      const papStart = new Date(pap.timeRange.startTime);
      const papEnd = new Date(pap.timeRange.endTime);

      if (papStart >= papEnd) {
        errors.push(`${pap.name}'s start time must be before end time`);
      }

      if (papStart < eventStart) {
        errors.push(`${pap.name}'s start time cannot be before event start time`);
      }

      if (papEnd > eventEnd) {
        errors.push(`${pap.name}'s end time cannot be after event end time`);
      }
    });

    // Validate Cooks
    formData.cooks.forEach((cook) => {
      if (!cook.timeRange.startTime || !cook.timeRange.endTime) {
        errors.push(`${cook.name} is missing time range`);
        return;
      }

      const cookStart = new Date(cook.timeRange.startTime);
      const cookEnd = new Date(cook.timeRange.endTime);

      if (cookStart >= cookEnd) {
        errors.push(`${cook.name}'s start time must be before end time`);
      }

      if (cookStart < eventStart) {
        errors.push(`${cook.name}'s start time cannot be before event start time`);
      }

      if (cookEnd > eventEnd) {
        errors.push(`${cook.name}'s end time cannot be after event end time`);
      }
    });

    return errors;
  };

  const validateForm = () => {
    const eventTimeErrors = validateEventTimes();
    const personnelTimeErrors = validatePersonnelTimes();
    const allErrors = [...eventTimeErrors, ...personnelTimeErrors];

    setValidationErrors(allErrors);
    return allErrors.length === 0;
  };

  // Validate on form data changes
  useEffect(() => {
    validateForm();
  }, [formData]);

  // Helper function to check if event time inputs have errors
  const hasEventTimeError = (field: 'start' | 'end') => {
    return validationErrors.some(error =>
      error.includes('Event') && error.includes(field === 'start' ? 'start' : 'end')
    );
  };

  return (
    <div className="space-y-6">
      {/* Validation Errors */}
      {validationErrors.length > 0 && (
        <Card className="border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="text-red-800 text-sm">Validation Errors</CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="text-sm text-red-700 space-y-1">
              {validationErrors.map((error, index) => (
                <li key={index} className="flex items-start gap-2">
                  <span className="text-red-500 mt-0.5">•</span>
                  {error}
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      )}
      {/* Event Times */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            {t('events.reports.form.eventTimes')}
          </CardTitle>
          <p className="text-sm text-muted-foreground">
            {t('events.reports.form.eventTimesDescription').replace('{timezone}', userTimezone)}
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <Label className="text-base font-medium">{t('events.reports.form.startDateTime')}</Label>
              <div className="grid grid-cols-2 gap-3">
                <div className="space-y-2">
                  <Label htmlFor="startDate" className="text-sm">{t('events.reports.form.date')}</Label>
                  <DateTimePicker
                    type="date"
                    value={formData.eventStartTime ? formatDateTimeLocal(formData.eventStartTime).split('T')[0] : ''}
                    onChange={(value) => {
                      const currentDateTime = formatDateTimeLocal(formData.eventStartTime);
                      const time = currentDateTime ? currentDateTime.split('T')[1] : '09:00';
                      const newDateTime = value ? `${value}T${time}` : '';
                      setFormData({
                        ...formData,
                        eventStartTime: newDateTime ? parseLocalDateTime(newDateTime) : ''
                      });
                    }}
                    placeholder={t('events.reports.form.selectDate')}
                    error={hasEventTimeError('start')}
                    disabled={!canEdit}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="startTime" className="text-sm">{t('events.reports.form.time')}</Label>
                  <DateTimePicker
                    type="time"
                    value={formData.eventStartTime ? formatDateTimeLocal(formData.eventStartTime).split('T')[1] : ''}
                    onChange={(value) => {
                      const currentDateTime = formatDateTimeLocal(formData.eventStartTime);
                      const date = currentDateTime ? currentDateTime.split('T')[0] : '';
                      if (date && value) {
                        const newDateTime = `${date}T${value}`;
                        setFormData({
                          ...formData,
                          eventStartTime: parseLocalDateTime(newDateTime)
                        });
                      }
                    }}
                    placeholder={t('events.reports.form.selectTime')}
                    error={hasEventTimeError('start')}
                    disabled={!canEdit}
                  />
                </div>
              </div>
              {hasEventTimeError('start') && (
                <p className="text-sm text-red-500 flex items-center gap-1">
                  <AlertTriangle className="h-3 w-3" />
                  {t('events.reports.form.startTimeInvalid')}
                </p>
              )}
              <p className="text-xs text-muted-foreground">
                {t('events.reports.form.timezoneNote').replace('{timezone}', userTimezone)}
              </p>
            </div>

            <div className="space-y-4">
              <Label className="text-base font-medium">{t('events.reports.form.endDateTime')}</Label>
              <div className="grid grid-cols-2 gap-3">
                <div className="space-y-2">
                  <Label htmlFor="endDate" className="text-sm">{t('events.reports.form.date')}</Label>
                  <DateTimePicker
                    type="date"
                    value={formData.eventEndTime ? formatDateTimeLocal(formData.eventEndTime).split('T')[0] : ''}
                    onChange={(value) => {
                      const currentDateTime = formatDateTimeLocal(formData.eventEndTime);
                      const time = currentDateTime ? currentDateTime.split('T')[1] : '17:00';
                      const newDateTime = value ? `${value}T${time}` : '';
                      setFormData({
                        ...formData,
                        eventEndTime: newDateTime ? parseLocalDateTime(newDateTime) : ''
                      });
                    }}
                    placeholder={t('events.reports.form.selectDate')}
                    error={hasEventTimeError('end')}
                    disabled={!canEdit}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="endTime" className="text-sm">{t('events.reports.form.time')}</Label>
                  <DateTimePicker
                    type="time"
                    value={formData.eventEndTime ? formatDateTimeLocal(formData.eventEndTime).split('T')[1] : ''}
                    onChange={(value) => {
                      const currentDateTime = formatDateTimeLocal(formData.eventEndTime);
                      const date = currentDateTime ? currentDateTime.split('T')[0] : '';
                      if (date && value) {
                        const newDateTime = `${date}T${value}`;
                        setFormData({
                          ...formData,
                          eventEndTime: parseLocalDateTime(newDateTime)
                        });
                      }
                    }}
                    placeholder={t('events.reports.form.selectTime')}
                    error={hasEventTimeError('end')}
                    disabled={!canEdit}
                  />
                </div>
              </div>
              {hasEventTimeError('end') && (
                <p className="text-sm text-red-500 flex items-center gap-1">
                  <AlertTriangle className="h-3 w-3" />
                  {t('events.reports.form.endTimeInvalid')}
                </p>
              )}
              <p className="text-xs text-muted-foreground">
                {t('events.reports.form.timezoneNote').replace('{timezone}', userTimezone)}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
      
      {/* Supervisors (Read-only) */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            {t('events.reports.form.supervisors')}
          </CardTitle>
          <p className="text-sm text-muted-foreground">
            {t('events.reports.form.supervisorsDescription')}
          </p>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            {event.supervisors?.map(supervisor => (
              <Badge key={supervisor._id} variant="outline">
                {supervisor.name}
              </Badge>
            ))}
          </div>
        </CardContent>
      </Card>
      
      {/* Personnel Management */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            {t('events.reports.form.personnelTimeRanges')}
          </CardTitle>
          <p className="text-sm text-muted-foreground">
            {t('events.reports.form.personnelDescription')}
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* PAPs Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                {t('events.reports.form.papAgents')}
                <Badge variant="secondary">{formData.paps.length}</Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                {/* Assigned PAPs */}
                <div className="space-y-2">
                  <h4 className="text-sm font-medium text-muted-foreground">{t('events.reports.form.assigned')}</h4>
                  <div className="space-y-3 min-h-[200px] h-[300px] overflow-y-auto border rounded-md p-3 bg-gray-50">
                    {formData.paps.length > 0 ? (
                      formData.paps.map((pap, index) => {
                        const isExpanded = expandedPaps.has(pap.userId);
                        return (
                          <div key={pap.userId} className="p-2 border rounded-lg bg-blue-50 border-blue-200">
                            <div className="flex items-center justify-between">
                              <div className="flex-1 min-w-0">
                                <p className="font-medium text-sm truncate">{pap.name}</p>
                                <p className="text-xs text-muted-foreground truncate">{pap.email}</p>
                              </div>
                              <div className="flex items-center gap-1">
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => togglePapExpanded(pap.userId)}
                                  className="h-6 w-6 p-0"
                                >
                                  {isExpanded ? (
                                    <ChevronDown className="h-3 w-3" />
                                  ) : (
                                    <ChevronRight className="h-3 w-3" />
                                  )}
                                </Button>
                                {canEdit && (
                                  <Button
                                    size="sm"
                                    variant="ghost"
                                    onClick={() => removePap(pap.userId)}
                                    className="h-6 w-6 p-0 text-destructive hover:text-destructive hover:bg-destructive/10"
                                  >
                                    <X className="h-3 w-3" />
                                  </Button>
                                )}
                              </div>
                            </div>
                            {isExpanded && (
                              <div className="mt-2 pt-2 border-t border-blue-200">
                                <TimeRangeSlider
                                  eventStartTime={formData.eventStartTime}
                                  eventEndTime={formData.eventEndTime}
                                  startTime={pap.timeRange.startTime}
                                  endTime={pap.timeRange.endTime}
                                  onChange={(startTime, endTime) => {
                                    const newPaps = [...formData.paps];
                                    newPaps[index].timeRange.startTime = startTime;
                                    newPaps[index].timeRange.endTime = endTime;
                                    setFormData({ ...formData, paps: newPaps });
                                  }}
                                  disabled={!canEdit}
                                  personName={pap.name}
                                />
                              </div>
                            )}
                          </div>
                        );
                      })
                    ) : (
                      <div className="flex items-center justify-center h-full">
                        <p className="text-muted-foreground text-sm">
                          {t('events.reports.form.noPapsAssigned')}
                        </p>
                      </div>
                    )}
                  </div>
                </div>

                {/* Available PAPs */}
                {canEdit && (
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium text-muted-foreground">{t('events.reports.form.available')}</h4>
                    <div className="relative">
                      <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder={t('events.reports.form.searchPaps')}
                        value={papSearch}
                        onChange={(e) => setPapSearch(e.target.value)}
                        className="pl-8"
                      />
                    </div>
                    <div className="space-y-2 min-h-[200px] h-[300px] overflow-y-auto border rounded-md p-2 bg-white">
                      {getAvailablePAPs().length > 0 ? (
                        getAvailablePAPs().map((pap) => (
                          <div
                            key={pap._id}
                            className="flex items-center justify-between p-2 border rounded hover:bg-muted cursor-pointer"
                            onClick={() => addPap(pap)}
                          >
                            <div className="flex-1 min-w-0">
                              <p className="font-medium text-sm truncate">{pap.name}</p>
                              <p className="text-xs text-muted-foreground truncate">{pap.email}</p>
                            </div>
                            <Button size="sm" variant="ghost" className="h-6 w-6 p-0">
                              <Plus className="h-3 w-3" />
                            </Button>
                          </div>
                        ))
                      ) : (
                        <div className="flex items-center justify-center h-full">
                          <p className="text-muted-foreground text-sm">
                            {papSearch.trim() ? t('events.reports.form.noPapsFound') : t('events.reports.form.noPapsAssigned')}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Cooks Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <ChefHat className="h-5 w-5" />
                {t('events.reports.form.cooks')}
                <Badge variant="secondary">{formData.cooks.length}</Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                {/* Assigned Cooks */}
                <div className="space-y-2">
                  <h4 className="text-sm font-medium text-muted-foreground">{t('events.reports.form.assigned')}</h4>
                  <div className="space-y-3 min-h-[200px] h-[300px] overflow-y-auto border rounded-md p-3 bg-gray-50">
                    {formData.cooks.length > 0 ? (
                      formData.cooks.map((cook, index) => {
                        const isExpanded = expandedCooks.has(cook.userId);
                        return (
                          <div key={cook.userId} className="p-2 border rounded-lg bg-orange-50 border-orange-200">
                            <div className="flex items-center justify-between">
                              <div className="flex-1 min-w-0">
                                <p className="font-medium text-sm truncate">{cook.name}</p>
                                <p className="text-xs text-muted-foreground truncate">{cook.email}</p>
                              </div>
                              <div className="flex items-center gap-1">
                                <div className="text-right">
                                  <p className="text-xs font-medium">{cook.percentage}%</p>
                                </div>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => toggleCookExpanded(cook.userId)}
                                  className="h-6 w-6 p-0"
                                >
                                  {isExpanded ? (
                                    <ChevronDown className="h-3 w-3" />
                                  ) : (
                                    <ChevronRight className="h-3 w-3" />
                                  )}
                                </Button>
                                {canEdit && (
                                  <Button
                                    size="sm"
                                    variant="ghost"
                                    onClick={() => removeCook(cook.userId)}
                                    className="h-6 w-6 p-0 text-destructive hover:text-destructive hover:bg-destructive/10"
                                  >
                                    <X className="h-3 w-3" />
                                  </Button>
                                )}
                              </div>
                            </div>
                            {isExpanded && (
                              <div className="mt-2 pt-2 border-t border-orange-200">
                                <TimeRangeSlider
                                  eventStartTime={formData.eventStartTime}
                                  eventEndTime={formData.eventEndTime}
                                  startTime={cook.timeRange.startTime}
                                  endTime={cook.timeRange.endTime}
                                  onChange={(startTime, endTime) => {
                                    const newCooks = [...formData.cooks];
                                    newCooks[index].timeRange.startTime = startTime;
                                    newCooks[index].timeRange.endTime = endTime;
                                    setFormData({ ...formData, cooks: newCooks });
                                  }}
                                  disabled={!canEdit}
                                  personName={cook.name}
                                />
                              </div>
                            )}
                          </div>
                        );
                      })
                    ) : (
                      <div className="flex items-center justify-center h-full">
                        <p className="text-muted-foreground text-sm">
                          {t('events.reports.form.noCooksAssigned')}
                        </p>
                      </div>
                    )}
                  </div>
                </div>

                {/* Available Cooks */}
                {canEdit && (
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium text-muted-foreground">{t('events.reports.form.available')}</h4>
                    <div className="relative">
                      <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder={t('events.reports.form.searchCooks')}
                        value={cookSearch}
                        onChange={(e) => setCookSearch(e.target.value)}
                        className="pl-8"
                      />
                    </div>
                    <div className="space-y-2 min-h-[200px] h-[300px] overflow-y-auto border rounded-md p-2 bg-white">
                      {getAvailableCooks().length > 0 ? (
                        getAvailableCooks().map((cook) => (
                          <div
                            key={cook._id}
                            className="flex items-center justify-between p-2 border rounded hover:bg-muted cursor-pointer"
                            onClick={() => addCook(cook)}
                          >
                            <div className="flex-1 min-w-0">
                              <p className="font-medium text-sm truncate">{cook.name}</p>
                              <p className="text-xs text-muted-foreground truncate">{cook.email}</p>
                            </div>
                            <Button size="sm" variant="ghost" className="h-6 w-6 p-0">
                              <Plus className="h-3 w-3" />
                            </Button>
                          </div>
                        ))
                      ) : (
                        <div className="flex items-center justify-center h-full">
                          <p className="text-muted-foreground text-sm">
                            {cookSearch.trim() ? t('events.reports.form.noCooksFound') : t('events.reports.form.noCooksAssigned')}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </CardContent>
      </Card>

      {/* Notes */}
      <Card>
        <CardHeader>
          <CardTitle>{t('events.reports.form.notes')}</CardTitle>
        </CardHeader>
        <CardContent>
          <Textarea
            placeholder={t('events.reports.form.notesPlaceholder')}
            value={formData.notes}
            onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
            disabled={!canEdit}
            rows={4}
          />
        </CardContent>
      </Card>
      
      {/* Action Buttons */}
      {canEdit && (
        <div className="flex justify-end gap-2">
          <Button
            variant="outline"
            onClick={handleSave}
            disabled={saving || submitting}
          >
            <Save className="h-4 w-4 mr-2" />
            {saving ? t('events.reports.form.saving') : t('events.reports.form.save')}
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={saving || submitting || report?.status === 'submitted' || validationErrors.length > 0}
          >
            <Send className="h-4 w-4 mr-2" />
            {submitting ? t('events.reports.form.submitting') : t('events.reports.form.submitForValidation')}
          </Button>
        </div>
      )}
    </div>
  );
}
