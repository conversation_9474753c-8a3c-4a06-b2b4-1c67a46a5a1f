'use client';

import React from 'react';
import { usePathname } from 'next/navigation';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { useEventContext } from '@/lib/contexts/event-context';
import { useLanguage } from '@/lib/contexts/language-context';
import {
  Calendar,
  CalendarDays,
  Plus,
  FileText,
  Edit,
  CheckCircle,
  Home
} from 'lucide-react';

interface BreadcrumbItem {
  label: string;
  href: string;
  icon?: React.ComponentType<{ className?: string }>;
}

export function EventsBreadcrumb() {
  const pathname = usePathname();
  const { currentEvent, currentReport } = useEventContext();
  const { t } = useLanguage();

  const generateBreadcrumbs = (): BreadcrumbItem[] => {
    const segments = pathname.split('/').filter(Boolean);
    const breadcrumbs: BreadcrumbItem[] = [
      { label: t('common.home'), href: '/', icon: Home }
    ];

    // Events section
    if (segments.includes('events')) {
      breadcrumbs.push({ label: t('events.navigation.title'), href: '/events', icon: Calendar });

      if (segments.includes('calendar')) {
        breadcrumbs.push({ label: t('events.navigation.calendarView'), href: '/events/calendar', icon: CalendarDays });
      }

      if (segments.includes('new')) {
        breadcrumbs.push({ label: t('events.navigation.createEvent'), href: '/events/new', icon: Plus });
      }

      // Event detail pages
      if (currentEvent && segments.length >= 2) {
        const eventId = segments[1];
        if (eventId && eventId !== 'calendar' && eventId !== 'new') {
          breadcrumbs.push({
            label: currentEvent.name,
            href: `/events/${currentEvent._id}`,
            icon: Calendar
          });

          if (segments.includes('edit')) {
            breadcrumbs.push({
              label: t('common.edit'),
              href: `/events/${currentEvent._id}/edit`,
              icon: Edit
            });
          }

          if (segments.includes('report')) {
            breadcrumbs.push({
              label: t('events.report'),
              href: `/events/${currentEvent._id}/report`,
              icon: FileText
            });
          }
        }
      }
    }

    // Dashboard section
    if (segments.includes('dashboard') && segments.includes('reports')) {
      breadcrumbs.push({
        label: t('events.navigation.myReports'),
        href: '/events/dashboard/reports',
        icon: FileText
      });
    }

    // Validation section
    if (segments.includes('validation')) {
      breadcrumbs.push({
        label: t('events.navigation.sections.validation'),
        href: '/events/validation',
        icon: CheckCircle
      });

      // Individual validation page
      if (currentReport && segments.length >= 3) {
        const reportId = segments[2];
        if (reportId) {
          breadcrumbs.push({
            label: t('events.navigation.validateReport'),
            href: `/events/validation/${reportId}`,
            icon: CheckCircle
          });
        }
      }
    }

    // Admin section
    if (segments.includes('admin')) {
      breadcrumbs.push({
        label: t('common.admin'),
        href: '/admin',
        icon: CheckCircle
      });

      if (segments.includes('event-reports')) {
        breadcrumbs.push({
          label: t('events.navigation.eventReports'),
          href: '/admin/event-reports',
          icon: FileText
        });

        if (segments.includes('validate') && currentReport) {
          breadcrumbs.push({
            label: t('events.navigation.validateReport'),
            href: `/admin/event-reports/${currentReport._id}/validate`,
            icon: CheckCircle
          });
        }
      }
    }

    return breadcrumbs;
  };

  const breadcrumbs = generateBreadcrumbs();

  if (breadcrumbs.length <= 1) {
    return null;
  }

  return (
    <div className="mb-6">
      <Breadcrumb>
        <BreadcrumbList>
          {breadcrumbs.map((item, index, array) => {
            const Icon = item.icon;
            const isLast = index === array.length - 1;
            
            return (
              <React.Fragment key={item.href}>
                <BreadcrumbItem>
                  {isLast ? (
                    <BreadcrumbPage className="flex items-center gap-1">
                      {Icon && <Icon className="h-4 w-4" />}
                      {item.label}
                    </BreadcrumbPage>
                  ) : (
                    <BreadcrumbLink href={item.href} className="flex items-center gap-1">
                      {Icon && <Icon className="h-4 w-4" />}
                      {item.label}
                    </BreadcrumbLink>
                  )}
                </BreadcrumbItem>
                {index < array.length - 1 && <BreadcrumbSeparator />}
              </React.Fragment>
            );
          })}
        </BreadcrumbList>
      </Breadcrumb>
    </div>
  );
}
