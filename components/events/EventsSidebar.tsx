'use client';

import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Calendar,
  CalendarDays,
  Plus,
  FileText,
  CheckCircle,
  ChevronLeft,
  Menu
} from 'lucide-react';
import {
  canUserViewEvents,
  canUserViewAllEvents,
  canUserCreateEvents,
  canUserSuperviseEvents,
  canUserValidateEventReports,
  canUserViewEventReports
} from '@/lib/utils/permissions-utils';
import { useLanguage } from '@/lib/contexts/language-context';


interface EventsSidebarProps {
  pathname: string;
  permissions: any;
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;
}

interface NavItemProps {
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  children: React.ReactNode;
  active?: boolean;
  badge?: string | number;
  isCollapsed?: boolean;
}

function NavItem({ href, icon: Icon, children, active, badge, isCollapsed }: NavItemProps) {
  return (
    <Link href={href}>
      <Button
        variant={active ? "secondary" : "ghost"}
        className={`w-full gap-2 h-9 ${isCollapsed ? 'justify-center px-2' : 'justify-start'}`}
        size="sm"
        title={isCollapsed ? children as string : undefined}
      >
        <Icon className="h-4 w-4 flex-shrink-0" />
        {!isCollapsed && (
          <>
            <span className="flex-1 text-left">{children}</span>
            {badge && (
              <Badge variant="secondary" className="ml-auto text-xs">
                {badge}
              </Badge>
            )}
          </>
        )}
      </Button>
    </Link>
  );
}

interface NavSectionProps {
  title: string;
  children: React.ReactNode;
  isCollapsed?: boolean;
}

function NavSection({ title, children, isCollapsed }: NavSectionProps) {
  return (
    <div className="space-y-1">
      {!isCollapsed && (
        <h4 className="text-sm font-medium text-muted-foreground px-2 py-1">
          {title}
        </h4>
      )}
      <div className="space-y-1">
        {children}
      </div>
    </div>
  );
}

export function EventsSidebar({ pathname, permissions, isCollapsed = false, onToggleCollapse }: EventsSidebarProps) {
  const { t } = useLanguage();

  if (!permissions) {
    return null;
  }

  const canViewEvents = canUserViewEvents(permissions);
  const canViewAllEvents = canUserViewAllEvents(permissions);
  const canCreateEvents = canUserCreateEvents(permissions);
  const canSuperviseEvents = canUserSuperviseEvents(permissions);
  const canValidateReports = canUserValidateEventReports(permissions);
  const canViewReports = canUserViewEventReports(permissions);



  return (
    <aside className={`${isCollapsed ? 'w-16' : 'w-64'} bg-white border-r border-border min-h-screen transition-all duration-300 ease-in-out`}>
      <div className={`${isCollapsed ? 'p-2' : 'p-4'}`}>
        <div className={`flex items-center mb-6 ${isCollapsed ? 'justify-center' : 'gap-2'}`}>
          {!isCollapsed && <Calendar className="h-6 w-6 text-primary" />}
          {!isCollapsed && <h2 className="text-lg font-semibold">{t('events.navigation.title')}</h2>}
          {isCollapsed && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onToggleCollapse}
              className="h-8 w-8 p-0"
              title={t('events.navigation.expandSidebar')}
            >
              <Menu className="h-4 w-4" />
            </Button>
          )}
        </div>

        {!isCollapsed && onToggleCollapse && (
          <div className="flex justify-end mb-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={onToggleCollapse}
              className="h-8 w-8 p-0"
              title="Collapse sidebar"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
          </div>
        )}

        <nav className={`space-y-6 ${isCollapsed ? 'space-y-2' : ''}`}>
          {/* Events Section - Combined for all users */}
          {canViewEvents && (
            <NavSection title={t('events.navigation.sections.events')} isCollapsed={isCollapsed}>
              {canViewAllEvents && (
                <NavItem
                  href="/events"
                  icon={Calendar}
                  active={pathname === '/events'}
                  isCollapsed={isCollapsed}
                >
                  {t('events.navigation.allEvents')}
                </NavItem>
              )}
              <NavItem
                href="/events/calendar"
                icon={CalendarDays}
                active={pathname.includes('/calendar')}
                isCollapsed={isCollapsed}
              >
                {t('events.navigation.calendarView')}
              </NavItem>
              {canCreateEvents && (
                <NavItem
                  href="/events/new"
                  icon={Plus}
                  active={pathname === '/events/new'}
                  isCollapsed={isCollapsed}
                >
                  {t('events.navigation.createEvent')}
                </NavItem>
              )}
              {canSuperviseEvents  && (
                <NavItem
                  href="/events/dashboard/reports"
                  icon={FileText}
                  active={pathname.includes('/events/dashboard/reports')}
                  isCollapsed={isCollapsed}
                >
                  {t('events.navigation.myReports')}
                </NavItem>
              )}
            </NavSection>
          )}

          {/* Validation Section */}
          {canValidateReports && (
            <NavSection title={t('events.navigation.sections.validation')} isCollapsed={isCollapsed}>
              <NavItem
                href="/events/validation"
                icon={CheckCircle}
                active={pathname.includes('/events/validation')}
                isCollapsed={isCollapsed}
              >
                {t('events.navigation.pendingReports')}
              </NavItem>
            </NavSection>
          )}


          
        </nav>
      </div>
    </aside>
  );
}
