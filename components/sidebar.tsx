'use client';

import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ThemeToggle } from '@/components/theme-toggle';
import {
  LayoutDashboard,
  Users,
  FolderTree,
  Package,
  ShoppingCart,
  Settings,
  Shield,
  UserCog,
  Menu,
  LogOut,
  PanelLeftClose,
  PanelLeftOpen,
  Building,
  Store,
  Key,
  Building2,
  Calendar,
  CalendarDays,
  PersonStanding,
  Handshake,
  Globe,
  CalendarClock,
  CalendarCheck,
  CircleSlash,
  Tag,
  Utensils,
  CalendarRange,
  ListChecks,
  ClipboardList,
  MessageSquare,
  DollarSign,
  CreditCard,
  Receipt,
  Mail,
  Home,
  Truck,
  History,
  Activity,
  Bell,
  FileText,
  Search,
  X,
  PhoneCall
} from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useState, useEffect, createContext, useContext, useMemo } from 'react';
import { useDebounce } from '@/hooks/use-debounce';
import Image from 'next/image';
import { signOut } from 'next-auth/react';
import { PermissionGuard } from '@/components/permission-guard';
import { handleLogout } from '@/lib/utils/auth-utils';
import { UserAccount as UserAccountComponent } from '@/components/user-account';
import { useLanguage } from '@/lib/contexts/language-context';
import en from '@/language/en.json';
import fr from '@/language/fr.json';
import { useSession } from 'next-auth/react';
import { ChevronRight } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { useSubMenu } from '@/components/dynamic-sub-menu/sub-menu-provider';
import { useTheme } from 'next-themes';
import * as RoleUtils from '@/lib/utils/role-utils';
import { AFFECTATION_PERMISSIONS, APPOINTMENT_PERMISSIONS, BILLING_PERMISSIONS, CALENDAR_PERMISSIONS, CONVERSATION_PERMISSIONS, EVENT_PERMISSIONS, PARTNER_PERMISSIONS, REQUEST_PERMISSIONS, RESERVATION_PERMISSIONS, RECONTACT_PERMISSIONS, SMS_PERMISSIONS, USER_PERMISSIONS, CONTACT_REQUEST_PERMISSIONS, CONTACT_PERMISSIONS, DELIVERY_PERMISSIONS, INVITATION_PERMISSIONS, SETTINGS_PERMISSIONS, COLLECTION_HITORY, AUDIT_PERMISSIONS, NOTIFICATION_PERMISSIONS } from '@/types/permission-codes';
import { COMMISSION_PERMISSIONS } from '@/types/commission-codes';
import { usePermissions } from '@/hooks/use-permissions';
import { useAppSelector } from '@/lib/redux/hooks';

// The interface should remain for type checking
interface SidebarItemProps {
  title: string;
  href: string;
  icon: React.ElementType;
  permission: string | undefined;
  customCheck?: (session: any) => boolean;
  children?: Array<{
    title: string;
    href: string;
    icon: React.ElementType;
    permission: string | undefined;
    customCheck?: (session: any) => boolean;
  }>;
}

interface NavigationItem {
  labelKey: string;
  icon: React.ElementType;
  href: string;
  permission: string | undefined;
  customCheck?: (session: any) => boolean;
  children?: Array<{
    labelKey: string;
    icon: React.ElementType;
    href: string;
    permission: string | undefined;
    customCheck?: (session: any) => boolean;
  }>;
}

interface UserAccountProps {
  email: string;
  name: string;
  isCollapsed: boolean;
}

const navigation: NavigationItem[] = [
  {
    labelKey: 'sidebar.home',
    icon: Home,
    href: '/',
    permission: undefined,
    customCheck: undefined // Show to all authenticated users
  },
  {
    labelKey: 'sidebar.adminDashboard',
    icon: LayoutDashboard,
    href: '/admin-dashboard',
    permission: undefined,
    customCheck: (roles) => RoleUtils.hasAdminPrivileges(roles)
  },
  {
    labelKey: 'sidebar.papDashboard',
    icon: LayoutDashboard,
    href: '/pap-dashboard',
    permission: undefined,
    customCheck: (roles) => RoleUtils.isPAPUser(roles)
  },
  {
    labelKey: 'sidebar.sellerDashboard',
    icon: LayoutDashboard,
    href: '/seller-dashboard',
    permission: undefined,
    customCheck: (roles) => RoleUtils.isSeller(roles)
  },
  {
    labelKey: 'sidebar.reservations',
    icon: CalendarRange,
    href: '/reservations',
    permission: RESERVATION_PERMISSIONS.ACCESS_RESERVATIONS,
    customCheck: undefined,
    children: [
      {
        labelKey: 'sidebar.allReservations',
        icon: CalendarRange,
        href: '/reservations',
        permission: RESERVATION_PERMISSIONS.ACCESS_RESERVATIONS,
      },
      {
        labelKey: 'sidebar.recontactReservations',
        icon: PhoneCall,
        href: '/recontact-reservations',
        permission: RECONTACT_PERMISSIONS.ACCESS_RECONTACT_RESERVATIONS,
      }
    ]
  },

  {
    labelKey: 'sidebar.disponibilities',
    icon: CalendarDays,
    href: '/appointments',
    permission: APPOINTMENT_PERMISSIONS.ACCESS_APPOINTMENTS,
  },
  {
    labelKey: 'sidebar.calendars',
    icon: Calendar,
    href: '/calendars',
    permission: CALENDAR_PERMISSIONS.ACCESS_ALL_CALENDARS,
  },
  {
    labelKey: 'sidebar.myCalendar',
    icon: CalendarCheck,
    href: '/mycalendar',
    permission: CALENDAR_PERMISSIONS.ACCESS_OWN_CALENDAR,
  },
  {
    labelKey: 'sidebar.affectations',
    icon: Users,
    href: '/affectations',
    permission: AFFECTATION_PERMISSIONS.ACCESS_AFFECTATIONS,

  },

  {
    labelKey: 'sidebar.conversations',
    icon: MessageSquare,
    href: '/conversations',
    permission: CONVERSATION_PERMISSIONS.ACCESS_CONVERSATIONS,

  },

  {
    labelKey: 'sidebar.invitations',
    icon: Mail,
    href: '/invitations',
    permission: INVITATION_PERMISSIONS.ACCESS_INVITATIONS,
  },

  {
    labelKey: 'sidebar.events',
    icon: CalendarClock,
    href: '/events/calendar',
    permission: EVENT_PERMISSIONS.VIEW_EVENTS,
  },
  {
    labelKey: 'sidebar.requests',
    icon: ListChecks,
    href: '/requests',
    permission: REQUEST_PERMISSIONS.ACCESS_REQUESTS,

  },
  {
    labelKey: 'sidebar.contactRequests',
    icon: ClipboardList,
    href: '/contact-requests',
    permission: CONTACT_REQUEST_PERMISSIONS.ACCESS_CONTACT_REQUESTS,
    customCheck: undefined
  },
  {
    labelKey: 'sidebar.partners',
    icon: Handshake,
    href: '/partners',
    permission: PARTNER_PERMISSIONS.ACCESS_PARTNERS,

  },
  {
    labelKey: 'sidebar.commissions',
    icon: Receipt,
    href: '#',
    permission: COMMISSION_PERMISSIONS.ACCESS_COMMISSIONS,

    children: [
      {
        labelKey: 'sidebar.commissionsList',
        icon: Receipt,
        href: '/commissions',
        permission: COMMISSION_PERMISSIONS.ACCESS_COMMISSIONS,

      },
      {
        labelKey: 'sidebar.userCommissions',
        icon: Users,
        href: '/commissions/users',
        permission: COMMISSION_PERMISSIONS.ACCESS_COMMISSIONS,
      },
      {
        labelKey: 'sidebar.weeklyCommissions',
        icon: CalendarRange,
        href: '/commissions/weekly',
        permission: COMMISSION_PERMISSIONS.ACCESS_COMMISSIONS
      },
    ]
  },
  {
    labelKey: 'sidebar.billing',
    icon: CreditCard,
    href: '/billing',
    permission: BILLING_PERMISSIONS.ACCESS_BILLING,

  },
  {
    labelKey: 'sidebar.auditLogs',
    icon: Activity,
    href: '/audit-logs',
    permission: AUDIT_PERMISSIONS.ACCESS_AUDIT_LOGS,
  },
  {
    labelKey: 'sidebar.notifications',
    icon: Bell,
    href: '/notifications',
    permission: NOTIFICATION_PERMISSIONS.MANAGE_NOTIFICATIONS,
  },

  {
    labelKey: 'sidebar.users',
    icon: Users,
    href: '/users',
    permission: USER_PERMISSIONS.MANAGE_USERS,
    customCheck: (roles) => process.env.NODE_ENV === 'development' && RoleUtils.isSuperAdmin(roles)

  },
  {
    labelKey: 'sidebar.devUsers',
    icon: Users,
    href: '/users/dev',
    permission: USER_PERMISSIONS.MANAGE_USERS,
    children: [
      {
        labelKey: 'sidebar.devUsers',
        icon: Users,
        href: '/users/dev',
        permission: USER_PERMISSIONS.MANAGE_USERS,
      },
      {
        labelKey: 'sidebar.contractManagement',
        icon: FileText,
        href: '/users/dev/contracts',
        permission: USER_PERMISSIONS.MANAGE_USERS,
      }
    ]
  },
  {
    labelKey: 'sidebar.sms',
    icon: MessageSquare,
    href: '/sms',
    permission: SMS_PERMISSIONS.MANAGE_SMS,

  },
  {
    labelKey: 'sidebar.scheduledSms',
    icon: MessageSquare,
    href: '/sms/scheduled',
    permission: SMS_PERMISSIONS.MANAGE_SCHEDULED_SMS,

  },
  {
    labelKey: 'sidebar.contacts',
    icon: Users,
    href: '/contacts',
    permission: CONTACT_PERMISSIONS.ACCESS_CONTACTS,
  },
  {
    labelKey: 'sidebar.deliveryAvailability',
    icon: Truck,
    href: '/delivery-availability',
    permission: DELIVERY_PERMISSIONS.ACCESS_DELIVERY_AVAILABILITY,
  },
  {
    labelKey: 'sidebar.reservationStatuses',
    icon: Tag,
    href: '/reservation-statuses',
    permission: RESERVATION_PERMISSIONS.MANAGE_RESERVATION_STATUSES,

  },
  {
    labelKey: 'sidebar.generalSettings',
    icon: Settings,
    href: '#',
    permission: undefined,
    customCheck: (roles) => {
      return RoleUtils.isSuperAdmin(roles)
    },

    children: [

      {
        labelKey: 'sidebar.roles',
        icon: Package,
        href: '/roles',
        permission: undefined,
        customCheck: (roles) => {
          return RoleUtils.isSuperAdmin(roles);
        }
      },
      {
        labelKey: 'sidebar.permissions',
        icon: FolderTree,
        href: '/permissions',
        permission: undefined,
        customCheck: (roles) => {
          return RoleUtils.isSuperAdmin(roles);
        }
      },
      {
        labelKey: 'sidebar.regions',
        icon: Globe,
        href: '/regions',
        permission: undefined,
        customCheck: (roles) => {
          return RoleUtils.isSuperAdmin(roles);
        }
      },
      {
        labelKey: 'sidebar.branches',
        icon: Building2,
        href: '/branches',
        permission: undefined,
        customCheck: (roles) => {
          return RoleUtils.isSuperAdmin(roles);
        }
      },
      {
        labelKey: 'sidebar.allergies',
        icon: CircleSlash,
        href: '/allergies',
        permission: undefined,
        customCheck: (roles) => {
          return RoleUtils.isSuperAdmin(roles);
        }
      },

      {
        labelKey: 'sidebar.translations',
        icon: Tag,
        href: '/translate',
        permission: undefined,
        customCheck: (roles) => {
          return RoleUtils.isSuperAdmin(roles);
        }
      },
      {
        labelKey: 'sidebar.foods',
        icon: Utensils,
        href: '/foods',
        permission: undefined,
        customCheck: (roles) => {
          return RoleUtils.isSuperAdmin(roles);
        }
      },

      {
        labelKey: 'sidebar.serviceTypes',
        icon: Package,
        href: '/service-types',
        permission: undefined,
        customCheck: (roles) => {
          return RoleUtils.isSuperAdmin(roles);
        }
      },
      {
        labelKey: 'sidebar.eventTypes',
        icon: Tag,
        href: '/event-types',
        permission: undefined,
        customCheck: (roles) => {
          return RoleUtils.isSuperAdmin(roles);
        }
      },
      {
        labelKey: 'sidebar.commissionTypes',
        icon: Tag,
        href: '/commission-types',
        permission: undefined,
        customCheck: (roles) => {
          return RoleUtils.isSuperAdmin(roles);
        }
      },
      {
        labelKey: 'sidebar.brevoTemplates',
        icon: Mail,
        href: '/brevo-templates',
        permission: undefined,
        customCheck: (roles) => {
          return RoleUtils.isSuperAdmin(roles);
        }
      },
      {
        labelKey: 'sidebar.reservationsAccess',
        icon: Key,
        href: '/reservations-access',
        permission: SETTINGS_PERMISSIONS.MANAGE_RESERVATION_SETTINGS,
      },
      {
        labelKey: 'sidebar.recontactStatuses',
        icon: Settings,
        href: '/recontact-statuses',
        permission: RECONTACT_PERMISSIONS.MANAGE_RECONTACT_STATUSES,
      },

    ]
  },
  {
    labelKey: 'sidebar.emailDemo',
    icon: Mail,
    href: '/email-demo',
    permission: undefined,
    customCheck: (roles) => {
      return RoleUtils.hasAdminPrivileges(roles) && process.env.NODE_ENV === 'development';
    }
  },
  {
    labelKey: 'sidebar.reservationsExperimental',
    icon: CalendarRange,
    href: '/reservations-exp',
    permission: undefined,
    customCheck: (roles) => {
      return process.env.NODE_ENV === 'development' || process.env.NEXT_PUBLIC_DEV_SERVER === 'true';
    }
  },
  {
    labelKey: 'sidebar.collectionHistory',
    icon: History,
    href: '/collection-history',
    permission: COLLECTION_HITORY.ACCESS_COLLECTION_HISTORY,

  },
];

function SidebarItem({ title, href, icon: Icon, permission, customCheck, children }: SidebarItemProps) {
  const pathname = usePathname() || '';
  const isActive = pathname === href || pathname.startsWith(`${href}/`);
  const [isExpanded, setIsExpanded] = useState(isActive);
  const isCollapsed = useContext(SidebarContext);
  const roles = useAppSelector(state => state.permissions.roles);

  if (customCheck && !customCheck(roles)) {
    return null;
  }

  const mainItem = (
    <Button
      variant="ghost"
      className={cn(
        "w-full justify-start",
        isActive && "bg-muted",
        isCollapsed && "px-2"
      )}
      onClick={() => children && setIsExpanded(!isExpanded)}
    >
      <Icon className={cn(
        "h-4 w-4",
        !isCollapsed && "mr-2"
      )} />
      {!isCollapsed && title}
      {children && !isCollapsed && (
        <ChevronRight className={cn(
          "ml-auto h-4 w-4 transition-transform",
          isExpanded && "rotate-90"
        )} />
      )}
    </Button>
  );

  return (
    <div>
      {permission ? (
        <PermissionGuard permission={permission}>
          {children ? mainItem : <Link href={href}>{mainItem}</Link>}
        </PermissionGuard>
      ) : (
        children ? mainItem : <Link href={href}>{mainItem}</Link>
      )}

      {children && isExpanded && !isCollapsed && (
        <div className="ml-4 mt-1 space-y-1">
          {children.map((child) => {
            if (child.customCheck && !child.customCheck(roles)) {
              return null;
            }

            return (
              <PermissionGuard
                key={child.href}
                permission={child.permission}
                fallback={null}
              >
                <Link href={child.href}>
                  <Button
                    variant="ghost"
                    className={cn(
                      "w-full justify-start",
                      pathname === child.href && "bg-muted"
                    )}
                  >
                    <child.icon className="mr-2 h-4 w-4" />
                    {child.title}
                  </Button>
                </Link>
              </PermissionGuard>
            );
          })}
        </div>
      )}
    </div>
  );
}

const SidebarContext = createContext(false);

export function UserAccount({ email, name, isCollapsed }: UserAccountProps) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="flex items-center justify-between">
      <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
        <DropdownMenuTrigger asChild>
          <Button
            variant={isCollapsed ? "default" : "ghost"}
            className={cn(
              "w-full justify-start gap-2",
              isCollapsed ? "h-9 w-9 p-0" : "h-9 px-2",
              isOpen && "bg-accent"
            )}
          >
            <Avatar className={cn(
              "h-6 w-6",
              isCollapsed && "h-9 w-9"
            )}>
              <AvatarFallback className={cn(
                "bg-primary text-primary-foreground",
                isOpen && "bg-accent text-accent-foreground"
              )}>
                {name ? name.slice(0, 2).toUpperCase() : email.slice(0, 2).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            {!isCollapsed && (
              <>
                <span className="flex-1 truncate">{name || email}</span>
                <ChevronRight className={cn(
                  "h-4 w-4 shrink-0 transition-transform",
                  isOpen && "rotate-90"
                )} />
              </>
            )}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          align={isCollapsed ? "center" : "start"}
          className="w-[200px]"
          side={isCollapsed ? "right" : "top"}
          sideOffset={isCollapsed ? 8 : 0}
        >
          <DropdownMenuItem
            className="cursor-pointer"
            onClick={() => handleLogout('/')}
          >
            <LogOut className="mr-2 h-4 w-4" />
            Log out
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {!isCollapsed && (
        <Button
          variant="ghost"
          size="icon"
          onClick={() => handleLogout('/')}
          title="Logout"
          className="h-8 w-8 ml-2"
        >
          <LogOut className="h-4 w-4" />
        </Button>
      )}
    </div>
  );
}

// Create a client-only ThemeLogo component
function ThemeLogo() {
  const { theme, resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  // Only show the logo once the component has mounted
  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    // Return a placeholder during SSR and initial client render
    return <div className="w-[200px] h-[48px]" />;
  }

  // Use resolvedTheme which correctly reflects the current theme
  const logoSrc = (resolvedTheme === 'dark') ? '/amq-logo-dark.png' : '/amq-logo.png';

  return (
    <Image
      src={logoSrc}
      alt="AMQ Logo"
      width={200}
      height={48}
      className="flex-shrink-0 pl-[50px]"
    />
  );
}

export function Sidebar() {
  const pathname = usePathname();
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const debouncedSearchQuery = useDebounce(searchQuery, 300);
  const { t } = useLanguage();
  const { menuItems } = useSubMenu();
  const { theme } = useTheme();
  const { isLoading: permissionsLoading, error: permissionsError, refreshPermissions } = usePermissions();
  const permissions = useAppSelector(state => state.permissions);
  const roles = permissions.roles;
  const { data: session } = useSession();

  useEffect(() => {
    const saved = localStorage.getItem('sidebarCollapsed');
    if (saved !== null) {
      setIsCollapsed(JSON.parse(saved));
    }
  }, []);

  // Normalize text for search (remove accents and convert to lowercase)
  const normalizeText = (text: string): string => {
    return text
      .toLowerCase()
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, ''); // Remove diacritics/accents
  };

  // Get translation in both languages for multilingual search
  const getTranslationInBothLanguages = (key: string): string[] => {
    const keys = key.split('.');

    const getNestedValue = (obj: any, keyPath: string[]): string => {
      let value: any = obj;
      for (const k of keyPath) {
        if (value && typeof value === 'object' && k in value) {
          value = value[k];
        } else {
          return '';
        }
      }
      return typeof value === 'string' ? value : '';
    };

    const enValue = getNestedValue(en, keys);
    const frValue = getNestedValue(fr, keys);

    return [enValue, frValue].filter(Boolean);
  };

  // Check if search query matches any translation of the item
  const itemMatchesSearch = (labelKey: string, normalizedQuery: string): boolean => {
    const translations = getTranslationInBothLanguages(labelKey);
    return translations.some(translation =>
      normalizeText(translation).includes(normalizedQuery)
    );
  };

  // Filter navigation items based on search query
  const filteredNavigation = useMemo(() => {
    if (!debouncedSearchQuery.trim()) {
      return navigation;
    }

    const normalizedQuery = normalizeText(debouncedSearchQuery);

    return navigation.filter((item) => {
      // Check if main item matches in any language
      const mainItemMatches = itemMatchesSearch(item.labelKey, normalizedQuery);

      // Check if any child item matches in any language
      const childMatches = item.children?.some(child =>
        itemMatchesSearch(child.labelKey, normalizedQuery)
      );

      return mainItemMatches || childMatches;
    }).map((item) => {
      // If main item matches, return as is
      const mainItemMatches = itemMatchesSearch(item.labelKey, normalizedQuery);
      if (mainItemMatches) {
        return item;
      }

      // If only children match, filter children to show only matching ones
      if (item.children) {
        return {
          ...item,
          children: item.children.filter(child =>
            itemMatchesSearch(child.labelKey, normalizedQuery)
          )
        };
      }

      return item;
    });
  }, [debouncedSearchQuery]);

  const handleCollapse = (collapsed: boolean) => {
    setIsCollapsed(collapsed);
    localStorage.setItem('sidebarCollapsed', JSON.stringify(collapsed));
  };

  // Loading state when fetching permissions
  if (permissionsLoading) {
    return (
      <div className="flex items-center justify-center h-full w-full">
        <span>Loading permissions...</span>
      </div>
    );
  }

  // Error state for permission loading failures
  if (permissionsError) {
    return (
      <div className="flex flex-col items-center justify-center h-full w-full">
        <span className="text-red-500 mb-2">Failed to load permissions</span>
        <Button
          variant="outline"
          onClick={() => refreshPermissions()}
          className="mt-2"
        >
          Retry
        </Button>
      </div>
    );
  }

  return (
    <>
      {/* Debug panel for permissions - only shown in development mode */}
      {false && process.env.NODE_ENV === 'development' && (
        <div className="fixed bottom-4 right-4 z-50 bg-black bg-opacity-80 text-white p-4 rounded-md max-w-[300px] text-xs overflow-y-auto max-h-[200px]">
          <p className="font-bold mb-1">Permissions Debug ({permissions.permissions.length})</p>
          <div className="mb-2">
            <span>Permissions: </span>
            {permissions.permissions.slice(0, 5).map((p) => (
              <span key={p} className="inline-block bg-blue-800 px-1 mr-1 mb-1 rounded">{p}</span>
            ))}
            {permissions.permissions.length > 5 && '...'}
          </div>
          <div>
            <span>Roles: </span>
            {roles.map((r) => (
              <span key={r._id} className="inline-block bg-green-800 px-1 mr-1 mb-1 rounded">{r.name}</span>
            ))}
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => refreshPermissions()}
            className="mt-2 text-xs w-full"
          >
            Refresh Permissions
          </Button>
        </div>
      )}

      <Button
        variant="ghost"
        size="icon"
        className="absolute top-4 left-4 z-50 md:hidden"
        onClick={() => setIsOpen(!isOpen)}
      >
        <Menu className="h-6 w-6" />
      </Button>
      <div
        className={cn(
          'fixed inset-y-0 left-0 z-40 flex flex-col bg-card transition-all duration-200 ease-in-out md:relative md:translate-x-0',
          {
            '-translate-x-full': !isOpen,
            'w-72': !isCollapsed,
            'w-[60px]': isCollapsed
          }
        )}
      >
        <div className="flex h-16 items-center gap-2 border-b px-6 justify-between">
          {!isCollapsed && (
            <div className="flex items-center gap-3">
              <ThemeLogo />
            </div>
          )}
          <Button
            variant="ghost"
            size="icon"
            className="hidden md:flex"
            onClick={() => handleCollapse(!isCollapsed)}
          >
            {isCollapsed ? (
              <PanelLeftOpen className="h-4 w-4" />
            ) : (
              <PanelLeftClose className="h-4 w-4" />
            )}
          </Button>
        </div>

        {/* Search Bar */}
        {!isCollapsed && (
          <div className="px-4 py-2 border-b">
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder={t('sidebar.searchPlaceholder') || 'Search menu...'}
                className="w-full pl-8 pr-8 h-9"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                aria-label={t('common.search') || 'Search'}
              />
              {searchQuery && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-9 w-9 p-0"
                  onClick={() => setSearchQuery('')}
                  type="button"
                  aria-label={t('common.clear') || 'Clear'}
                >
                  <X className="h-4 w-4" />
                  <span className="sr-only">{t('common.clear') || 'Clear'}</span>
                </Button>
              )}
            </div>
          </div>
        )}

        <SidebarContext.Provider value={isCollapsed}>
          <div className={cn(
            "flex-1 overflow-y-auto",
            isCollapsed ? "p-2" : "p-4"
          )}>
            <nav className="flex flex-col gap-1">
              {filteredNavigation.map((item) => {
                //console.log('Rendering item:', item.labelKey, 'Permission:', item.permission);

                return (
                  <SidebarItem
                    key={item.labelKey}
                    title={t(item.labelKey)}
                    href={item.href}
                    icon={item.icon}
                    permission={item.permission}
                    customCheck={item.customCheck}
                    children={item.children?.map(child => {

                      return ({
                        title: t(child.labelKey),
                        href: child.href,
                        icon: child.icon,
                        permission: child.permission,
                        customCheck: child.customCheck
                      });
                    })}
                  />
                );
              })}
            </nav>
          </div>
        </SidebarContext.Provider>
        <div className={cn(
          "border-t p-4",
          isCollapsed && "p-2"
        )}>
          <div className="flex items-center justify-between gap-2">
            <UserAccountComponent
              email={session?.user?.email || ''}
              name={session?.user?.name || ''}
              isCollapsed={isCollapsed}
            />
            {!isCollapsed && (
              <Button
                variant="ghost"
                size="icon"
                onClick={() => handleLogout('/')}
                title="Logout"
                className="h-8 w-8"
              >
                <LogOut className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </div>
    </>
  );
}
