'use client';

import { useEffect, useState } from 'react';
import { CalendarGrid } from './calendar-grid';
import { BranchSelector } from './branch-selector';
import { RegionSelector } from './region-selector';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { addDays, format, subDays, startOfWeek } from 'date-fns';
import { fr } from 'date-fns/locale';
import { useLanguage } from '@/lib/contexts/language-context';

interface AvailabilityData {
  date: string;
  hours: {
    [hour: string]: number;
  };
}

// Get Monday of current week
const getInitialDate = () => {
  return startOfWeek(new Date(), { weekStartsOn: 1 }); // 1 represents Monday
};

export default function AvailabilityCalendar() {
  const { t, language } = useLanguage();
  const locale = language === 'fr' ? fr : undefined;
  const [data, setData] = useState<AvailabilityData[]>([]);
  const [startDate, setStartDate] = useState(getInitialDate());
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedBranch, setSelectedBranch] = useState<string | null>(null);
  const [selectedRegion, setSelectedRegion] = useState<string | null>(null);

  const fetchData = async (start: Date, branchId: string | null, regionId: string | null) => {
    setIsLoading(true);
    setError(null);
    try {
      const endDate = addDays(start, 4);
      const params = new URLSearchParams({
        startDate: format(start, 'yyyy-MM-dd'),
        endDate: format(endDate, 'yyyy-MM-dd'),
      });
      
      if (branchId && branchId !== 'all') {
        params.append('branchId', branchId);
      }
      
      if (regionId && regionId !== 'all') {
        params.append('regionId', regionId);
      }

      const response = await fetch(
        `/api/appointmentsdisponibilities/aggregate?${params}`
      );
      
      if (!response.ok) {
        throw new Error('Failed to fetch data');
      }

      const result = await response.json();
      
      if (!Array.isArray(result)) {
        throw new Error('Invalid response format');
      }

      // Ensure we have a full range of dates
      const fullRange: AvailabilityData[] = [];
      let currentDate = start;
      
      while (currentDate <= endDate) {
        const dateStr = format(currentDate, 'yyyy-MM-dd');
        const existing = result.find(d => d.date === dateStr);
        
        fullRange.push({
          date: dateStr,
          hours: existing?.hours || {}
        });
        
        currentDate = addDays(currentDate, 1);
      }

      setData(fullRange);
    } catch (error) {
      console.error('Error fetching availability data:', error);
      setError(error instanceof Error ? error.message : 'Failed to load data');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchData(startDate, selectedBranch, selectedRegion);
  }, [startDate, selectedBranch, selectedRegion]);

  const handlePrevious = () => {
    setStartDate(prev => subDays(prev, 7)); // Move back by a week
  };

  const handleNext = () => {
    setStartDate(prev => addDays(prev, 7)); // Move forward by a week
  };

  const handleToday = () => {
    setStartDate(getInitialDate()); // Reset to Monday of current week
  };

  const handleBranchChange = (branchId: string | null) => {
    setSelectedBranch(branchId);
  };

  const handleRegionChange = (regionId: string | null) => {
    // Reset branch selection when region changes
    setSelectedBranch(null);
    setSelectedRegion(regionId);
  };

  // Find max count across all days and hours
  const maxCount = Math.max(
    1,
    ...data.flatMap(day => Object.values(day.hours))
      .filter(count => typeof count === 'number' && count > 0)
  );

  if (error) {
    return (
      <div className="p-4 text-red-500">
        Error: {error}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex flex-col space-y-4">
        <h2 className="text-lg font-semibold">Staff Availability Overview</h2>
        <div className="flex flex-col gap-4">
          <div className="flex items-center gap-2">
            <RegionSelector onRegionChange={handleRegionChange} />
            <BranchSelector 
              onBranchChange={handleBranchChange} 
              regionId={selectedRegion}
            />
          </div>
          <div className="flex items-center justify-center space-x-2">
            <Button
              variant="outline"
              size="icon"
              onClick={handlePrevious}
              disabled={isLoading}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              onClick={handleToday}
              className="min-w-[100px]"
            >
              {t('common.today')}
            </Button>
            <div className="w-32 text-center text-sm">
              {format(startDate, 'MMM d', { locale })} - {format(addDays(startDate, 4), 'MMM d', { locale })}
            </div>
            <Button
              variant="outline"
              size="icon"
              onClick={handleNext}
              disabled={isLoading}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      <div className="border rounded-lg overflow-x-auto">
        <div className={`
          flex min-w-full divide-x bg-background
          ${isLoading ? 'opacity-50 pointer-events-none' : ''}
        `}>
          {data.map((day) => (
            <div key={day.date} className="flex-1 min-w-[250px]">
              <CalendarGrid
                date={day.date}
                hours={day.hours}
                maxCount={maxCount}
              />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}