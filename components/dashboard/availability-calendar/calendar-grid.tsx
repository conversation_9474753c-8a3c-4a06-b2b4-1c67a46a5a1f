'use client';

import { HourCell } from './hour-cell';
import { useLanguage } from '@/lib/contexts/language-context';

interface CalendarGridProps {
  date: string;
  hours: {
    [hour: string]: number;
  };
  maxCount: number;
}

// Generate all possible hours from 8:00 to 20:00
const HOUR_RANGE = Array.from({ length: 13 }, (_, i) => {
  const hour = i + 8;
  return `${hour}:00`;
});

export function CalendarGrid({ date, hours, maxCount }: CalendarGridProps) {
  const { language } = useLanguage();
  const locale = language === 'fr' ? 'fr-CA' : 'en-US';

  const formattedDate = new Date(date).toLocaleDateString(locale, {
    weekday: 'short',
    month: 'short',
    day: 'numeric',
  });

  // For debugging
  console.log('Grid hours:', hours);

  return (
    <div className="flex flex-col">
      <div className="text-sm font-medium py-2 px-4 border-b bg-muted/50">
        {formattedDate}
      </div>
      <div className="grid grid-cols-1">
        {HOUR_RANGE.map((hour) => (
          <HourCell
            key={hour}
            hour={hour}
            count={hours[hour] || 0}
            maxCount={maxCount}
          />
        ))}
      </div>
    </div>
  );
}