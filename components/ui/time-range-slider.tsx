'use client';

import React from 'react';
import { Slider } from '@/components/ui/slider';
import { Label } from '@/components/ui/label';
import { Clock } from 'lucide-react';
import { format, addMinutes, differenceInMinutes, differenceInDays } from 'date-fns';
import { formatInTimeZone } from 'date-fns-tz';
import { useLanguage } from '@/lib/contexts/language-context';

interface TimeRangeSliderProps {
  eventStartTime: string;
  eventEndTime: string;
  startTime: string;
  endTime: string;
  onChange: (startTime: string, endTime: string) => void;
  disabled?: boolean;
  personName?: string;
  className?: string;
}

export function TimeRangeSlider({
  eventStartTime,
  eventEndTime,
  startTime,
  endTime,
  onChange,
  disabled = false,
  personName,
  className = ''
}: TimeRangeSliderProps) {
  // Get user's timezone
  const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
  const { t } = useLanguage();

  // Validate inputs and provide fallbacks
  if (!eventStartTime || !eventEndTime || !startTime || !endTime) {
    return (
      <div className={`p-4 bg-red-50 border border-red-200 rounded-lg ${className}`}>
        <p className="text-red-600 text-sm">{t('events.reports.form.invalidTimeData')}</p>
      </div>
    );
  }

  // Convert times to minutes from event start for slider values
  const eventStart = new Date(eventStartTime);
  const eventEnd = new Date(eventEndTime);
  const totalEventMinutes = differenceInMinutes(eventEnd, eventStart);

  // Ensure event has valid duration
  if (totalEventMinutes <= 0) {
    return (
      <div className={`p-4 bg-yellow-50 border border-yellow-200 rounded-lg ${className}`}>
        <p className="text-yellow-600 text-sm">{t('events.reports.form.invalidEventDuration')}</p>
      </div>
    );
  }

  // Convert current times to slider values (minutes from event start)
  const currentStart = new Date(startTime);
  const currentEnd = new Date(endTime);
  let startMinutes = differenceInMinutes(currentStart, eventStart);
  let endMinutes = differenceInMinutes(currentEnd, eventStart);

  // Clamp values to valid range
  startMinutes = Math.max(0, Math.min(startMinutes, totalEventMinutes));
  endMinutes = Math.max(startMinutes, Math.min(endMinutes, totalEventMinutes));
  
  // Handle slider value changes
  const handleSliderChange = (values: number[]) => {
    const [newStartMinutes, newEndMinutes] = values;

    // Ensure valid range
    if (newStartMinutes >= newEndMinutes) {
      return; // Don't update if invalid
    }

    // Convert back to actual times
    const newStartTime = addMinutes(eventStart, newStartMinutes);
    const newEndTime = addMinutes(eventStart, newEndMinutes);

    // Validate the times are within event bounds
    if (newStartTime < eventStart || newEndTime > eventEnd) {
      return; // Don't update if out of bounds
    }

    onChange(newStartTime.toISOString(), newEndTime.toISOString());
  };

  // Check if event spans multiple days
  const eventStartDate = new Date(eventStartTime);
  const eventEndDate = new Date(eventEndTime);
  const isMultiDay = eventStartDate.toDateString() !== eventEndDate.toDateString();
  const daySpan = isMultiDay ? differenceInDays(eventEndDate, eventStartDate) + 1 : 1;

  // Format time for display in user's timezone
  const formatTime = (utcDateString: string) => {
    try {
      const utcDate = new Date(utcDateString);
      if (isMultiDay) {
        // For multi-day events, show date and time with explicit space
        const dateStr = formatInTimeZone(utcDate, userTimezone, 'MMM d');
        const timeStr = formatInTimeZone(utcDate, userTimezone, 'HH:mm');
        return `${dateStr} ${timeStr}`;
      } else {
        // For single-day events, show only time
        return formatInTimeZone(utcDate, userTimezone, 'HH:mm');
      }
    } catch (error) {
      console.error('Error formatting time:', error);
      return '--:--';
    }
  };

  // Format time for markers (shorter format)
  const formatMarkerTime = (date: Date) => {
    try {
      if (isMultiDay) {
        const dateStr = formatInTimeZone(date, userTimezone, 'MMM d');
        const timeStr = formatInTimeZone(date, userTimezone, 'HH:mm');
        return `${dateStr} ${timeStr}`;
      } else {
        return formatInTimeZone(date, userTimezone, 'HH:mm');
      }
    } catch (error) {
      console.error('Error formatting marker time:', error);
      return '--:--';
    }
  };

  // Calculate duration
  const duration = differenceInMinutes(new Date(endTime), new Date(startTime));
  const formatDuration = (minutes: number) => {
    const days = Math.floor(minutes / (24 * 60));
    const hours = Math.floor((minutes % (24 * 60)) / 60);
    const mins = minutes % 60;

    if (days > 0) {
      if (hours === 0 && mins === 0) return `${days}d`;
      if (mins === 0) return `${days}d ${hours}h`;
      return `${days}d ${hours}h ${mins}min`;
    }

    if (hours === 0) return `${mins}min`;
    if (mins === 0) return `${hours}h`;
    return `${hours}h ${mins}min`;
  };

  return (
    <div className={`space-y-4 p-4 bg-slate-50 rounded-lg border ${className}`}>
      {/* Header with Person Name and Duration */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Clock className="h-4 w-4 text-primary" />
          <span className="text-sm font-semibold text-slate-700">
            {personName || t('events.reports.form.timeRange')}
          </span>
        </div>
        <div className="px-2 py-1 bg-primary/10 text-primary text-xs font-medium rounded-full">
          {formatDuration(duration)}
        </div>
      </div>

      {/* Selected Time Display */}
      <div className="flex items-center justify-center gap-4 py-2">
        <div className="text-center">
          <div className="text-xs text-muted-foreground mb-1">Start</div>
          <div className={`${isMultiDay ? 'text-sm' : 'text-lg'} font-mono font-bold text-slate-800`}>
            {formatTime(startTime)}
          </div>
        </div>
        <div className="text-muted-foreground">→</div>
        <div className="text-center">
          <div className="text-xs text-muted-foreground mb-1">End</div>
          <div className={`${isMultiDay ? 'text-sm' : 'text-lg'} font-mono font-bold text-slate-800`}>
            {formatTime(endTime)}
          </div>
        </div>
      </div>

      {/* Slider */}
      <div className="px-2 py-4">
        <div className="relative">
          {/* Time markers */}
          <div className={`flex justify-between text-xs text-muted-foreground mb-2 ${isMultiDay ? 'gap-1' : ''}`}>
            {Array.from({ length: isMultiDay ? 3 : 5 }, (_, i) => {
              const totalMarkers = isMultiDay ? 2 : 4;
              const markerMinutes = (totalEventMinutes / totalMarkers) * i;
              const markerTime = addMinutes(eventStart, markerMinutes);
              return (
                <span key={i} className={`text-center ${isMultiDay ? 'text-xs' : ''}`}>
                  {formatMarkerTime(markerTime)}
                </span>
              );
            })}
          </div>

          <Slider
            value={[startMinutes, endMinutes]}
            onValueChange={handleSliderChange}
            min={0}
            max={totalEventMinutes}
            step={15} // 15-minute increments
            disabled={disabled}
            className="w-full"
          />

          {/* Visual indicators */}
          <div className="flex justify-between text-xs text-muted-foreground mt-1">
            <span>0min</span>
            <span>{formatDuration(totalEventMinutes)} total</span>
          </div>
        </div>
      </div>

      {/* Event Time Reference */}
      <div className="bg-white px-3 py-2 rounded border">
        <div className={`flex ${isMultiDay ? 'flex-col gap-2' : 'justify-between'} text-xs text-muted-foreground mb-1`}>
          <div className="flex items-center gap-1">
            <span className="w-2 h-2 bg-green-500 rounded-full"></span>
            <span>Event Start: {formatTime(eventStartTime)}</span>
          </div>
          <div className="flex items-center gap-1">
            <span className="w-2 h-2 bg-red-500 rounded-full"></span>
            <span>Event End: {formatTime(eventEndTime)}</span>
          </div>
        </div>
        <div className="text-center text-xs text-muted-foreground">
          {isMultiDay && (
            <div className="mb-1 text-amber-600 font-medium">
              ⚠️ Multi-day event ({daySpan} days)
            </div>
          )}
          {t('events.reports.form.timezoneNote').replace('{timezone}', userTimezone)}
        </div>
      </div>

      {/* Quick Time Buttons */}
      {!disabled && (
        <div className="flex gap-2 justify-center">
          <button
            type="button"
            onClick={() => onChange(eventStartTime, eventEndTime)}
            className="px-3 py-1 text-xs bg-blue-100 hover:bg-blue-200 text-blue-700 rounded-full transition-colors"
          >
            {t('events.reports.form.fullEvent')}
          </button>
          <button
            type="button"
            onClick={() => {
              const eventStartDate = new Date(eventStartTime);
              const eventEndDate = new Date(eventEndTime);
              const halfDuration = differenceInMinutes(eventEndDate, eventStartDate) / 2;
              const midTime = addMinutes(eventStartDate, halfDuration);
              onChange(eventStartTime, midTime.toISOString());
            }}
            className="px-3 py-1 text-xs bg-green-100 hover:bg-green-200 text-green-700 rounded-full transition-colors"
          >
            {t('events.reports.form.firstHalf')}
          </button>
          <button
            type="button"
            onClick={() => {
              const eventStartDate = new Date(eventStartTime);
              const eventEndDate = new Date(eventEndTime);
              const halfDuration = differenceInMinutes(eventEndDate, eventStartDate) / 2;
              const midTime = addMinutes(eventStartDate, halfDuration);
              onChange(midTime.toISOString(), eventEndTime);
            }}
            className="px-3 py-1 text-xs bg-orange-100 hover:bg-orange-200 text-orange-700 rounded-full transition-colors"
          >
            {t('events.reports.form.secondHalf')}
          </button>
        </div>
      )}
    </div>
  );
}
