import { RESERVATION_STATUS_PERMISSIONS } from './reservation-status-codes';
import { COMMISSION_TYPE_PERMISSIONS } from './commission-type-codes';
import { COMMISSION_PERMISSIONS } from './commission-codes';
import { RECONTACT_PERMISSIONS } from './recontact-permission-codes';

export const USER_PERMISSIONS = {
  VIEW_OWN_BRANCH_USERS: 'VIEW_OWN_BRANCH_USERS',
  VIEW_USERS: 'VIEW_USERS',
  CREATE_USERS: 'CREATE_USERS',
  EDIT_USERS: 'EDIT_USERS',
  DELETE_USERS: 'DELETE_USERS',
  MANAGE_USERS: 'MANAGE_USERS',
  MANAGE_USER_ROLES: 'MANAGE_USER_ROLES',
  VIEW_NAS: 'VIEW_NAS',
} as const;

export const COLLECTION_HITORY = {
  ACCESS_COLLECTION_HISTORY: 'ACCESS_COLLECTION_HISTORY',
}

export const DELIVERY_PERMISSIONS = {
  ACCESS_DELIVERY_AVAILABILITY: 'ACCESS_DELIVERY_AVAILABILITY',

} as const;

export const CONTACT_REQUEST_PERMISSIONS = {
  ACCESS_CONTACT_REQUESTS: 'ACCESS_CONTACT_REQUESTS',
  MANAGE_CONTACT_REQUESTS_SOURCES: 'MANAGE_CONTACT_REQUESTS_SOURCES',
} as const;

export const CONTACT_PERMISSIONS = {
  ACCESS_CONTACTS: 'ACCESS_CONTACTS',
} as const;

export const CUSTOMER_PERMISSIONS = {
  VIEW_CUSTOMER_DETAILS: 'VIEW_CUSTOMER_DETAILS',
  VIEW_CUSTOMER_PHONE: 'VIEW_CUSTOMER_PHONE',
} as const;

export const ROLE_PERMISSIONS = {
  VIEW_ROLES: 'VIEW_ROLES',
  CREATE_ROLES: 'CREATE_ROLES',
  EDIT_ROLES: 'EDIT_ROLES',
  DELETE_ROLES: 'DELETE_ROLES',
  MANAGE_PERMISSIONS: 'MANAGE_PERMISSIONS'
} as const;

export const REGION_PERMISSIONS = {
  VIEW_REGIONS: 'VIEW_REGIONS',
  CREATE_REGIONS: 'CREATE_REGIONS',
  EDIT_REGIONS: 'EDIT_REGIONS',
  DELETE_REGIONS: 'DELETE_REGIONS',
  MANAGE_REGIONS: 'MANAGE_REGIONS',
} as const;

export const EVENT_PERMISSIONS = {
  ACCESS_EVENTS: 'ACCESS_EVENTS',
  VIEW_EVENTS: 'VIEW_EVENTS',
  VIEW_ALL_EVENTS: 'VIEW_ALL_EVENTS',
  CREATE_EVENTS: 'CREATE_EVENTS',
  EDIT_EVENTS: 'EDIT_EVENTS',
  DELETE_EVENTS: 'DELETE_EVENTS',
  MANAGE_EVENTS: 'MANAGE_EVENTS',

  // Event supervision permissions
  SUPERVISE_EVENTS: 'SUPERVISE_EVENTS',
  VIEW_SUPERVISED_EVENTS: 'VIEW_SUPERVISED_EVENTS',
  EDIT_EVENT_REPORTS: 'EDIT_EVENT_REPORTS',
  SUBMIT_EVENT_REPORTS: 'SUBMIT_EVENT_REPORTS',

  // Event report management
  VIEW_EVENT_REPORTS: 'VIEW_EVENT_REPORTS',
  VALIDATE_EVENT_REPORTS: 'VALIDATE_EVENT_REPORTS',
  MANAGE_EVENT_PERSONNEL: 'MANAGE_EVENT_PERSONNEL',

  // Commission permissions
  VIEW_EVENT_COMMISSIONS: 'VIEW_EVENT_COMMISSIONS',
  CALCULATE_EVENT_COMMISSIONS: 'CALCULATE_EVENT_COMMISSIONS',
} as const;

export const BRANCH_PERMISSIONS = {
  VIEW_OWN_BRANCHES: 'VIEW_OWN_BRANCHES',
  VIEW_BRANCHES: 'VIEW_BRANCHES',
  CREATE_BRANCHES: 'CREATE_BRANCHES',
  EDIT_BRANCHES: 'EDIT_BRANCHES',
  DELETE_BRANCHES: 'DELETE_BRANCHES',
  MANAGE_BRANCHES: 'MANAGE_BRANCHES',
} as const;

export const CONVERSATION_PERMISSIONS = {
  ACCESS_CONVERSATIONS: 'ACCESS_CONVERSATIONS',
} as const;

export const AFFECTATION_PERMISSIONS = {
  ACCESS_AFFECTATIONS: 'ACCESS_AFFECTATIONS',
} as const;

export const APPOINTMENT_PERMISSIONS = {
  ACCESS_APPOINTMENTS: 'ACCESS_APPOINTMENTS',
} as const;

export const REQUEST_PERMISSIONS = {
  ACCESS_REQUESTS: 'ACCESS_REQUESTS',
} as const;

export const CALENDAR_PERMISSIONS = {
  ACCESS_ALL_CALENDARS: 'ACCESS_ALL_CALENDARS',
  ACCESS_OWN_CALENDAR: 'ACCESS_OWN_CALENDAR',
} as const;

export const RESERVATION_PERMISSIONS = {
  MANAGE_RESERVATION_STATUSES: 'MANAGE_RESERVATION_STATUSES',
  ACCESS_RESERVATIONS: 'ACCESS_RESERVATIONS',
  VIEW_ASSIGNED_RESERVATIONS: 'VIEW_ASSIGNED_RESERVATIONS',
  VIEW_OWN_RESERVATIONS: 'VIEW_OWN_RESERVATIONS',
  VIEW_OWN_BRANCH_RESERVATIONS: 'VIEW_OWN_BRANCH_RESERVATIONS',
  VIEW_DELETED_RESERVATIONS: 'VIEW_DELETED_RESERVATIONS',
  VIEW_RESERVATION_STATS: 'VIEW_RESERVATION_STATS',
  VIEW_RESERVATIONS: 'VIEW_RESERVATIONS',
  CREATE_RESERVATIONS: 'CREATE_RESERVATIONS',
  EDIT_RESERVATIONS: 'EDIT_RESERVATIONS',
  DELETE_RESERVATIONS: 'DELETE_RESERVATIONS',
} as const;

export const PARTNER_PERMISSIONS = {
  ACCESS_PARTNERS: 'ACCESS_PARTNERS',
  VIEW_PARTNERS: 'VIEW_PARTNERS',
  CREATE_PARTNERS: 'CREATE_PARTNERS',
  EDIT_PARTNERS: 'EDIT_PARTNERS',
  DELETE_PARTNERS: 'DELETE_PARTNERS',
  MANAGE_PARTNERS: 'MANAGE_PARTNERS',
} as const;

export const ALLERGY_PERMISSIONS = {
  VIEW_ALLERGIES: 'VIEW_ALLERGIES',
  CREATE_ALLERGIES: 'CREATE_ALLERGIES',
  EDIT_ALLERGIES: 'EDIT_ALLERGIES',
  DELETE_ALLERGIES: 'DELETE_ALLERGIES',
} as const;

export const SETTINGS_PERMISSIONS = {
  VIEW_SETTINGS: 'VIEW_SETTINGS',
  EDIT_SETTINGS: 'EDIT_SETTINGS',
  MANAGE_RESERVATION_SETTINGS: 'MANAGE_RESERVATION_SETTINGS',
} as const;

export const FOOD_PERMISSIONS = {
  VIEW_FOODS: 'VIEW_FOODS',
  CREATE_FOODS: 'CREATE_FOODS',
  EDIT_FOODS: 'EDIT_FOODS',
  DELETE_FOODS: 'DELETE_FOODS',
  MANAGE_FOODS: 'MANAGE_FOODS',
} as const;
export const MESSAGE_PERMISSIONS = {
  VIEW_MESSAGES: 'VIEW_MESSAGES',
} as const;

export const BILLING_PERMISSIONS = {
  VIEW_BILLING: 'VIEW_BILLING',
  MANAGE_BILLING: 'MANAGE_BILLING',
  ACCESS_BILLING: 'ACCESS_BILLING',
  MANAGE_INVOICE_ITEM_TYPES: 'MANAGE_INVOICE_ITEM_TYPES',
  MANAGE_TAX_TYPES: 'MANAGE_TAX_TYPES',
} as const;
export const SMS_PERMISSIONS = {
  VIEW_SMS: 'VIEW_SMS',
  CREATE_SMS: 'CREATE_SMS',
  EDIT_SMS: 'EDIT_SMS',
  DELETE_SMS: 'DELETE_SMS',
  MANAGE_SMS: 'MANAGE_SMS',
  MANAGE_SCHEDULED_SMS: 'MANAGE_SCHEDULED_SMS',
} as const;



export const SELLER_DASHBOARD_PERMISSIONS = {
  ACCESS_SELLER_DASHBOARD: 'ACCESS_SELLER_DASHBOARD',
} as const;

export const INVITATION_PERMISSIONS = {
  ACCESS_INVITATIONS: 'ACCESS_INVITATIONS',
  CREATE_INVITATION: 'CREATE_INVITATION',
  EDIT_INVITATION: 'EDIT_INVITATION',
  DELETE_INVITATION: 'DELETE_INVITATION',
  MANAGE_INVITATIONS: 'MANAGE_INVITATIONS'
};

export const AUDIT_PERMISSIONS = {
  ACCESS_AUDIT_LOGS: 'ACCESS_AUDIT_LOGS',
  VIEW_AUDIT_LOGS: 'VIEW_AUDIT_LOGS',
  EXPORT_AUDIT_LOGS: 'EXPORT_AUDIT_LOGS',
} as const;

export const NOTIFICATION_PERMISSIONS = {
  MANAGE_NOTIFICATIONS: 'MANAGE_NOTIFICATIONS',
  VIEW_NOTIFICATIONS: 'VIEW_NOTIFICATIONS',
  CREATE_NOTIFICATIONS: 'CREATE_NOTIFICATIONS',
  EDIT_NOTIFICATIONS: 'EDIT_NOTIFICATIONS',
  DELETE_NOTIFICATIONS: 'DELETE_NOTIFICATIONS',
} as const;

export { RECONTACT_PERMISSIONS } from './recontact-permission-codes';

export type PermissionCode =
  | typeof USER_PERMISSIONS[keyof typeof USER_PERMISSIONS]
  | typeof ROLE_PERMISSIONS[keyof typeof ROLE_PERMISSIONS]
  | typeof REGION_PERMISSIONS[keyof typeof REGION_PERMISSIONS]
  | typeof EVENT_PERMISSIONS[keyof typeof EVENT_PERMISSIONS]
  | typeof BRANCH_PERMISSIONS[keyof typeof BRANCH_PERMISSIONS]
  | typeof RESERVATION_PERMISSIONS[keyof typeof RESERVATION_PERMISSIONS]
  | typeof RECONTACT_PERMISSIONS[keyof typeof RECONTACT_PERMISSIONS]
  | typeof PARTNER_PERMISSIONS[keyof typeof PARTNER_PERMISSIONS]
  | typeof ALLERGY_PERMISSIONS[keyof typeof ALLERGY_PERMISSIONS]
  | typeof RESERVATION_STATUS_PERMISSIONS[keyof typeof RESERVATION_STATUS_PERMISSIONS]
  | typeof COMMISSION_TYPE_PERMISSIONS[keyof typeof COMMISSION_TYPE_PERMISSIONS]
  | typeof COMMISSION_PERMISSIONS[keyof typeof COMMISSION_PERMISSIONS]
  | typeof FOOD_PERMISSIONS[keyof typeof FOOD_PERMISSIONS]
  | typeof MESSAGE_PERMISSIONS[keyof typeof MESSAGE_PERMISSIONS]
  | typeof AUDIT_PERMISSIONS[keyof typeof AUDIT_PERMISSIONS]
  | typeof NOTIFICATION_PERMISSIONS[keyof typeof NOTIFICATION_PERMISSIONS]
  | typeof CONTACT_REQUEST_PERMISSIONS[keyof typeof CONTACT_REQUEST_PERMISSIONS];