import { USER_PERMISSIONS, R<PERSON><PERSON>_PERMISSIONS, R<PERSON>ION_PERMISSIONS, BRANCH_PERMISSIONS, RESERVATION_PERMISSIONS, RECONTACT_PERMISSIONS, PARTNER_PERMISSIONS, AL<PERSON>RGY_PERMISSIONS, SETTINGS_PERMISSIONS, FOOD_PERMISSIONS, MESSAGE_PERMISSIONS, CUSTOMER_PERMISSIONS, EVENT_PERMISSIONS, SMS_PERMISSIONS, BILLING_PERMISSIONS, CONVERSATION_PERMISSIONS, AFFECTATION_PERMISSIONS, APPOINTMENT_PERMISSIONS, <PERSON><PERSON><PERSON><PERSON>_PERMISSIONS, SELLER_DASHBOARD_PERMISSIONS, DELIVERY_PERMISSIONS, INVITATION_PERMISSIONS } from '../../types/permission-codes';
import { RESERVATION_STATUS_PERMISSIONS } from '../../types/reservation-status-codes';
import { COMMISSION_TYPE_PERMISSIONS } from '../../types/commission-type-codes';
import { COMMISSION_PERMISSIONS } from '../../types/commission-codes';
import { IPermission, IRole } from '../../types/next-auth';
import * as RoleUtils from './role-utils';

// Accepts either a Session.user, JWT, or a user-like object with permissions array
export type UserLike = {
  permissions?: string[];
  directPermissions?: IPermission[];
  roles?: IRole[];
};

function hasPermission(user: UserLike, permission: string): boolean {
  if (!user) return false;
  if (user.permissions?.includes(permission)) return true;
  if (user.directPermissions?.some((p) => p.code === permission)) return true;
  if (user.roles?.some((role) => role.permissions?.includes(permission))) return true;
  return false;
}

// --- CUSTOMER PERMISSIONS ---
export function canUserViewCustomerPhone(user: UserLike) {
  return (
    RoleUtils.isSuperAdmin(user.roles) ||
    hasPermission(user, CUSTOMER_PERMISSIONS.VIEW_CUSTOMER_PHONE)
  );
}

// --- DELIVERY PERMISSIONS ---
export function canUserViewDeliveries(user: UserLike) {
  return (
    RoleUtils.isSuperAdmin(user.roles) ||
    hasPermission(user, DELIVERY_PERMISSIONS.ACCESS_DELIVERY_AVAILABILITY)
  );
}


// --- USER PERMISSIONS ---
export function canUserViewOwnBranchUsers(user: UserLike) {
  return (
    RoleUtils.isSuperAdmin(user.roles) ||
    hasPermission(user, USER_PERMISSIONS.VIEW_OWN_BRANCH_USERS)
  );
}

export function canUserViewUsers(user: UserLike) {
  return (
    RoleUtils.isSuperAdmin(user.roles) ||
    hasPermission(user, USER_PERMISSIONS.VIEW_USERS)
  );
}
export function canUserViewCustomersDetails(user: UserLike) {
  return (
    RoleUtils.isSuperAdmin(user.roles) ||
    hasPermission(user, CUSTOMER_PERMISSIONS.VIEW_CUSTOMER_DETAILS)
  );
}
export function canUserCreateUsers(user: UserLike) {
  return hasPermission(user, USER_PERMISSIONS.CREATE_USERS);
}
export function canUserEditUsers(user: UserLike) {
  return hasPermission(user, USER_PERMISSIONS.EDIT_USERS);
}
export function canUserDeleteUsers(user: UserLike) {
  return hasPermission(user, USER_PERMISSIONS.DELETE_USERS);
}
export function canUserManageUsers(user: UserLike) {
  return RoleUtils.hasAdminPrivileges(user.roles) || hasPermission(user, USER_PERMISSIONS.MANAGE_USERS);
}
export function canUserManageUserRoles(user: UserLike) {
  return hasPermission(user, USER_PERMISSIONS.MANAGE_USER_ROLES);
}
export function canUserViewNAS(permissions: any[]) {
  return (
    permissions.some(permission=>permission===USER_PERMISSIONS.VIEW_NAS)
  );
}

// --- CONVERSATION PERMISSIONS ---
export function canUserAccessConversations(user: UserLike) {
  return RoleUtils.isSuperAdmin(user.roles) || hasPermission(user, CONVERSATION_PERMISSIONS.ACCESS_CONVERSATIONS);
}

// --- ROLE PERMISSIONS ---
export function canUserViewRoles(user: UserLike) {
  return hasPermission(user, ROLE_PERMISSIONS.VIEW_ROLES);
}
export function canUserCreateRoles(user: UserLike) {
  return hasPermission(user, ROLE_PERMISSIONS.CREATE_ROLES);
}
export function canUserEditRoles(user: UserLike) {
  return hasPermission(user, ROLE_PERMISSIONS.EDIT_ROLES);
}
export function canUserDeleteRoles(user: UserLike) {
  return hasPermission(user, ROLE_PERMISSIONS.DELETE_ROLES);
}
export function canUserManagePermissions(user: UserLike) {
  return hasPermission(user, ROLE_PERMISSIONS.MANAGE_PERMISSIONS);
}

// --- BILLING PERMISSIONS ---
export function canUserAccessBilling(user: UserLike) {
  return RoleUtils.isSuperAdmin(user.roles) || hasPermission(user, BILLING_PERMISSIONS.ACCESS_BILLING);
}
export function canUserManageBilling(user: UserLike) {
  return RoleUtils.isSuperAdmin(user.roles) || hasPermission(user, BILLING_PERMISSIONS.MANAGE_BILLING);
}
export function canUserManageTaxTypes(user: UserLike) {
  return RoleUtils.isSuperAdmin(user.roles) || hasPermission(user, BILLING_PERMISSIONS.MANAGE_TAX_TYPES);
}
export function canUserManageInvoiceItemTypes(user: UserLike) {
  return RoleUtils.isSuperAdmin(user.roles) || hasPermission(user, BILLING_PERMISSIONS.MANAGE_INVOICE_ITEM_TYPES);
}
// --- REGION PERMISSIONS ---
export function canUserViewRegions(user: UserLike) {
  return hasPermission(user, REGION_PERMISSIONS.VIEW_REGIONS);
}
export function canUserCreateRegions(user: UserLike) {
  return hasPermission(user, REGION_PERMISSIONS.CREATE_REGIONS);
}
export function canUserEditRegions(user: UserLike) {
  return hasPermission(user, REGION_PERMISSIONS.EDIT_REGIONS);
}
export function canUserDeleteRegions(user: UserLike) {
  return hasPermission(user, REGION_PERMISSIONS.DELETE_REGIONS);
}
export function canUserManageRegions(user: UserLike) {
  return hasPermission(user, REGION_PERMISSIONS.MANAGE_REGIONS);
}

// --- SMS PERMISSIONS ---
export function canUserManageSms(user: UserLike) {
  return RoleUtils.isSuperAdmin(user.roles) || hasPermission(user, SMS_PERMISSIONS.MANAGE_SMS);
}
export function canUserManageScheduledSms(user: UserLike) {
  return RoleUtils.isSuperAdmin(user.roles) || hasPermission(user, SMS_PERMISSIONS.MANAGE_SCHEDULED_SMS);
}

// --- BRANCH PERMISSIONS ---
export function canUserViewOwnBranches(user: UserLike) {
  return (
    RoleUtils.isSuperAdmin(user.roles) ||
    hasPermission(user, BRANCH_PERMISSIONS.VIEW_OWN_BRANCHES)
  );
}
export function canUserViewAllBranches(user: UserLike) {
  return RoleUtils.isSuperAdmin(user.roles) || hasPermission(user, BRANCH_PERMISSIONS.VIEW_BRANCHES);
}
export function canUserCreateBranches(user: UserLike) {
  return hasPermission(user, BRANCH_PERMISSIONS.CREATE_BRANCHES);
}
export function canUserEditBranches(user: UserLike) {
  return hasPermission(user, BRANCH_PERMISSIONS.EDIT_BRANCHES);
}
export function canUserDeleteBranches(user: UserLike) {
  return hasPermission(user, BRANCH_PERMISSIONS.DELETE_BRANCHES);
}
export function canUserManageBranches(user: UserLike) {
  return hasPermission(user, BRANCH_PERMISSIONS.MANAGE_BRANCHES);
}

// --- RESERVATION PERMISSIONS ---
export function canUserViewOwnReservations(user: UserLike) {
  return (
    RoleUtils.isSuperAdmin(user.roles) ||
    RoleUtils.isBranchesAdmin(user.roles) ||
    RoleUtils.isBranchesAgent(user.roles) ||
    RoleUtils.isPAPUser(user.roles) ||
    hasPermission(user, RESERVATION_PERMISSIONS.VIEW_OWN_RESERVATIONS)
  );
}

export function canUserViewOwnBranchReservations(user: UserLike) {
  return (
    RoleUtils.isSuperAdmin(user.roles) ||
    hasPermission(user, RESERVATION_PERMISSIONS.VIEW_OWN_BRANCH_RESERVATIONS)
  );
}
export function canUserViewAssignedReservations(user: UserLike) {
  return (
    RoleUtils.isSuperAdmin(user.roles) ||
    hasPermission(user, RESERVATION_PERMISSIONS.VIEW_ASSIGNED_RESERVATIONS)
  );
}
export function canUserViewDeletedReservations(user: UserLike) {
  return (
    RoleUtils.isSuperAdmin(user.roles) ||
    hasPermission(user, RESERVATION_PERMISSIONS.VIEW_DELETED_RESERVATIONS)
  );
}
export function canUserViewReservationsStats(user: UserLike) {
  return (
    RoleUtils.isSuperAdmin(user.roles) ||
    RoleUtils.isBranchesAdmin(user.roles) ||
    RoleUtils.isBranchesAgent(user.roles) ||
    hasPermission(user, RESERVATION_PERMISSIONS.VIEW_RESERVATION_STATS)
  );
}
export function canUserViewAllReservations(user: UserLike) {
  return RoleUtils.isSuperAdmin(user.roles) || hasPermission(user, RESERVATION_PERMISSIONS.VIEW_RESERVATIONS);
}
export function doesUserHaveReservationAccess(user: UserLike) {
  return (
    canUserAccessReservations(user) ||
    canUserViewAllReservations(user) ||
    canUserViewOwnBranchReservations(user) ||
    canUserViewAssignedReservations(user) ||
    canUserViewOwnReservations(user)
  );
}

export function canUserViewOnlyOwnReservations(user: UserLike) {
  return canUserViewOwnReservations(user) && !(
    canUserViewAllReservations(user) ||
    canUserViewOwnBranchReservations(user) ||
    canUserViewAssignedReservations(user)
  );
}
export function canUserViewOnlyAssignedReservations(user: UserLike) {
  return canUserViewAssignedReservations(user) && !(
    canUserViewAllReservations(user) ||
    canUserViewOwnBranchReservations(user) ||
    canUserViewOwnReservations(user)
  );
}
export function canUserAccessReservations(user: UserLike) {
  return (
    RoleUtils.isSuperAdmin(user.roles) ||
    hasPermission(user, RESERVATION_PERMISSIONS.ACCESS_RESERVATIONS)
  );
}
export function canUserCreateReservations(user: UserLike) {
  return (
    RoleUtils.isSuperAdmin(user.roles) ||
    hasPermission(user, RESERVATION_PERMISSIONS.CREATE_RESERVATIONS)
  );
}
export function canUserEditReservations(user: UserLike) {
  return (
    RoleUtils.isSuperAdmin(user.roles) ||
    hasPermission(user, RESERVATION_PERMISSIONS.EDIT_RESERVATIONS)
  );
}
export function canUserDeleteReservations(user: UserLike) {
  return (
    RoleUtils.isSuperAdmin(user.roles) ||
    hasPermission(user, RESERVATION_PERMISSIONS.DELETE_RESERVATIONS)
  );
}

// --- PARTNER PERMISSIONS ---
export function canUserViewPartners(user: UserLike) {
  return hasPermission(user, PARTNER_PERMISSIONS.VIEW_PARTNERS);
}
export function canUserCreatePartners(user: UserLike) {
  return hasPermission(user, PARTNER_PERMISSIONS.CREATE_PARTNERS);
}
export function canUserEditPartners(user: UserLike) {
  return hasPermission(user, PARTNER_PERMISSIONS.EDIT_PARTNERS);
}
export function canUserDeletePartners(user: UserLike) {
  return hasPermission(user, PARTNER_PERMISSIONS.DELETE_PARTNERS);
}
export function canUserManagePartners(user: UserLike) {
  return hasPermission(user, PARTNER_PERMISSIONS.MANAGE_PARTNERS);
}

// --- ALLERGY PERMISSIONS ---
export function canUserViewAllergies(user: UserLike) {
  return hasPermission(user, ALLERGY_PERMISSIONS.VIEW_ALLERGIES);
}
export function canUserCreateAllergies(user: UserLike) {
  return hasPermission(user, ALLERGY_PERMISSIONS.CREATE_ALLERGIES);
}
export function canUserEditAllergies(user: UserLike) {
  return hasPermission(user, ALLERGY_PERMISSIONS.EDIT_ALLERGIES);
}
export function canUserDeleteAllergies(user: UserLike) {
  return hasPermission(user, ALLERGY_PERMISSIONS.DELETE_ALLERGIES);
}

// --- RESERVATION STATUS PERMISSIONS ---
export function canUserViewReservationStatuses(user: UserLike) {
  return (
    RoleUtils.isSuperAdmin(user.roles) ||
    hasPermission(user, RESERVATION_STATUS_PERMISSIONS.VIEW_RESERVATION_STATUSES)
  );
}
export function canUserViewSellerReservationStatuses(user: UserLike) {
  return (
    RoleUtils.isSuperAdmin(user.roles) ||
    hasPermission(user, RESERVATION_STATUS_PERMISSIONS.VIEW_SELLER_RESERVATION_STATUSES)
  );
}


export function canUserViewPhoneReservationStatuses(user: UserLike) {
  return (
    RoleUtils.isSuperAdmin(user.roles) ||
    RoleUtils.isBranchesAdmin(user.roles) ||
    RoleUtils.isBranchesAgent(user.roles) ||
    hasPermission(user, RESERVATION_STATUS_PERMISSIONS.VIEW_PHONE_RESERVATION_STATUSES)
  );
}
export function canUserCreateReservationStatuses(user: UserLike) {
  return hasPermission(user, RESERVATION_STATUS_PERMISSIONS.CREATE_RESERVATION_STATUSES);
}
export function canUserEditReservationStatuses(user: UserLike) {
  return hasPermission(user, RESERVATION_STATUS_PERMISSIONS.EDIT_RESERVATION_STATUSES);
}
export function canUserDeleteReservationStatuses(user: UserLike) {
  return hasPermission(user, RESERVATION_STATUS_PERMISSIONS.DELETE_RESERVATION_STATUSES);
}

// --- COMMISSION TYPE PERMISSIONS ---
export function canUserViewCommissionTypes(user: UserLike) {
  return hasPermission(user, COMMISSION_TYPE_PERMISSIONS.VIEW_COMMISSION_TYPES);
}
export function canUserCreateCommissionTypes(user: UserLike) {
  return hasPermission(user, COMMISSION_TYPE_PERMISSIONS.CREATE_COMMISSION_TYPES);
}
export function canUserEditCommissionTypes(user: UserLike) {
  return hasPermission(user, COMMISSION_TYPE_PERMISSIONS.EDIT_COMMISSION_TYPES);
}
export function canUserDeleteCommissionTypes(user: UserLike) {
  return hasPermission(user, COMMISSION_TYPE_PERMISSIONS.DELETE_COMMISSION_TYPES);
}

// --- COMMISSION PERMISSIONS ---
export function canUserViewCommissions(user: UserLike) {
  return hasPermission(user, COMMISSION_PERMISSIONS.VIEW_COMMISSIONS);
}
export function canUserApproveCommissions(user: UserLike) {
  return hasPermission(user, COMMISSION_PERMISSIONS.APPROVE_COMMISSIONS);
}
export function canUserManageCommissions(user: UserLike) {
  return hasPermission(user, COMMISSION_PERMISSIONS.MANAGE_COMMISSIONS);
}
export function canUserManageCommissionConfig(user: UserLike) {
  return RoleUtils.isSuperAdmin(user.roles) ||
         RoleUtils.isBranchesAdmin(user.roles) ||
         hasPermission(user, COMMISSION_PERMISSIONS.MANAGE_COMMISSION_CONFIG);
}

// Check if user is a branchAdmin of a specific branch
export async function canUserManageCommissionConfigForBranch(user: UserLike, branchId: string): Promise<boolean> {
  // SuperAdmins can manage any branch
  if (RoleUtils.isSuperAdmin(user.roles)) {
    return true;
  }

  // Check if user has the BranchesAdmin role and is responsible for this branch
  if (RoleUtils.isBranchesAdmin(user.roles)) {
    const Branch = (await import('../../models/Branch')).default;
    const branch = await Branch.findById(branchId).lean();

    if (branch && branch.responsible) {
      return branch.responsible.some((responsibleId: any) =>
        responsibleId.toString() === user.id
      );
    }
  }

  // Check if user has specific permission
  return hasPermission(user, COMMISSION_PERMISSIONS.MANAGE_COMMISSION_CONFIG);
}



// --- FOOD PERMISSIONS ---
export function canUserViewFoods(user: UserLike) {
  return hasPermission(user, FOOD_PERMISSIONS.VIEW_FOODS);
}
export function canUserCreateFoods(user: UserLike) {
  return hasPermission(user, FOOD_PERMISSIONS.CREATE_FOODS);
}
export function canUserEditFoods(user: UserLike) {
  return hasPermission(user, FOOD_PERMISSIONS.EDIT_FOODS);
}
export function canUserDeleteFoods(user: UserLike) {
  return hasPermission(user, FOOD_PERMISSIONS.DELETE_FOODS);
}
export function canUserManageFoods(user: UserLike) {
  return hasPermission(user, FOOD_PERMISSIONS.MANAGE_FOODS);
}

// --- SETTINGS PERMISSIONS ---
export function canUserViewSettings(user: UserLike) {
  return hasPermission(user, SETTINGS_PERMISSIONS.VIEW_SETTINGS);
}
export function canUserEditSettings(user: UserLike) {
  return hasPermission(user, SETTINGS_PERMISSIONS.EDIT_SETTINGS);
}

// --- MESSAGE PERMISSIONS ---
export function canUserViewMessages(user: UserLike) {
  return (
    RoleUtils.isSuperAdmin(user.roles) ||
    hasPermission(user, MESSAGE_PERMISSIONS.VIEW_MESSAGES)
  );
}


// --- EVENT PERMISSIONS ---
export function canUserViewEvents(user: UserLike) {
  return (
    RoleUtils.isSuperAdmin(user.roles) ||
    hasPermission(user, EVENT_PERMISSIONS.VIEW_EVENTS)
  );
}

export function canUserViewAllEvents(user: UserLike) {
  return (
    RoleUtils.isSuperAdmin(user.roles) ||
    hasPermission(user, EVENT_PERMISSIONS.VIEW_ALL_EVENTS)
  );
}
export function canUserCreateEvents(user: UserLike) {
  return (
    RoleUtils.isSuperAdmin(user.roles) ||
    hasPermission(user, EVENT_PERMISSIONS.CREATE_EVENTS)
  );
}
export function canUserEditEvents(user: UserLike) {
  return (
    RoleUtils.isSuperAdmin(user.roles) ||
    hasPermission(user, EVENT_PERMISSIONS.EDIT_EVENTS)
  );
}

export function canUserDeleteEvents(user: UserLike) {
  return (
    RoleUtils.isSuperAdmin(user.roles) ||
    hasPermission(user, EVENT_PERMISSIONS.DELETE_EVENTS)
  );
}

// --- EVENT SUPERVISION PERMISSIONS ---
export function canUserSuperviseEvents(user: UserLike) {
  return (
    RoleUtils.isSuperAdmin(user.roles) ||
    RoleUtils.isEventSupervisor(user.roles) ||
    hasPermission(user, EVENT_PERMISSIONS.SUPERVISE_EVENTS)
  );
}

export function canUserViewSupervisedEvents(user: UserLike) {
  return (
    RoleUtils.isSuperAdmin(user.roles) ||
    RoleUtils.isEventSupervisor(user.roles) ||
    hasPermission(user, EVENT_PERMISSIONS.VIEW_SUPERVISED_EVENTS)
  );
}

export function canUserEditEventReports(user: UserLike) {
  return (
    RoleUtils.isSuperAdmin(user.roles) ||
    RoleUtils.isEventSupervisor(user.roles) ||
    hasPermission(user, EVENT_PERMISSIONS.EDIT_EVENT_REPORTS)
  );
}

export function canUserSubmitEventReports(user: UserLike) {
  return (
    RoleUtils.isSuperAdmin(user.roles) ||
    RoleUtils.isEventSupervisor(user.roles) ||
    hasPermission(user, EVENT_PERMISSIONS.SUBMIT_EVENT_REPORTS)
  );
}

export function canUserViewEventReports(user: UserLike) {
  return (
    RoleUtils.isSuperAdmin(user.roles) ||
    hasPermission(user, EVENT_PERMISSIONS.VIEW_EVENT_REPORTS)
  );
}

export function canUserValidateEventReports(user: UserLike) {
  return (
    RoleUtils.isSuperAdmin(user.roles) ||
    hasPermission(user, EVENT_PERMISSIONS.VALIDATE_EVENT_REPORTS)
  );
}

export function canUserManageEventPersonnel(user: UserLike) {
  return (
    RoleUtils.isSuperAdmin(user.roles) ||
    hasPermission(user, EVENT_PERMISSIONS.MANAGE_EVENT_PERSONNEL)
  );
}

export function canUserViewEventCommissions(user: UserLike) {
  return (
    RoleUtils.isSuperAdmin(user.roles) ||
    hasPermission(user, EVENT_PERMISSIONS.VIEW_EVENT_COMMISSIONS)
  );
}

export function canUserCalculateEventCommissions(user: UserLike) {
  return (
    RoleUtils.isSuperAdmin(user.roles) ||
    hasPermission(user, EVENT_PERMISSIONS.CALCULATE_EVENT_COMMISSIONS)
  );
}

// --- AFFECTATION PERMISSIONS ---
export function canUserAccessAffectations(user: UserLike) {
  return (
    RoleUtils.isSuperAdmin(user.roles) ||
    hasPermission(user, AFFECTATION_PERMISSIONS.ACCESS_AFFECTATIONS)
  );
}

// --- APPOINTMENT PERMISSIONS ---
export function canUserAccessAppointments(user: UserLike) {
  return (
    RoleUtils.isSuperAdmin(user.roles) ||
    hasPermission(user, APPOINTMENT_PERMISSIONS.ACCESS_APPOINTMENTS)
  );
}

// --- CALENDAR PERMISSIONS ---
export function canUserAccessCalendars(user: UserLike) {
  return (
    RoleUtils.isSuperAdmin(user.roles) ||
    hasPermission(user, CALENDAR_PERMISSIONS.ACCESS_ALL_CALENDARS) ||
    hasPermission(user, CALENDAR_PERMISSIONS.ACCESS_OWN_CALENDAR)
  );
}


export function canUserViewSellerDashboard(user: UserLike) {
  return (
    RoleUtils.isSuperAdmin(user.roles) || RoleUtils.isSeller(user.roles) ||
    hasPermission(user, SELLER_DASHBOARD_PERMISSIONS.ACCESS_SELLER_DASHBOARD)
  );
}

export function canAccessInvitations(user: UserLike) {
  return RoleUtils.isSuperAdmin(user.roles) || hasPermission(user, INVITATION_PERMISSIONS.ACCESS_INVITATIONS);
}

export function canCreateInvitation(user: UserLike) {
  return hasPermission(user, INVITATION_PERMISSIONS.CREATE_INVITATION);
}

export function canEditInvitation(user: UserLike) {
  return hasPermission(user, INVITATION_PERMISSIONS.EDIT_INVITATION);
}

export function canDeleteInvitation(user: UserLike) {
  return hasPermission(user, INVITATION_PERMISSIONS.DELETE_INVITATION);
}

export function canManageInvitations(user: UserLike) {
  return hasPermission(user, INVITATION_PERMISSIONS.MANAGE_INVITATIONS);
}

// --- RECONTACT PERMISSIONS ---
export function canUserAccessRecontactReservations(user: UserLike) {
  return RoleUtils.isSuperAdmin(user.roles) || hasPermission(user, RECONTACT_PERMISSIONS.ACCESS_RECONTACT_RESERVATIONS);
}

export function canUserManageRecontactStatuses(user: UserLike) {
  return RoleUtils.isSuperAdmin(user.roles) || hasPermission(user, RECONTACT_PERMISSIONS.MANAGE_RECONTACT_STATUSES);
}
