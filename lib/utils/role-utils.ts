// Define a RoleType interface for the user roles
interface RoleType {
  _id: string;
  name: string;
  [key: string]: any;
}

// Helper to extract roles array from either argument
function extractRoles(input: any): RoleType[] {
  if (Array.isArray(input)) return input;
  if (input?.user?.roles) return input.user.roles;
  return [];
}

// Check if user is SuperAdmin
export function isSuperAdmin(rolesOrSession?: any): boolean {
  const roles = extractRoles(rolesOrSession);
  return roles.some((role: any) =>
    typeof role === 'object'
      ? role._id?.toString() === '67add3214badd3283e873329'
      : role?.toString() === '67add3214badd3283e873329'
  );
}

// Check if user is Event Supervisor
export function isEventSupervisor(rolesOrSession?: any): boolean {
  const roles = extractRoles(rolesOrSession);
   return roles.some((role: any) =>
    typeof role === 'object'
      ? role._id?.toString() === '68542308d93e57259f691917'
      : role?.toString() === '68542308d93e57259f691917'
  );
}

// Check if user is Cook
export function isCook(rolesOrSession?: any): boolean {
  const roles = extractRoles(rolesOrSession);
  return roles.some((role: any) =>
    typeof role === 'object'
      ? role.name === 'Cook'
      : false
  );
}

// Check if user is BranchesAgent 67c032fe0af5117479e27737
export function isBranchesAgent(rolesOrSession?: any): boolean {
  const roles = extractRoles(rolesOrSession);
  return roles.some((role: any) => 
    typeof role === 'object' 
      ? role._id?.toString() === '67c032fe0af5117479e27737' 
      : role?.toString() === '67c032fe0af5117479e27737'
  );
}

// Check if user is BranchesAdmin
export function isBranchesAdmin(rolesOrSession?: any): boolean {
  const roles = extractRoles(rolesOrSession);
  return roles.some((role: any) => 
    typeof role === 'object' 
      ? role._id?.toString() === '67c032e90af5117479e27731' 
      : role?.toString() === '67c032e90af5117479e27731'
  );
}

// Check if user is Seller
export function isSeller(rolesOrSession?: any): boolean {
  const roles = extractRoles(rolesOrSession);
  return roles.some((role: any) => 
    typeof role === 'object' 
      ? role.name?.toLowerCase() === 'seller'
      : false
  );
}

// Check if user is Telephoniste
export function isTelephoniste(rolesOrSession?: any): boolean {
  const roles = extractRoles(rolesOrSession);
  return roles.some((role: any) => 
    typeof role === 'object' 
      ? role.name?.toLowerCase() === 'telephoniste'
      : false
  );
}

// Check if user is PAP
export function isPAPUser(rolesOrSession?: any): boolean {
  const roles = extractRoles(rolesOrSession);
  return roles.some((role: any) => 
    typeof role === 'object' 
      ? role._id?.toString() === '67fbd1707839bdba5be4b02b' 
      : role?.toString() === '67fbd1707839bdba5be4b02b'
  );
}

// Check if user has admin privileges (either SuperAdmin or BranchesAdmin)
export function hasAdminPrivileges(rolesOrSession?: any): boolean {
  return isSuperAdmin(rolesOrSession) || isBranchesAdmin(rolesOrSession);
}

// Check if user has calendar access
export function hasCalendarAccess(rolesOrSession?: any): boolean {
  const ALLOWED_ROLE_IDS = ['67add3214badd3283e873329', '67c032e90af5117479e27731'];
  const roles = extractRoles(rolesOrSession);
  return roles.some((role: any) =>
    typeof role === 'object'
      ? ALLOWED_ROLE_IDS.includes(role._id?.toString())
      : ALLOWED_ROLE_IDS.includes(role?.toString())
  );
}

// Get list of dashboard types a user has access to
export function getAccessibleDashboards(rolesOrSession?: any): string[] {
  const dashboards: string[] = [];
  const roles = extractRoles(rolesOrSession);
  // Main dashboard is always available for multi-role users
  dashboards.push('main');
  if (hasAdminPrivileges(roles)) {
    dashboards.push('admin');
  }
  if (isSeller(roles)) {
    dashboards.push('seller');
  }
  if (isPAPUser(roles)) {
    dashboards.push('pap');
  }
  if (isTelephoniste(roles)) {
    dashboards.push('telephoniste');
  }
  return dashboards;
}

// Check if user has only one dashboard type
export function hasSingleDashboard(rolesOrSession?: any): string | null {
  const dashboards = getAccessibleDashboards(rolesOrSession).filter(d => d !== 'main');
  // If user has exactly one dashboard type (excluding 'main')
  if (dashboards.length === 1) {
    return dashboards[0];
  }
  return null;
} 