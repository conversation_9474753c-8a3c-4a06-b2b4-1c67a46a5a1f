import { TooltipProvider } from "@/components/ui/tooltip";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as HotToaster } from "react-hot-toast";

export default function InvoiceSigningLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="min-h-screen">
      <TooltipProvider>
        {children}
      </TooltipProvider>
      <Toaster />
      <HotToaster position="top-right" />
    </div>
  );
} 