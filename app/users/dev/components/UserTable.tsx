"use client";

import { useState, useEffect, useRef } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { useLanguage } from "@/lib/contexts/language-context";
import { Edit, MoreH<PERSON>zon<PERSON>, Trash2, <PERSON>, <PERSON> as LinkI<PERSON>, Check, AlertTriangle, FileText } from "lucide-react";
import { UserForm } from "./UserForm";
import { useToast } from "@/hooks/use-toast";
import { Badge } from "@/components/ui/badge";
import { BranchRolesBadges } from "./BranchRolesBadges";
import { roleDisplayMapping } from "../utils/roleDisplayMapping";
import { ContractStatusBadge } from "./ContractStatusBadge";
import { ContractDetailsDialog } from "./ContractDetailsDialog";

interface UserTableProps {
  users: any[];
  onRefresh: () => void;
  isLoading?: boolean;
  includeDeleted?: boolean;
  onSelectUser?: (user: any) => void;
}

export function UserTable({
  users,
  onRefresh,
  isLoading = false,
  includeDeleted = false,
  onSelectUser
}: UserTableProps) {
  const { t } = useLanguage();
  const { toast } = useToast();
  const [editUser, setEditUser] = useState<any | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [deletingUser, setDeletingUser] = useState<any | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isActionInProgress, setIsActionInProgress] = useState(false);
  const [copiedUserId, setCopiedUserId] = useState<string | null>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [contractStatuses, setContractStatuses] = useState<Record<string, any>>({});
  const [contractDetailsUserId, setContractDetailsUserId] = useState<string | null>(null);
  const [isContractDetailsOpen, setIsContractDetailsOpen] = useState(false);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  // Load contract statuses when users change
  useEffect(() => {
    if (users.length > 0) {
      loadContractStatuses();
    }
  }, [users]);

  const loadContractStatuses = async () => {
    try {
      const userIds = users.map(user => user._id);
      const response = await fetch('/api/contracts/batch-status', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userIds })
      });

      const data = await response.json();
      if (data.success) {
        setContractStatuses(data.statuses);
      }
    } catch (error) {
      console.error('Error loading contract statuses:', error);
    }
  };

  const handleViewContractDetails = (userId: string) => {
    setContractDetailsUserId(userId);
    setIsContractDetailsOpen(true);
  };

  const handleCloseContractDetails = () => {
    setContractDetailsUserId(null);
    setIsContractDetailsOpen(false);
  };

  const handleEditUser = (user: any) => {
    setEditUser(user);
    setIsEditDialogOpen(true);
    if (onSelectUser) {
      onSelectUser(user);
    }
  };

  const handleDeleteUser = (user: any) => {
    setDeletingUser(user);
    setIsDeleteDialogOpen(true);
  };

  const handleRestoreUser = async (user: any) => {
    if (!user._id) return;

    try {
      setIsActionInProgress(true);
      const response = await fetch(`/api/users/dev/${user._id}/restore`, {
        method: "POST",
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to restore user");
      }

      toast({
        title: t("common.success"),
        description: t("users.userRestored"),
      });
      onRefresh();
    } catch (error) {
      console.error("Error restoring user:", error);
      toast({
        title: t("common.error"),
        description: error instanceof Error ? error.message : "An error occurred",
        variant: "destructive",
      });
    } finally {
      setIsActionInProgress(false);
    }
  };

  const copyInvitationLink = (userId: string) => {
    const baseUrl =
      process.env.NEXT_PUBLIC_DEV_SERVER === "true"
        ? "https://devpartner.amq.company"
        : "https://amq.company";
    const link = `${baseUrl}/booking/${userId}/invitation`;
    navigator.clipboard.writeText(link);

    // Set the copied state to show confirmation icon
    setCopiedUserId(userId);

    // Show toast notification
    toast({
      title: t("common.linkCopied"),
      description: t("users.invitationLinkCopied"),
    });

    // Clear any existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // Reset the copied state after 2 seconds
    timeoutRef.current = setTimeout(() => {
      setCopiedUserId(null);
      timeoutRef.current = null;
    }, 2000);
  };

  const confirmDeleteUser = async () => {
    if (!deletingUser?._id) return;

    try {
      setIsActionInProgress(true);
      const response = await fetch(`/api/users/${deletingUser._id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to delete user");
      }

      toast({
        title: t("common.success"),
        description: t("users.userDeleted"),
      });
      setIsDeleteDialogOpen(false);
      onRefresh();
    } catch (error) {
      console.error("Error deleting user:", error);
      toast({
        title: t("common.error"),
        description: error instanceof Error ? error.message : "An error occurred",
        variant: "destructive",
      });
    } finally {
      setIsActionInProgress(false);
    }
  };

  // Helper function to format roles
  const formatRoles = (roles: any[]) => {
    if (!roles || !Array.isArray(roles)) return null;
    return (
      <div className="flex flex-wrap gap-1">
        {roles.map((role) => {
          const mapping = roleDisplayMapping[role._id];
          if (mapping && !mapping.isVisible) return null;
          const displayName = mapping ? t(mapping.translationKey) || mapping.displayed_name : role.name;
          return (
            <Badge key={role._id} variant="outline" className="text-xs">
              {displayName}
            </Badge>
          );
        })}
      </div>
    );
  };

  // Helper to check if a user is deleted
  const isUserDeleted = (user: any) => {
    return user.deletedAt !== null && user.deletedAt !== undefined;
  };

  // Helper to check if user requires a contract (PAP or Seller roles)
  const userRequiresContract = (user: any) => {
    if (!user.roles || !Array.isArray(user.roles)) return false;

    return user.roles.some((role: any) => {
      const roleName = typeof role === 'object' ? role.name?.toLowerCase() : '';
      const roleId = typeof role === 'object' ? role._id : role.toString();

      return roleName === 'seller' ||
             roleName === 'pap' ||
             roleId === '67e0aad60f0a3bdeba18542c' || // Seller role ID
             roleId === '67fbd1707839bdba5be4b02b';   // PAP role ID
    });
  };

  // Helper to check if user needs a contract generated (requires contract but doesn't have one)
  // Note: With the new flow, contracts are auto-created, so this should rarely be needed
  const userNeedsContractGenerated = (user: any) => {
    if (!userRequiresContract(user)) return false;
    const contractStatus = contractStatuses[user._id];
    // Only show "Generate Contract" if user requires contract but has no token at all
    return !contractStatus || !contractStatus.token;
  };

  // Helper to check if user needs admin signing (has contract but admin hasn't signed)
  const userNeedsAdminSigning = (user: any) => {
    if (!userRequiresContract(user)) return false;
    const contractStatus = contractStatuses[user._id];
    return contractStatus && contractStatus.token && !contractStatus.adminSignedAt;
  };

  // Handle generate contract for existing user
  const handleGenerateContract = async (user: any) => {
    try {
      setIsActionInProgress(true);

      const response = await fetch('/api/debug/generate-contract-token', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: user._id,
          contractStartDate: new Date().toISOString()
        })
      });

      const result = await response.json();

      if (result.success) {
        toast({
          title: "Success",
          description: "Contract generated successfully! You can now sign it.",
        });
        await loadContractStatuses(); // Refresh contract statuses
      } else {
        toast({
          title: "Error",
          description: `Failed to generate contract: ${result.error}`,
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error generating contract:', error);
      toast({
        title: "Error",
        description: "Failed to generate contract. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsActionInProgress(false);
    }
  };

  // Helper to format missing required fields for tooltip
  const formatMissingFields = (missingFields: string[]) => {
    if (!missingFields || missingFields.length === 0) return '';

    const fieldLabels = missingFields.map(field => {
      // Convert field keys to readable labels
      switch (field) {
        case 'name': return t('users.name') || 'Name';
        case 'email': return t('users.email') || 'Email';
        case 'phone': return t('users.phone') || 'Phone';
        case 'roles': return t('users.roles') || 'Roles';
        case 'birthDate': return t('users.birthDate') || 'Birth Date';
        case 'address': return t('users.address') || 'Address';
        case 'city': return t('users.city') || 'City';
        case 'postalCode': return t('users.postalCode') || 'Postal Code';
        case 'socialAssurance.nas': return t('users.socialAssurance') || 'Social Insurance Number';
        case 'emergencyContact.name': return t('users.emergencyContactName') || 'Emergency Contact Name';
        case 'emergencyContact.phone': return t('users.emergencyContactPhone') || 'Emergency Contact Phone';
        case 'taxInfo.isTaxable': return t('users.taxInfo.isTaxable') || 'Tax Status';
        case 'taxInfo.taxtypeid': return t('users.taxInfo.taxType') || 'Tax Type';
        case 'partnerId': return t('users.partnerId') || 'Partner Company';
        default: return field;
      }
    });

    return fieldLabels.join(', ');
  };

  return (
    <TooltipProvider>
      <div>
        <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t("users.name")}</TableHead>
            <TableHead>{t("users.email")}</TableHead>
            <TableHead>{t("users.roles") || "Roles"}</TableHead>
            <TableHead>{t("users.branches")}</TableHead>
            <TableHead>{t("users.assignedBranches") || "Assigned Branches"}</TableHead>
            <TableHead>{t("users.phone")}</TableHead>
            <TableHead>Contract Status</TableHead>
            <TableHead className="w-[140px]">{t("common.actions")}</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {users.length === 0 ? (
            <TableRow>
              <TableCell colSpan={8} className="text-center py-6 text-muted-foreground">
                {isLoading ? (
                  <div className="flex justify-center items-center py-4">
                    <div className="mr-2">
                      <div className="animate-spin w-5 h-5 border-2 border-primary border-t-transparent rounded-full"></div>
                    </div>
                    <span>{t("common.loading")}</span>
                  </div>
                ) : includeDeleted
                  ? t("users.noUsersIncludingDeleted")
                  : t("users.noUsers")}
              </TableCell>
            </TableRow>
          ) : isLoading ? (
            // Show loading skeleton rows
            Array(5).fill(0).map((_, index) => (
              <TableRow key={`loading-${index}`}>
                <TableCell>
                  <div className="h-6 w-32 bg-muted animate-pulse rounded"></div>
                </TableCell>
                <TableCell>
                  <div className="h-6 w-48 bg-muted animate-pulse rounded"></div>
                </TableCell>
                <TableCell>
                  <div className="h-6 w-40 bg-muted animate-pulse rounded"></div>
                </TableCell>
                <TableCell>
                  <div className="h-6 w-40 bg-muted animate-pulse rounded"></div>
                </TableCell>
                <TableCell>
                  <div className="h-6 w-28 bg-muted animate-pulse rounded"></div>
                </TableCell>
                <TableCell>
                  <div className="h-6 w-28 bg-muted animate-pulse rounded"></div>
                </TableCell>
                <TableCell>
                  <div className="h-6 w-24 bg-muted animate-pulse rounded"></div>
                </TableCell>
                <TableCell>
                  <div className="h-8 w-20 bg-muted animate-pulse rounded"></div>
                </TableCell>
              </TableRow>
            ))
          ) : (
            users.map((user, index) => {
              const hasIssues = user.hasRequiredFieldIssues && !isUserDeleted(user);
              const isDeleted = isUserDeleted(user);

              let rowClassName = "";
              if (isDeleted) {
                rowClassName = "opacity-60 bg-muted/30";
              } else if (hasIssues) {
                rowClassName = "bg-red-50 hover:bg-red-100";
              }

              return (
                <TableRow
                  key={`${user._id}-${index}`}
                  className={rowClassName}
                >
                <TableCell className="font-medium">
                  <div className="flex items-center gap-2">
                    <span>{user.name}</span>
                    {hasIssues && (
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <AlertTriangle className="h-5 w-5 text-amber-500 cursor-help" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <div className="max-w-xs">
                            <p className="font-medium mb-1">
                              {t('users.missingRequiredFields') || 'Missing Required Fields'}
                            </p>
                            <p className="text-sm">
                              {formatMissingFields(user.missingRequiredFields || [])}
                            </p>
                          </div>
                        </TooltipContent>
                      </Tooltip>
                    )}
                    {isDeleted && (
                      <Badge variant="secondary" className="text-xs">
                        {t("common.deleted")}
                      </Badge>
                    )}
                  </div>
                </TableCell>
                <TableCell>{user.email}</TableCell>
                <TableCell>{formatRoles(user.roles)}</TableCell>
                <TableCell>
                  <BranchRolesBadges branches={user.branches || []} />
                </TableCell>
                <TableCell>
                  {/* Display branch names from branchIds array */}
                  {Array.isArray(user.branchIds) && user.branchIds.length > 0 ? (
                    <div className="flex flex-wrap gap-1">
                      {user.branchIds.map((branch: any) => (
                        <Badge key={branch._id} variant="secondary" className="text-xs">
                          {branch.name || branch._id}
                        </Badge>
                      ))}
                    </div>
                  ) : (
                    <span className="text-muted-foreground text-sm">-</span>
                  )}
                </TableCell>
                <TableCell>{user.phone || "-"}</TableCell>
                <TableCell>
                  <ContractStatusBadge
                    status={contractStatuses[user._id]}
                    userId={user._id}
                    onStatusChange={loadContractStatuses}
                    onViewDetails={() => handleViewContractDetails(user._id)}
                  />
                </TableCell>
                <TableCell>
                  <div className="flex space-x-1 items-center">
                    {!isUserDeleted(user) ? (
                      <>
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={() => copyInvitationLink(user._id)}
                          className="h-8 w-8"
                          title={t("users.copyLink")}
                        >
                          {copiedUserId === user._id ? (
                            <Check className="h-4 w-4 text-green-500" />
                          ) : (
                            <LinkIcon className="h-4 w-4" />
                          )}
                        </Button>

                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button
                              variant="ghost"
                              size="icon"
                            >
                              <MoreHorizontal className="h-4 w-4" />
                              <span className="sr-only">{t("common.actions")}</span>
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleEditUser(user)}>
                              <Edit className="mr-2 h-4 w-4" />
                              {t("common.edit")}
                            </DropdownMenuItem>
                            {userNeedsContractGenerated(user) && (
                              <DropdownMenuItem
                                onClick={() => handleGenerateContract(user)}
                                disabled={isActionInProgress}
                              >
                                <FileText className="mr-2 h-4 w-4" />
                                Generate Contract
                              </DropdownMenuItem>
                            )}
                            <DropdownMenuItem
                              onClick={() => handleDeleteUser(user)}
                              className="text-destructive focus:text-destructive"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              {t("common.delete")}
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </>
                    ) : (
                      <Button
                        variant="default"
                        size="sm"
                        onClick={() => handleRestoreUser(user)}
                        disabled={isActionInProgress}
                        className="h-8 px-3 bg-blue-600 hover:bg-blue-700 text-white border-blue-600 hover:border-blue-700 shadow-sm"
                      >
                        <Eye className="mr-2 h-4 w-4" />
                        {t("common.restore")}
                      </Button>
                    )}
                  </div>
                </TableCell>
              </TableRow>
              );
            })
          )}
        </TableBody>
      </Table>

      {/* Edit User Dialog */}
      <Dialog
        open={isEditDialogOpen}
        onOpenChange={(open) => {
          setIsEditDialogOpen(open);
          if (!open && onSelectUser) {
            onSelectUser(null);
          }
        }}
      >
        <DialogContent
          className="max-w-[1100px] w-[95vw] max-h-[90vh] flex flex-col p-0"
          onPointerDownOutside={(e) => e.preventDefault()} // Prevent closing by clicking outside
        >
          <DialogHeader className="p-6 pb-4 border-b">
            <DialogTitle>{t("users.editUser")}</DialogTitle>
          </DialogHeader>
          <div className="flex-1 overflow-auto px-6">
            {editUser && (
              <UserForm
                user={editUser}
                onSuccess={() => {
                  setIsEditDialogOpen(false);
                  onRefresh();
                  if (onSelectUser) {
                    onSelectUser(null);
                  }
                }}
                onCancel={() => {
                  setIsEditDialogOpen(false);
                  if (onSelectUser) {
                    onSelectUser(null);
                  }
                }}
                className="py-4"
              />
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t("users.confirmDelete")}</AlertDialogTitle>
            <AlertDialogDescription>
              {t("users.deleteWarning")} {deletingUser?.name}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isActionInProgress}>
              {t("common.cancel")}
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteUser}
              disabled={isActionInProgress}
              className="bg-destructive hover:bg-destructive/90"
            >
              {t("common.delete")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Contract Details Dialog */}
      {contractDetailsUserId && (
        <ContractDetailsDialog
          isOpen={isContractDetailsOpen}
          onClose={handleCloseContractDetails}
          userId={contractDetailsUserId}
        />
      )}
      </div>
    </TooltipProvider>
  );
}