"use client";

import { useState, useEffect } from "react";
import { UserFormFields } from "./UserFormFields";
import { UserField, getFieldsForRoles, standardUserFields, roleSpecificFields } from "../utils/UserFieldRegistry";
import { z } from "zod";
import { useLanguage } from "@/lib/contexts/language-context";
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { fetchTaxTypes, fetchPartners } from '../utils/api';
import { DirectPermissionsField } from "./DirectPermissionsField";
import { Permission } from "@/types/permission";
import { useSession } from "next-auth/react";
import { useAppSelector } from "@/lib/redux/hooks";
import { ContractDebugPanel } from "./ContractDebugPanel";
import { ContractSigningDialog } from "./ContractSigningDialog";
import { doesUserRequireContract } from "@/lib/utils/contract-client-utils";

interface UserFormProps {
  user?: {
    _id?: string;
    [key: string]: any;
  };
  onSuccess?: (user: any) => void;
  onCancel?: () => void;
  className?: string;
}

export function UserForm({ user, onSuccess, onCancel, className = "" }: UserFormProps) {
  const { t } = useLanguage();
  const { toast } = useToast();
  // const { data: session } = useSession();
  // Use Redux permissions state for roles
  const reduxRoles = useAppSelector(state => state.permissions.roles);
  const isSuperAdmin = Array.isArray(reduxRoles) && reduxRoles.some(
    (role: any) => role.name === "SuperAdmin" || role._id === "67add3214badd3283e873329"
  );
  const [values, setValues] = useState<Record<string, any>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [apiError, setApiError] = useState<string | null>(null);
  const [roles, setRoles] = useState<{ _id: string; name: string }[]>([]);
  const [branches, setBranches] = useState<{ _id: string; name: string }[]>([]);
  const [visibleFields, setVisibleFields] = useState<UserField[]>(standardUserFields);
  const [roleSpecificVisibleFields, setRoleSpecificVisibleFields] = useState<UserField[]>([]);
  const [isLoading, setIsLoading] = useState(!user);
  const [fileStates, setFileStates] = useState<{ [key: string]: File | null | File[] }>({});
  const [isUploadingFiles, setIsUploadingFiles] = useState(false);
  const [taxTypeOptions, setTaxTypeOptions] = useState<any[]>([]);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [permissionsLoading, setPermissionsLoading] = useState(false);
  const [partners, setPartners] = useState<any[]>([]);
  const [partnersLoading, setPartnersLoading] = useState(false);
  const [partnersError, setPartnersError] = useState<string | null>(null);
  const [showContractDialog, setShowContractDialog] = useState(false);
  const [createdUser, setCreatedUser] = useState<any>(null);

  // Track if form has been initialized to prevent resetting user changes
  const [isFormInitialized, setIsFormInitialized] = useState(false);

  // Initialize form values from user data (only once)
  useEffect(() => {
    if (user && !isFormInitialized) {
      console.log('Initializing form with user data:', user);
      const initialValues: Record<string, any> = { ...user };
      // Convert role objects to role IDs
      if (user.roles && Array.isArray(user.roles)) {
        initialValues.roles = user.roles.map((role: any) =>
          typeof role === "string" ? role : role._id
        );
      }

      // Initialize branch selections from user.branches data
      if (user.branches && Array.isArray(user.branches)) {
        initialValues.branchIds = user.branches.map((branch: any) => branch.id);
      }

      // --- Normalize phone numbers ---
      if (typeof initialValues.phone === 'string') {
        const digits = initialValues.phone.replace(/\D/g, '');
        initialValues.phone = digits.length > 10 ? digits.slice(-10) : digits;
      }
      if (initialValues.emergencyContact && typeof initialValues.emergencyContact.phone === 'string') {
        const digits = initialValues.emergencyContact.phone.replace(/\D/g, '');
        initialValues['emergencyContact.phone'] = digits.length > 10 ? digits.slice(-10) : digits;
      }
      // --- Add emergencyContact fields to initialValues ---
      if (initialValues.emergencyContact && typeof initialValues.emergencyContact === 'object') {
        initialValues['emergencyContact.name'] = initialValues.emergencyContact.name || '';
        initialValues['emergencyContact.phone'] = initialValues.emergencyContact.phone || '';
        initialValues['emergencyContact.relationship'] = initialValues.emergencyContact.relationship || '';
      } else {
        initialValues['emergencyContact.name'] = '';
        initialValues['emergencyContact.phone'] = '';
        initialValues['emergencyContact.relationship'] = '';
      }
      // --- Add socialAssurance fields to initialValues ---
      if (initialValues.socialAssurance && typeof initialValues.socialAssurance === 'object') {
        initialValues['socialAssurance.nas'] = initialValues.socialAssurance.nas || '';
        initialValues['socialAssurance.expiry'] = initialValues.socialAssurance.expiry || '';
      } else {
        initialValues['socialAssurance.nas'] = '';
        initialValues['socialAssurance.expiry'] = '';
      }
      // --- Add taxInfo fields to initialValues ---
      if (initialValues.taxInfo && typeof initialValues.taxInfo === 'object') {
        initialValues['taxInfo.isTaxable'] = initialValues.taxInfo.isTaxable || 'no';
        initialValues['taxInfo.taxtypeid'] = initialValues.taxInfo.taxtypeid || '';
        initialValues['taxInfo.qstRegistrationNumber'] = initialValues.taxInfo.qstRegistrationNumber || '';
        initialValues['taxInfo.tpsRegistrationNumber'] = initialValues.taxInfo.tpsRegistrationNumber || '';
      } else {
        initialValues['taxInfo.isTaxable'] = 'no';
        initialValues['taxInfo.taxtypeid'] = '';
        initialValues['taxInfo.qstRegistrationNumber'] = '';
        initialValues['taxInfo.tpsRegistrationNumber'] = '';
      }

      // --- Add partnerId field to initialValues ---
      if (initialValues.partnerId) {
        initialValues.partnerId = initialValues.partnerId;
      }
      // --- End normalize phone numbers ---

      setValues(initialValues);
      setIsFormInitialized(true);
      console.log('Form initialized with values:', initialValues);

      // Update visible fields based on roles is handled by the useEffect below
    } else if (!user && !isFormInitialized) {
      // For new users, initialize with defaults
      console.log('Initializing form for new user');
      const defaults: Record<string, any> = {};
      standardUserFields.forEach(field => {
        if (field.defaultValue !== undefined) {
          defaults[field.key] = field.defaultValue;
        }
      });
      setValues(defaults);
      setIsFormInitialized(true);
    }
  }, [user, isFormInitialized]);

  // Fetch roles, branches, and tax types
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        setPermissionsLoading(true);
        // Fetch roles
        const rolesResponse = await fetch("/api/roles");
        if (!rolesResponse.ok) throw new Error("Failed to fetch roles");
        const rolesData = await rolesResponse.json();
        setRoles(rolesData);
        // Fetch branches
        const branchesResponse = await fetch("/api/branches");
        if (!branchesResponse.ok) throw new Error("Failed to fetch branches");
        const branchesData = await branchesResponse.json();
        setBranches(branchesData);
        // Fetch tax types
        const taxTypes = await fetchTaxTypes();
        // Filter out tax types with code "amq" and map to UserField option shape
        const mapped = taxTypes
          .filter((taxType) => taxType.code !== "amq")
          .map((taxType) => ({
            value: taxType._id,
            label: {
              en: taxType.names.join(", "),
              fr: taxType.names.join(", "),
            },
            code: taxType.code,
          }));
        setTaxTypeOptions(mapped);
        // Fetch permissions
        const permissionsResponse = await fetch("/api/permissions");
        if (!permissionsResponse.ok) throw new Error("Failed to fetch permissions");
        const permissionsData = await permissionsResponse.json();
        setPermissions(permissionsData);
        // Fetch partners
        setPartnersLoading(true);
        setPartnersError(null);
        try {
          const partnersData = await fetchPartners();
          setPartners(partnersData);
          console.log('Partners fetched successfully:', partnersData.length);
        } catch (partnersErr) {
          console.error('Failed to fetch partners:', partnersErr);
          setPartnersError(partnersErr instanceof Error ? partnersErr.message : 'Failed to fetch partners');
          setPartners([]); // Set empty array on error
        } finally {
          setPartnersLoading(false);
        }
      } catch (error) {
        console.error("Error fetching form data:", error);
        toast({
          title: t("common.error"),
          description: t("common.errorFetchingData"),
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
        setPermissionsLoading(false);
      }
    };
    fetchData();
  }, [toast, t]);

  // Update visible fields when roles change
  useEffect(() => {
    if (!values.roles || !Array.isArray(values.roles) || values.roles.length === 0) {
      setVisibleFields(standardUserFields);
      setRoleSpecificVisibleFields([]);
      return;
    }

    const roleNames = values.roles.map((roleId: string) =>
      roles.find(role => role._id === roleId)?.name || ""
    ).filter((name: string) => name !== "BranchesAgent" && name !== "AgentPartner" && name !== "Teleponiste");

    // Now separate standard fields from role-specific ones
    const allFields = getFieldsForRoles(roleNames);
    const standardFieldKeys = standardUserFields.map(field => field.key);

    const standardFieldsToShow = allFields.filter(field =>
      standardFieldKeys.includes(field.key)
    );

    const roleSpecificFieldsToShow = allFields.filter(field =>
      !standardFieldKeys.includes(field.key)
    );

    // Debug logging for partner role
    if (process.env.NODE_ENV === 'development') {
      console.log('Role names:', roleNames);
      console.log('All fields:', allFields.map(f => f.key));
      console.log('Role-specific fields to show:', roleSpecificFieldsToShow.map(f => f.key));
      console.log('Has Partner role:', roleNames.includes('Partner'));
    }

    setVisibleFields(standardFieldsToShow);
    setRoleSpecificVisibleFields(roleSpecificFieldsToShow);

    // --- Set default values for role-specific fields (including nested fields) ---
    const newDefaults: Record<string, any> = {};
    roleSpecificFieldsToShow.forEach(field => {
      if (field.defaultValue !== undefined && values[field.key] === undefined) {
        newDefaults[field.key] = field.defaultValue;
      }
      // Handle nested fields (e.g., tax-info-group)
      if (Array.isArray(field.fields)) {
        field.fields.forEach(subField => {
          if (subField.defaultValue !== undefined && values[subField.key] === undefined) {
            newDefaults[subField.key] = subField.defaultValue;
          }
        });
      }
    });
    if (Object.keys(newDefaults).length > 0) {
      setValues(prev => ({ ...newDefaults, ...prev }));
    }
    // --- End set default values ---
  }, [values.roles, roles]);

  // After fetching permissions, map user.directPermissions (objects, codes, or IDs) to IDs
  useEffect(() => {
    if (user && permissions.length > 0) {
      let directPermissions: string[] = [];
      if (Array.isArray(user.directPermissions)) {
        if (typeof user.directPermissions[0] === "object" && user.directPermissions[0] !== null) {
          // Array of permission objects
          directPermissions = user.directPermissions.map((p: any) => p._id).filter((id: any): id is string => typeof id === 'string');
        } else if (typeof user.directPermissions[0] === "string") {
          if (user.directPermissions[0].length < 30) {
            // Array of codes
            directPermissions = user.directPermissions
              .map((code: string) => permissions.find(p => p.code === code)?._id)
              .filter((id: any): id is string => typeof id === 'string');
          } else {
            // Array of IDs
            directPermissions = user.directPermissions.filter((id: any): id is string => typeof id === 'string');
          }
        }
      }
      setValues(prev => ({ ...prev, directPermissions }));
    }
  }, [user, permissions]);

  // Handle field changes
  const handleChange = (key: string, value: any) => {
    console.log(`Field changed: ${key} = ${value}`);

    if (key === 'branchIds' && Array.isArray(value)) {
      setValues(prev => ({ ...prev, [key]: Array.from(new Set(value)) }));
    } else if (key === 'directPermissions') {
      setValues(prev => ({ ...prev, directPermissions: value }));
    } else {
      setValues(prev => {
        const newValues = { ...prev, [key]: value };

        // Handle dependent field clearing for tax info
        if (key === 'taxInfo.isTaxable') {
          if (value === 'no') {
            // Clear tax type and registration numbers when not taxable
            newValues['taxInfo.taxtypeid'] = '';
            newValues['taxInfo.qstRegistrationNumber'] = '';
            newValues['taxInfo.tpsRegistrationNumber'] = '';
            console.log('Cleared tax-related fields because isTaxable = no');
          }
        }

        return newValues;
      });
    }

    // Clear the error for this field
    if (errors[key]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[key];
        return newErrors;
      });
    }
  };

  // Validate all fields
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};
    const allFields = [...visibleFields, ...roleSpecificVisibleFields];

    // Only validate visible fields
    allFields.forEach(field => {
      const value = values[field.key];

      // Check required fields
      if (field.required && (value === undefined || value === null || value === "")) {
        newErrors[field.key] = t("validation.required");
        return;
      }

      // Skip validation for empty optional fields
      if (!field.required && (value === undefined || value === null || value === "")) {
        return;
      }

      // Apply Zod validation if defined
      if (field.validation) {
        try {
          field.validation.parse(value);
        } catch (error) {
          if (error instanceof z.ZodError) {
            newErrors[field.key] = error.errors[0]?.message || t("validation.invalid");
          }
        }
      }
    });

    // Check if user has branch-specific roles but no branches selected
    const selectedRoles = values.roles || [];
    if (selectedRoles.length > 0) {
      const roleNames = selectedRoles.map((roleId: string) =>
        roles.find(role => role._id === roleId)?.name || ""
      ).filter(Boolean);

      const hasBranchAdmin = roleNames.includes('BranchesAdmin');
      const hasBranchAgent = roleNames.includes('BranchesAgent');
      const hasSeller = roleNames.includes('Seller');
      const hasPAP = roleNames.includes('PAP');
      // If user has any branch-specific role, they need at least one branch
      if ((hasBranchAdmin || hasBranchAgent || hasSeller || hasPAP) &&
        (!values.branchIds || !Array.isArray(values.branchIds) || values.branchIds.length === 0)) {
        newErrors['branchIds'] = t("validation.branchRequired") || "At least one branch is required for the selected role(s)";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle file selection for single file
  const handleFileSelect = (key: string, file: File | null) => {
    setFileStates(prev => ({ ...prev, [key]: file }));
  };
  // Handle file selection for multiple files
  const handleFilesSelect = (key: string, files: File[]) => {
    setFileStates(prev => ({ ...prev, [key]: files }));
  };

  // Handle contract signing completion
  const handleContractSigned = (contractData: any) => {
    setShowContractDialog(false);
    toast({
      title: 'Contract Signed',
      description: 'Contract has been signed successfully. You can now send it to the user.',
    });

    // Now call the original success callback
    if (onSuccess && createdUser) {
      onSuccess(createdUser);
    }
  };

  // Handle contract dialog close without signing
  const handleContractDialogClose = () => {
    setShowContractDialog(false);
    // Still call success callback even if contract wasn't signed
    if (onSuccess && createdUser) {
      onSuccess(createdUser);
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setApiError(null);
    if (!validateForm()) {
      return;
    }
    try {
      setIsSubmitting(true);
      // Prepare the data for submission
      const userData = { ...values };

      // Clean up any old/wrong keys that might be lingering
      if (userData['socialAssurance.nas.expiration']) {
        delete userData['socialAssurance.nas.expiration'];
      }

      // Remove the branches array if present to avoid conflicts with branchIds
      if (userData.branches) {
        delete userData.branches;
      }

      // Ensure branchIds is properly formatted as an array with unique values
      if (userData.branchIds) {
        userData.branchIds = Array.from(new Set(userData.branchIds));
      }

      // Add role information for branch updates on the backend
      const selectedRoles = userData.roles || [];
      const roleNames = selectedRoles.map((roleId: string) =>
        roles.find(role => role._id === roleId)?.name || ""
      ).filter(Boolean);
      userData.branchRoles = {
        isAdmin: roleNames.includes('BranchesAdmin'),
        isAgent: roleNames.includes('BranchesAgent'),
        isSeller: roleNames.includes('Seller'),
        isPAP: roleNames.includes('PAP')
      };

      // Remove file fields from userData for initial creation
      delete userData['documents.checkSpecimen'];
      delete userData['documents.otherDocuments'];

      // --- Ensure nested objects for emergencyContact, socialAssurance, and taxInfo ---
      // Emergency Contact
      const emergencyContactKeys = Object.keys(userData).filter(k => k.startsWith('emergencyContact.'));
      if (emergencyContactKeys.length > 0) {
        userData.emergencyContact = {};
        for (const key of emergencyContactKeys) {
          const subKey = key.replace('emergencyContact.', '');
          userData.emergencyContact[subKey] = userData[key];
          delete userData[key];
        }
      }
      // Social Assurance (NAS)
      const socialAssuranceKeys = Object.keys(userData).filter(k => k.startsWith('socialAssurance.'));
      if (socialAssuranceKeys.length > 0) {
        userData.socialAssurance = {};
        for (const key of socialAssuranceKeys) {
          const subKey = key.replace('socialAssurance.', '');
          // Fix: Skip any old 'nas.expiration' keys and map them to 'expiry'
          if (subKey === 'nas.expiration') {
            userData.socialAssurance['expiry'] = userData[key];
          } else {
            userData.socialAssurance[subKey] = userData[key];
          }
          delete userData[key];
        }
        // Clean up any potential duplicate or wrong keys
        if (userData.socialAssurance['nas.expiration']) {
          delete userData.socialAssurance['nas.expiration'];
        }
      }
      // Tax Info
      const taxInfoKeys = Object.keys(userData).filter(k => k.startsWith('taxInfo.'));
      if (taxInfoKeys.length > 0) {
        userData.taxInfo = {};
        for (const key of taxInfoKeys) {
          const subKey = key.replace('taxInfo.', '');
          userData.taxInfo[subKey] = userData[key];
          delete userData[key];
        }
      }
      // --- End nested objects ---

      // Ensure directPermissions is an array
      if (!Array.isArray(userData.directPermissions)) {
        userData.directPermissions = [];
      }

      // Debug log for NAS and taxInfo
      console.log('Before transformation - socialAssurance keys:', Object.keys(userData).filter(k => k.startsWith('socialAssurance.')));
      console.log('Before transformation - taxInfo keys:', Object.keys(userData).filter(k => k.startsWith('taxInfo.')));
      console.log('Raw form values before transformation:', JSON.stringify(values, null, 2));
      console.log('Submitting user data:', JSON.stringify(userData, null, 2));

      // API endpoint and method
      const url = user?._id ? `/api/users/dev/${user._id}` : "/api/users/dev";
      const method = user?._id ? "PUT" : "POST";

      console.log("Submitting user data:", JSON.stringify(userData));

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(userData),
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to save user");
      }
      const savedUser = await response.json();

      // Track if component is still mounted
      let isMounted = true;
      // Set up cleanup function
      const cleanup = () => {
        isMounted = false;
      };

      // After user is created, upload files if any
      let fileUploadError = false;
      // Use the correct userId for uploads
      const uploadUserId = savedUser._id || user?._id;
      if (fileStates['documents.checkSpecimen']) {
        setIsUploadingFiles(true);
        const file = fileStates['documents.checkSpecimen'] as File;
        const formData = new FormData();
        formData.append('file', file);
        formData.append('type', 'checkSpecimen');
        formData.append('userId', uploadUserId);
        try {
          const res = await fetch('/api/users/dev/upload', {
            method: 'POST',
            body: formData,
          });
          const data = await res.json();
          if (!(res.ok && data.reference)) {
            fileUploadError = true;
            toast({ title: t('common.error'), description: data.error || 'Erreur lors de l\'upload du chèque', variant: 'destructive' });
          }
        } finally {
          setIsUploadingFiles(false);
        }
      }
      if (fileStates['documents.otherDocuments'] && Array.isArray(fileStates['documents.otherDocuments'])) {
        setIsUploadingFiles(true);
        const files = fileStates['documents.otherDocuments'] as File[];
        for (const file of files) {
          const formData = new FormData();
          formData.append('file', file);
          formData.append('type', 'otherDocuments');
          formData.append('userId', uploadUserId);
          const res = await fetch('/api/users/dev/upload', {
            method: 'POST',
            body: formData,
          });
          const data = await res.json();
          if (!(res.ok && data.reference)) {
            fileUploadError = true;
            toast({ title: t('common.error'), description: data.error || 'Erreur lors de l\'upload d\'un document', variant: 'destructive' });
          }
        }
        setIsUploadingFiles(false);
      }
      // No PATCH request needed, upload route updates user docs
      if (!fileUploadError) {
        toast({
          title: t("common.success"),
          description: user?._id
            ? t("users.userUpdated")
            : t("users.userCreated"),
        });
        setFileStates({}); // Reset file state

        // Check if user requires contract and this is a new user creation
        if (!user?._id && savedUser && doesUserRequireContract(savedUser.roles)) {
          setCreatedUser(savedUser);
          setShowContractDialog(true);
          // Don't call onSuccess immediately - wait for contract signing
        } else {
          // For updates or users that don't require contracts, proceed normally
          if (isMounted && onSuccess) {
            onSuccess(savedUser);
            // Cleanup after callback to prevent potential further state updates
            cleanup();
          }
        }
      }
    } catch (error) {
      console.error("Error saving user:", error);
      setApiError(
        error instanceof Error ? error.message : String(error) || "An error occurred"
      );
    } finally {
      if (setIsSubmitting) {
        setIsSubmitting(false);
      }
      setIsUploadingFiles(false);
    }
  };

  // Inject taxTypeOptions into PAP role fields and partner options into Partner role fields before rendering
  const patchedRoleSpecificFields = roleSpecificVisibleFields.map(field => {
    if (field.key === 'taxInfo.group' && Array.isArray(field.fields)) {
      return {
        ...field,
        fields: field.fields.map(subField =>
          subField.key === 'taxInfo.taxtypeid'
            ? { ...subField, options: taxTypeOptions }
            : subField
        ),
      };
    }
    if (field.key === 'partnerId') {
      let partnerOptions: any[] = [];
      let fieldPlaceholder = field.placeholder;

      if (partnersLoading) {
        // Show loading state
        partnerOptions = [{
          value: '',
          label: {
            en: 'Loading partners...',
            fr: 'Chargement des partenaires...',
          },
          disabled: true
        }];
      } else if (partnersError) {
        // Show error state
        partnerOptions = [{
          value: '',
          label: {
            en: `Error: ${partnersError}`,
            fr: `Erreur: ${partnersError}`,
          },
          disabled: true
        }];
      } else if (partners.length === 0) {
        // Show no partners available
        partnerOptions = [{
          value: '',
          label: {
            en: 'No partners available',
            fr: 'Aucun partenaire disponible',
          },
          disabled: true
        }];
      } else {
        // Show actual partner options
        partnerOptions = partners.map(partner => ({
          value: partner._id,
          label: {
            en: partner.name || 'Unnamed Partner',
            fr: partner.name || 'Partenaire sans nom',
          },
        }));
      }

      // Debug logging for partner options
      if (process.env.NODE_ENV === 'development') {
        console.log('Partner state:', {
          partnersLoading,
          partnersError,
          partnersCount: partners.length,
          optionsCount: partnerOptions.length
        });
        console.log('Injecting partner options:', partnerOptions);
      }

      return {
        ...field,
        options: partnerOptions,
        placeholder: fieldPlaceholder,
      };
    }
    return field;
  });

  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className={`flex flex-col ${className}`}>
      {/* Sticky Header (Errors) */}
      <div className="pb-2">
        {apiError && (
          <Alert variant="destructive" className="mb-4">
            <AlertDescription>{apiError}</AlertDescription>
          </Alert>
        )}
        {isUploadingFiles && (
          <Alert className="mb-4">
            <Loader2 className="inline-block mr-2 h-4 w-4 animate-spin text-primary" />
            {t('users.uploadingDocuments') || 'Uploading documents...'}
          </Alert>
        )}
      </div>

      {/* Content */}
      <div className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-12 gap-4">
          {/* Standard Fields */}
          <div className="md:col-span-6">
            <h3 className="text-lg font-medium mb-3">{t('users.basicInfo') || "Basic Information"}</h3>
            {/* Render roles field first */}
            <UserFormFields
              fields={visibleFields.filter(f => f.key === 'roles')}
              values={values}
              errors={errors}
              onChange={handleChange}
              options={{ roles, branches }}
              fileStates={fileStates}
              onFileSelect={handleFileSelect}
              onFilesSelect={handleFilesSelect}
            />
            {/* Render branches field second */}
            <UserFormFields
              fields={visibleFields.filter(f => f.key === 'branchIds')}
              values={values}
              errors={errors}
              onChange={handleChange}
              options={{ roles, branches }}
              fileStates={fileStates}
              onFileSelect={handleFileSelect}
              onFilesSelect={handleFilesSelect}
            />
            {/* Render the rest of the fields */}
            <UserFormFields
              fields={visibleFields.filter(f => f.key !== 'roles' && f.key !== 'branchIds')}
              values={values}
              errors={errors}
              onChange={handleChange}
              options={{ roles, branches }}
              fileStates={fileStates}
              onFileSelect={handleFileSelect}
              onFilesSelect={handleFilesSelect}
            />
            {/* Direct Permissions for SuperAdmins (redux user) */}
            {isSuperAdmin && permissions.length > 0 && (
              <div className="mt-6">
                <label className="block mb-2 font-medium">{t('users.directPermissions') || 'Direct Permissions'}</label>
                <DirectPermissionsField
                  permissions={permissions.filter(p => p.code && p.code.startsWith('ACCESS_'))}
                  value={values.directPermissions || []}
                  onChange={(val) => handleChange('directPermissions', val)}
                  loading={permissionsLoading}
                />
              </div>
            )}
          </div>

          {/* Role-specific Fields */}
          {roleSpecificVisibleFields.length > 0 && (
            <>
              <div className="hidden md:block md:col-span-1">
                <Separator orientation="vertical" className="h-full mx-auto" />
              </div>

              <div className="md:hidden">
                <Separator className="my-4" />
              </div>

              <div className="md:col-span-5">
                <h3 className="text-lg font-medium mb-4">{t('users.roleSpecificInfo') || "Role-Specific Information"}</h3>
                <UserFormFields
                  fields={patchedRoleSpecificFields}
                  values={values}
                  errors={errors}
                  onChange={handleChange}
                  options={{ roles, branches }}
                  fileStates={fileStates}
                  onFileSelect={handleFileSelect}
                  onFilesSelect={handleFilesSelect}
                />
              </div>
            </>
          )}
        </div>
      </div>

      {/* Sticky Footer (Buttons) */}
      <div className="pt-4 mt-2 border-t flex justify-end space-x-2 sticky bottom-0 bg-background">
        {onCancel && (
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isSubmitting || isUploadingFiles}
          >
            {t("common.cancel")}
          </Button>
        )}

        <Button type="submit" disabled={isSubmitting || isUploadingFiles}>
          {(isSubmitting || isUploadingFiles) && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          {user?._id ? t("common.update") : t("common.create")}
        </Button>
      </div>

      {/* Debug Panel for Development */}
      {process.env.NODE_ENV === 'development' && (
        <div className="mt-4 border rounded-md bg-muted/20 p-4">
          <h4 className="text-sm font-semibold mb-2">Debug Information</h4>
          <div className="space-y-2 text-xs">
            <div>
              <span className="font-medium">Partners:</span> {partners.length} loaded
              {partnersLoading && <span className="text-blue-500 ml-2">Loading...</span>}
              {partnersError && <span className="text-red-500 ml-2">Error: {partnersError}</span>}
            </div>
            <div>
              <span className="font-medium">Role-specific fields:</span> {roleSpecificVisibleFields.map(f => f.key).join(', ') || 'None'}
            </div>
            <div>
              <span className="font-medium">Selected roles:</span> {
                (values.roles || []).map((roleId: string) =>
                  roles.find(role => role._id === roleId)?.name || roleId
                ).join(', ') || 'None'
              }
            </div>
          </div>
        </div>
      )}

      {/* Contract Debug Panel for Development */}
      {user?._id && (
        <ContractDebugPanel userId={user._id} />
      )}

      {/* Contract Signing Dialog */}
      {showContractDialog && createdUser && (
        <ContractSigningDialog
          isOpen={showContractDialog}
          onClose={handleContractDialogClose}
          user={createdUser}
          onContractSigned={handleContractSigned}
        />
      )}
    </form>
  );
}