import { NextResponse } from 'next/server';
import mongoose from 'mongoose';
import Branch from '@/models/Branch';  // Import Branch model
import { getServerSession } from "next-auth/next"; // Import next-auth session
import { authOptions } from "@/lib/auth"; // Import auth options
import { normalizePhoneNumber } from '@/lib/twilio';
import Reservation from '@/models/Reservation';
import { RESERVATION_PERMISSIONS } from '@/types/permission-codes';
import dbConnect from '@/lib/db';
import { getUserPermissions } from '../../utils/server-permission-utils';
import Appointment from '@/models/Appointment'; // Import Appointment model
import Contact from '@/app/models/Contact';
import { ReservationAuditLogger } from '@/lib/utils/audit-utils';
import { transferPartnerCommissions } from '../../utils/commission-utils';
import { ObjectId } from 'mongodb';
import ReservationNote from '@/models/ReservationNote';
import User from '@/models/User';
// Define the Reservation schema to match the model
const ReservationSchema = new mongoose.Schema({
  appointmentId: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
  },
  partnerId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  type: {
    type: String,
    enum: ['branch', 'online', 'home', 'family'],
    default: 'branch'
  },
  status: {
    type: String,
    required: true,
    default: 'pending'
  },
  source: {
    type: String,
    enum: ['invitation', 'direct', 'other', 'amq_website'],
    default: 'direct'
  },
  customerInfo: {
    client1Name: { type: String, required: true },
    hasCompanion: { type: Boolean, default: false },
    client2Name: String,
    city: { type: String},
    postalCode: { type: String, required: true },
    address: String,
    phone: { type: String, required: true },
    phone2: String,
    email: { type: String, required: true },
    isPostalCodeValid: { type: Boolean, default: false }
  },
  preferences: {
    preferredLanguage: { type: String, default: 'fr' },
    allergies: String,
    hasChildren: { type: Boolean, default: false },
    childrenAges: {
      age0to5: { type: Number, default: 0 },
      age6to12: { type: Number, default: 0 },
      age13to17: { type: Number, default: 0 }
    },
    branchId: { type: mongoose.Schema.Types.ObjectId, ref: 'Branch' },
    visitDate: String,
    visitTime: String
  }
}, {
  timestamps: true
});

// Create or get the model
const ReservationModel = mongoose.models.Reservation || mongoose.model('Reservation', ReservationSchema);

interface RouteParams {
  params: Promise<{ id: string }>;
}

// Field labels for human-readable change descriptions
const FIELD_LABELS: Record<string, string> = {
  'customerInfo.client1Name': 'Client Name',
  'customerInfo.client2Name': 'Companion Name',
  'customerInfo.hasCompanion': 'Has Companion',
  'customerInfo.city': 'City',
  'customerInfo.postalCode': 'Postal Code',
  'customerInfo.address': 'Address',
  'customerInfo.phone': 'Phone',
  'customerInfo.phone2': 'Alternative Phone',
  'customerInfo.email': 'Email',
  'preferences.preferredLanguage': 'Preferred Language',
  'preferences.allergies': 'Allergies',
  'preferences.hasChildren': 'Has Children',
  'preferences.childrenAges.age0to5': 'Children Ages 0-5',
  'preferences.childrenAges.age6to12': 'Children Ages 6-12',
  'preferences.childrenAges.age13to17': 'Children Ages 13-17',
  'preferences.branchId': 'Branch',
  'preferences.visitDate': 'Visit Date',
  'preferences.visitTime': 'Visit Time',
  'partnerId': 'Partner',
  'appointmentId': 'Appointment',
  'type': 'Type',
  'status': 'Status'
};

// Function to generate human-readable change description
async function generateChangeDescription(oldData: any, newData: any): Promise<string> {
  const changes: string[] = [];

  // Helper function to get nested value
  const getValue = (obj: any, path: string) => {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  };

  // Helper function to format values for display
  const formatValue = async (value: any, fieldPath: string) => {
    if (value === null || value === undefined || value === '') return 'empty';
    if (typeof value === 'boolean') return value ? 'Yes' : 'No';
    if (typeof value === 'object' && value._id) return value._id;

    // Special formatting for specific fields
    if (fieldPath === 'preferences.branchId' && mongoose.Types.ObjectId.isValid(value)) {
      try {
        const branch = await Branch.findById(value).select('name');
        return branch?.name || value;
      } catch (error) {
        return value;
      }
    }

    if (fieldPath === 'partnerId' && mongoose.Types.ObjectId.isValid(value)) {
      try {
        const user = await User.findById(value).select('name');
        return user?.name || value;
      } catch (error) {
        return value;
      }
    }

    if (fieldPath === 'preferences.visitDate' && value) {
      try {
        const date = new Date(value);
        return date.toLocaleDateString('en-CA'); // YYYY-MM-DD format
      } catch (error) {
        return value;
      }
    }

    return value.toString();
  };

  // Check all possible fields for changes
  for (const fieldPath of Object.keys(FIELD_LABELS)) {
    const oldValue = getValue(oldData, fieldPath);
    const newValue = getValue(newData, fieldPath);

    // Compare values (handle ObjectIds specially)
    let hasChanged = false;
    if (mongoose.Types.ObjectId.isValid(oldValue) && mongoose.Types.ObjectId.isValid(newValue)) {
      hasChanged = oldValue.toString() !== newValue.toString();
    } else {
      // Handle null/undefined/empty string comparisons more carefully
      const normalizedOld = oldValue === null || oldValue === undefined || oldValue === '' ? null : oldValue;
      const normalizedNew = newValue === null || newValue === undefined || newValue === '' ? null : newValue;
      hasChanged = normalizedOld !== normalizedNew;
    }

    if (hasChanged) {
      const label = FIELD_LABELS[fieldPath];
      const oldFormatted = await formatValue(oldValue, fieldPath);
      const newFormatted = await formatValue(newValue, fieldPath);

      // Skip if both formatted values are the same (e.g., both "empty")
      if (oldFormatted === newFormatted) {
        continue;
      }

      if (oldFormatted === 'empty') {
        changes.push(`${label} set to "${newFormatted}"`);
      } else if (newFormatted === 'empty') {
        changes.push(`${label} cleared (was "${oldFormatted}")`);
      } else {
        changes.push(`${label} changed from "${oldFormatted}" to "${newFormatted}"`);
      }
    }
  }

  if (changes.length === 0) {
    return 'Reservation updated (no significant changes detected)';
  }

  if (changes.length === 1) {
    return changes[0];
  }

  // Show all changes, separated by semicolons for better readability
  return changes.join('; ');
}

export async function GET(
  request: Request,
  { params }: RouteParams
) {
  const { id } = await params;

  if (!id) {
    return NextResponse.json(
      { error: 'Reservation ID is required' },
      { status: 400 }
    );
  }

  try {
    await dbConnect();

    const reservation = await ReservationModel.findById(id)
      .populate({
        path: 'preferences.branchId',
        select: 'name',
        model: Branch
      })
      .populate({
        path: 'partnerId',
        select: 'name email',
        model: 'User'
      })
      .lean();

    if (!reservation) {
      return NextResponse.json(
        { error: 'Reservation not found' },
        { status: 404 }
      );
    }

    // Log audit event for reservation viewing
    try {
      await ReservationAuditLogger.logReservationViewed(
        request as any, // Cast to NextRequest for audit logging
        (reservation as any)._id.toString(),
        (reservation as any).customerInfo.client1Name
      );
    } catch (auditError) {
      console.error('Failed to log reservation view audit:', auditError);
      // Don't fail the request if audit logging fails
    }

    return NextResponse.json(reservation);
  } catch (error) {
    console.error('GET reservation error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch reservation' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: Request,
  { params }: RouteParams
) {
  const { id } = await params;

  if (!id) {
    return NextResponse.json(
      { error: 'Reservation ID is required' },
      { status: 400 }
    );
  }

  try {
    await dbConnect();

    const session = await getServerSession();
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();

    // First, retrieve the existing reservation to check if phone number is changing
    // TypeScript doesn't understand what findById().lean() returns exactly, so we use any
    const existingReservation: any = await ReservationModel.findById(id).lean();



    if (!existingReservation) {
      return NextResponse.json(
        { error: 'Reservation not found' },
        { status: 404 }
      );
    }

    // Check if the phone number is being changed
    const existingPhone = normalizePhoneNumber(existingReservation?.customerInfo?.phone);
    const newPhone = normalizePhoneNumber(body.customerInfo?.phone);

    // Only check for duplicates if the phone number is actually changing
    if (newPhone && existingPhone !== newPhone) {

      //update contact
      const contact = await Contact.findOne({
        phone: existingPhone
      });
      if (contact) {
        contact.phone = newPhone;
        await contact.save();
      }


      // Check for existing reservations with the same phone (excluding current reservation)
      const duplicateReservation = await ReservationModel.findOne({
        _id: { $ne: id }, // Exclude the current reservation
        'customerInfo.phone': newPhone
      }).lean();

      if (duplicateReservation) {
        return NextResponse.json(
          { error: "A reservation with this phone number already exists." },
          { status: 409 }
        );
      }
    }

    // --- APPOINTMENT CAPACITY LOGIC ---
    // If appointmentId changes, update contact
    const oldAppointmentId = existingReservation.appointmentId?.toString();
    const newAppointmentId = body.appointmentId?.toString();
    const appointmentChanged = oldAppointmentId && newAppointmentId && oldAppointmentId !== newAppointmentId;

    if(appointmentChanged) {
      //update contact
      const contact = await Contact.findOne({
        phone: existingPhone
      });
      if (contact) {
        const newAppointment = await Appointment.findById(newAppointmentId);
        contact.conversation.linkedBranch=newAppointment?.branchId;
        await contact.save();
      }
    }


    // Process and normalize data before saving
    if (body.customerInfo) {
      body.customerInfo.phone = normalizePhoneNumber(body.customerInfo.phone);
      if (body.customerInfo.phone2) {
        body.customerInfo.phone2 = normalizePhoneNumber(body.customerInfo.phone2);
      }
    }

    // Handle allergies - ensure it's a string, not an array
    if (body.preferences && body.preferences.allergies) {
      // If allergies is an array, join it into a string
      if (Array.isArray(body.preferences.allergies)) {
        body.preferences.allergies = body.preferences.allergies.join(', ');
      }
    }

    const updatedReservation = await ReservationModel.findByIdAndUpdate(
      id,
      body,
      { new: true }
    )
    .populate({
      path: 'preferences.branchId',
      select: 'name',
      model: Branch
    })
    .populate({
      path: 'partnerId',
      select: 'name email',
      model: 'User'
    });

    if (!updatedReservation) {
      return NextResponse.json(
        { error: 'Failed to update reservation' },
        { status: 500 }
      );
    }

    // Generate and add reservation note for changes
    try {
      const changeDescription = await generateChangeDescription(existingReservation, updatedReservation.toObject());
    
      // Only create a note if there are meaningful changes
      if (!changeDescription.includes('no significant changes detected')) {
        await ReservationNote.create({
          reservationId: new mongoose.Types.ObjectId(id),
          userId: new mongoose.Types.ObjectId(session.user.id),
          content: changeDescription,
          createdAt: new Date(),
          updatedAt: new Date()
        });
      }
    } catch (noteError) {
      console.error('Failed to create reservation note for changes:', noteError);
      // Don't fail the request if note creation fails
    }

    // Log audit event for reservation update
    try {
      await ReservationAuditLogger.logReservationUpdated(
        request as any, // Cast to NextRequest for audit logging
        updatedReservation._id.toString(),
        updatedReservation.customerInfo.client1Name,
        existingReservation,
        updatedReservation.toObject(),
        session
      );
    } catch (auditError) {
      console.error('Failed to log reservation update audit:', auditError);
      // Don't fail the request if audit logging fails
    }

    return NextResponse.json(updatedReservation);
  } catch (error) {
    console.error('Update reservation error:', error);
    return NextResponse.json(
      { error: 'Failed to update reservation', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: Request,
  context: { params: { id: string } }
) {
  const { id } = context.params;
  const session = await getServerSession(authOptions);
  if(!session?.user) {
    return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
  }
  if(session && !session.user.permissions){
    session.user.permissions = await getUserPermissions(session);
  }
  if(!session.user.permissions.includes(RESERVATION_PERMISSIONS.DELETE_RESERVATIONS)) {
    return NextResponse.json({ message: 'Forbidden' }, { status: 403 });
  }
  if (!id || typeof id !== 'string') {
    return NextResponse.json({ message: 'Invalid reservation ID' }, { status: 400 });
  }

  try {
    console.log('Connecting to database and attempting soft delete...');
    await dbConnect();

    // First, ensure the document exists
    const exists = await Reservation.findById(id);
    if (!exists) {
      return NextResponse.json({ message: 'Reservation not found' }, { status: 404 });
    }

    if (exists.isDeleted) {
      return NextResponse.json({ message: 'Reservation is already deleted' }, { status: 400 });
    }

    // Force add the soft delete fields using raw MongoDB operation
    console.log('Soft deleting reservation:', id);

    if (!mongoose.connection.readyState || !mongoose.connection.db) {
      throw new Error('Database connection not established');
    }

    const collection = mongoose.connection.db.collection('reservations');
    const objectId = new mongoose.Types.ObjectId(id);

    // Perform the update
    const updateResult = await collection.updateOne(
      { _id: objectId },
      {
        $set: {
          isDeleted: true,
          deletedAt: new Date()
        }
      },
      { upsert: false }
    );

    console.log('Update operation result:', updateResult);

    if (!updateResult.acknowledged || updateResult.modifiedCount === 0) {
      throw new Error('Failed to soft delete reservation');
    }

    // Verify the update by fetching the document
    const updatedDoc = await collection.findOne({ _id: objectId });

    console.log('Updated document:', updatedDoc);

    if (!updatedDoc) {
      throw new Error('Failed to verify update - document not found');
    }

    // Log audit event for reservation deletion
    try {
      await ReservationAuditLogger.logReservationDeleted(
        request as any, // Cast to NextRequest for audit logging
        updatedDoc._id.toString(),
        exists.customerInfo.client1Name,
        session
      );
    } catch (auditError) {
      console.error('Failed to log reservation deletion audit:', auditError);
      // Don't fail the request if audit logging fails
    }

    return NextResponse.json({
      message: 'Reservation soft deleted successfully',
      debug: 'Fields added using raw MongoDB operation',
      reservation: {
        id: updatedDoc._id.toString(),
        isDeleted: Boolean(updatedDoc.isDeleted),
        deletedAt: updatedDoc.deletedAt ? new Date(updatedDoc.deletedAt).toISOString() : null
      }
    }, { status: 200 });

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('Error soft deleting reservation:', {
      error: errorMessage,
      reservationId: id,
      userId: session.user.id
    });

    return NextResponse.json({
      message: 'Internal Server Error',
      error: errorMessage
    }, { status: 500 });
  }
}

export async function PATCH(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    await dbConnect();

    const body = await request.json();
    const { partnerId } = body;

    if (!partnerId) {
      return NextResponse.json(
        { error: 'Partner ID is required' },
        { status: 400 }
      );
    }

    // Get session for notes
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the existing reservation to check if partnerId is changing
    const existingReservation = await ReservationModel.findById(params.id).lean();

    if (!existingReservation) {
      return NextResponse.json(
        { error: 'Reservation not found' },
        { status: 404 }
      );
    }

    const oldPartnerId = (existingReservation as any).partnerId?.toString();
    const newPartnerId = partnerId.toString();

    // Check if partnerId is actually changing
    const isPartnerChanging = oldPartnerId && oldPartnerId !== newPartnerId;

    // If partner is changing, handle commission transfer
    let transferResult = null;
    if (isPartnerChanging) {
      await dbConnect();
      const db = mongoose.connection;
      transferResult = await transferPartnerCommissions(
        db,
        params.id,
        oldPartnerId,
        newPartnerId
      );
    }

    // Update the reservation
    const reservation = await ReservationModel.findByIdAndUpdate(
      params.id,
      {
        $set: {
          partnerId,
        }
      },
      { new: true }
    );

    if (!reservation) {
      return NextResponse.json(
        { error: 'Reservation not found' },
        { status: 404 }
      );
    }

    // Create a note about the partner change and commission transfer
    if (isPartnerChanging && transferResult) {
      const noteContent = transferResult.transferredCount > 0
        ? `Partner changed from ${transferResult.oldPartnerName} to ${transferResult.newPartnerName}. ${transferResult.transferredCount} commission(s) transferred to new partner.`
        : `Partner changed from ${transferResult.oldPartnerName} to ${transferResult.newPartnerName}. No commissions to transfer.`;

      await ReservationNote.create({
        reservationId: ObjectId.createFromHexString(params.id),
        userId: ObjectId.createFromHexString(session.user.id),
        content: noteContent,
        createdAt: new Date(),
        updatedAt: new Date()
      });
    }

    // Log audit event for reservation partner assignment
    try {
      await ReservationAuditLogger.logReservationUpdated(
        request as any, // Cast to NextRequest for audit logging
        reservation._id.toString(),
        reservation.customerInfo?.client1Name || 'Unknown Customer',
        { partnerId: oldPartnerId }, // Original data
        { partnerId: newPartnerId }, // Updated data
        session
      );
    } catch (auditError) {
      console.error('Failed to log reservation partner assignment audit:', auditError);
      // Don't fail the request if audit logging fails
    }

    return NextResponse.json(reservation);
  } catch (error) {
    console.error('Error updating reservation:', error);
    return NextResponse.json(
      { error: 'Failed to update reservation' },
      { status: 500 }
    );
  }
}