"use client";

import React from "react";
import { useLanguage } from "@/lib/contexts/language-context";
import { useState, useEffect, useMemo, useRef } from "react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

interface User {
  _id: string;
  name: string;
  email: string;
  taxInfo?: {
    isTaxable: 'yes' | 'no';
    taxtypeid?: string;
  };
}

interface Invoice {
  _id: string;
  userId: string;
  invoiceNumber: string;
  status: 'nouveau' | 'verifie' | 'envoye' | 'signe' | 'en_traitement' | 'paye' | 'en_retard';
  title: string;
  date: string;
  subtotal: number;
  tax: number;
  total: number;
  startDate?: string;
  endDate?: string;
  taxTypeId: string;
}

interface CreateInvoiceFormProps {
  onInvoiceCreated: (id: string, invoiceNumber: string) => void;
  existingInvoice?: Invoice;
}

interface DropdownSearchInputProps {
  value: string;
  onChange: (v: string) => void;
  isOpen: boolean;
  autoFocus?: boolean;
  placeholder?: string;
}

const DropdownSearchInput: React.FC<DropdownSearchInputProps> = ({ value, onChange, isOpen, autoFocus, placeholder }) => {
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (isOpen && autoFocus && inputRef.current) {
      // Small delay to ensure the dropdown is fully rendered
      const timer = setTimeout(() => {
        if (inputRef.current) {
          inputRef.current.focus();
        }
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [isOpen, autoFocus]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    onChange(newValue);

    // Prevent losing focus by immediately refocusing
    setTimeout(() => {
      if (inputRef.current) {
        inputRef.current.focus();
      }
    }, 0);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    // Prevent ALL keyboard events from bubbling to the Select component
    e.stopPropagation();

    // Only allow basic text input and navigation within the input
    if (e.key === 'Escape') {
      // Allow escape to close the dropdown
      return;
    }

    // Prevent default behavior for keys that might trigger Select navigation
    if (['ArrowDown', 'ArrowUp', 'Enter', 'Space', 'Home', 'End', 'PageUp', 'PageDown'].includes(e.key)) {
      e.preventDefault();
    }
  };

  const handleFocus = () => {
    // Ensure we stay focused
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    // Prevent blur if it's caused by hovering or clicking on dropdown content
    const relatedTarget = e.relatedTarget as HTMLElement;
    if (relatedTarget && (
      relatedTarget.closest('[role="option"]') ||
      relatedTarget.closest('[role="listbox"]') ||
      relatedTarget.closest('[data-radix-select-content]') ||
      relatedTarget.closest('[data-radix-popper-content-wrapper]')
    )) {
      e.preventDefault();
      return;
    }

    // Also prevent blur if mouse is over dropdown content (even if relatedTarget is null)
    const dropdownContent = document.querySelector('[data-radix-select-content]');
    if (dropdownContent) {
      e.preventDefault();
      // Restore focus after a brief delay
      setTimeout(() => {
        if (inputRef.current && !document.querySelector('[data-radix-select-content]:hover')) {
          inputRef.current.focus();
        }
      }, 50);
    }
  };

  return (
    <input
      ref={inputRef}
      value={value}
      onChange={handleChange}
      onKeyDown={handleKeyDown}
      onFocus={handleFocus}
      onBlur={handleBlur}
      placeholder={placeholder || 'Search...'}
      className="h-8 px-3 py-1 text-sm border border-input bg-background rounded-md shadow-none w-full focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
      autoFocus={false}
      autoComplete="off"
      type="text"
    />
  );
};

const invoiceFormSchema = z.object({
  title: z.string().min(1, { message: "Title is required" }),
  date: z.date(),
  status: z.enum(["nouveau", "verifie", "envoye", "signe", "en_traitement", "paye", "en_retard"]),
  userId: z.string().min(1, { message: "Beneficiary is required" }),
  taxTypeId: z.string().min(1, { message: "Tax type is required" }),
});

type InvoiceFormValues = z.infer<typeof invoiceFormSchema>;

export function CreateInvoiceForm({ onInvoiceCreated, existingInvoice }: CreateInvoiceFormProps) {
  const { t } = useLanguage();
  const [isLoading, setIsLoading] = useState(false);
  const [users, setUsers] = useState<User[]>([]);
  const [error, setError] = useState<string | null>(null);
  const isEditing = !!existingInvoice;
  const [mounted, setMounted] = useState(false);
  const [usersLoaded, setUsersLoaded] = useState(false);
  const [taxTypes, setTaxTypes] = useState<any[]>([]);
  const [taxTypesLoading, setTaxTypesLoading] = useState(false);
  const [taxTypesError, setTaxTypesError] = useState<string | null>(null);
  const [userSearchTerm, setUserSearchTerm] = useState('');
  const [userDropdownOpen, setUserDropdownOpen] = useState(false);

  // Filter users based on search term
  const filteredUsers = useMemo(() => {
    if (!userSearchTerm) return users;
    return users.filter(user =>
      user.name.toLowerCase().includes(userSearchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(userSearchTerm.toLowerCase())
    );
  }, [users, userSearchTerm]);

  // Clear search when dropdown closes (but not if it was already cleared by selection)
  useEffect(() => {
    if (!userDropdownOpen && userSearchTerm) {
      // Only clear if there's still a search term (meaning dropdown was closed without selection)
      const timer = setTimeout(() => {
        setUserSearchTerm('');
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [userDropdownOpen, userSearchTerm]);

  // Initialize form with default empty values to prevent hydration mismatch
  const form = useForm<InvoiceFormValues>({
    resolver: zodResolver(invoiceFormSchema),
    defaultValues: {
      title: "",
      date: new Date(),
      status: "nouveau",
      userId: "",
      taxTypeId: "",
    },
  });

  // Client-side only effect to handle mounted state
  useEffect(() => {
    setMounted(true);
  }, []);

  // Update form with existingInvoice data only after component is mounted and users are loaded
  useEffect(() => {
    if (mounted && existingInvoice && usersLoaded && taxTypes.length > 0) {
      form.reset({
        title: existingInvoice.title,
        date: new Date(existingInvoice.date),
        status: existingInvoice.status,
        userId: existingInvoice.userId,
        taxTypeId: existingInvoice.taxTypeId || "",
      });
    }
  }, [existingInvoice, form, mounted, usersLoaded, taxTypes]);

  useEffect(() => {
    // Fetch users for the dropdown (only on client-side)
    if (mounted) {
      fetch("/api/users?includeDeleted=true")
        .then(async (res) => {
          if (!res.ok) throw new Error(await res.text());
          return res.json();
        })
        .then((data) => {
          if (data.users) {
            setUsers(data.users);
            setUsersLoaded(true);
          }
        })
        .catch((err) => {
          console.error("Error fetching users:", err);
          setError("Failed to load users");
          setUsersLoaded(true);
        });
    }
  }, [mounted]);

  // Fetch tax types for the dropdown
  useEffect(() => {
    setTaxTypesLoading(true);
    setTaxTypesError(null);
    fetch("/api/tax-types")
      .then(async (res) => {
        if (!res.ok) throw new Error(await res.text());
        return res.json();
      })
      .then((data) => {
        setTaxTypes(data.taxTypes || []);
      })
      .catch(() => {
        setTaxTypesError("Failed to load tax types");
      })
      .finally(() => setTaxTypesLoading(false));
  }, []);

  const onSubmit = async (values: InvoiceFormValues) => {
    setIsLoading(true);
    setError(null);
    try {
      if (isEditing && existingInvoice) {
        // Update existing invoice
        const response = await fetch(`/api/billing/invoices/${existingInvoice._id}`, {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(values),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || "Failed to update invoice");
        }

        const invoice = await response.json();
        onInvoiceCreated(invoice._id, invoice.invoiceNumber);
      } else {
        // Create new invoice
        const invoiceData = {
          ...values,
          subtotal: 0,
          tax: 0,
          total: 0,
          manualItems: [],
          commissions: [],
        };

        const response = await fetch("/api/billing/invoices", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(invoiceData),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || "Failed to create invoice");
        }

        const invoice = await response.json();
        onInvoiceCreated(invoice._id, invoice.invoiceNumber);
      }
    } catch (err: any) {
      setError(err.message || "An error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  if (!mounted) {
    return (
      <div className="space-y-6 animate-pulse">
        <div className="h-12 bg-muted rounded"></div>
        <div className="h-12 bg-muted rounded"></div>
        <div className="h-12 bg-muted rounded"></div>
        <div className="h-12 bg-muted rounded"></div>
        <div className="h-10 w-20 bg-muted rounded mt-6"></div>
      </div>
    );
  }

  return (
    <Card className="p-0 border-0 shadow-none">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <FormField
            control={form.control}
            name="title"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t("billing.invoiceTitle") || "Title"}</FormLabel>
                <FormControl>
                  <Input placeholder="Invoice Title" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="date"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>{t("billing.date") || "Date"}</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant={"outline"}
                        className={cn(
                          "w-full pl-3 text-left font-normal",
                          !field.value && "text-muted-foreground"
                        )}
                      >
                        {field.value ? (
                          format(field.value, "PPP")
                        ) : (
                          <span>Pick a date</span>
                        )}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={field.value}
                      onSelect={field.onChange}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="status"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t("common.status") || "Status"}</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  value={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="nouveau">{t("billing.status.nouveau") || "Nouveau"}</SelectItem>
                    <SelectItem value="verifie">{t("billing.status.verifie") || "Vérifié"}</SelectItem>
                    <SelectItem value="envoye">{t("billing.status.envoye") || "Envoyé"}</SelectItem>
                    <SelectItem value="signe">{t("billing.status.signe") || "Signé"}</SelectItem>
                    <SelectItem value="en_traitement">{t("billing.status.en_traitement") || "En traitement"}</SelectItem>
                    <SelectItem value="paye">{t("billing.status.paye") || "Payé"}</SelectItem>
                    <SelectItem value="en_retard">{t("billing.status.en_retard") || "En Retard"}</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="userId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t("billing.beneficiary") || "Beneficiary"}</FormLabel>
                <div onKeyDown={(e) => {
                  // If search input is focused, prevent Select from handling keyboard events
                  if (document.activeElement?.tagName === 'INPUT') {
                    e.stopPropagation();
                  }
                }}>
                  <Select
                    onValueChange={val => {
                      // Immediately close dropdown and clear search to prevent flickering
                      setUserDropdownOpen(false);
                      setUserSearchTerm('');

                      field.onChange(val);
                      // Set taxTypeId immediately when user changes
                      const selectedUser = users.find(u => u._id === val);
                      if (selectedUser && selectedUser.taxInfo) {
                        if (selectedUser.taxInfo.isTaxable === 'yes' && selectedUser.taxInfo.taxtypeid) {
                          form.setValue('taxTypeId', selectedUser.taxInfo.taxtypeid);
                        } else if (selectedUser.taxInfo.isTaxable === 'no') {
                          const noTaxType = taxTypes.find((type: any) => type.code === 'no_tax');
                          if (noTaxType && noTaxType._id) {
                            form.setValue('taxTypeId', noTaxType._id);
                          }
                        }
                      }
                    }}
                    value={field.value}
                    disabled={!usersLoaded}
                    open={userDropdownOpen}
                    onOpenChange={setUserDropdownOpen}
                  >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder={
                        !usersLoaded
                          ? t("common.loading") || "Loading..."
                          : t("billing.selectUser") || "Select user"
                      } />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent
                    onCloseAutoFocus={(e) => e.preventDefault()}
                    onEscapeKeyDown={(e) => {
                      // Only close if search is empty or if focus is not on search input
                      if (userSearchTerm === '' || document.activeElement?.tagName !== 'INPUT') {
                        setUserDropdownOpen(false);
                      } else {
                        e.preventDefault();
                      }
                    }}
                    onPointerDownOutside={(e) => {
                      // Prevent closing when clicking on search input
                      const target = e.target as HTMLElement;
                      if (target.closest('input[type="text"]')) {
                        e.preventDefault();
                      }
                    }}
                    onMouseEnter={() => {
                      // Ensure search input stays focused when mouse enters dropdown
                      const searchInput = document.querySelector('input[type="text"]') as HTMLInputElement;
                      if (searchInput && document.activeElement !== searchInput) {
                        searchInput.focus();
                      }
                    }}
                    onMouseLeave={() => {
                      // Keep search input focused even when mouse leaves dropdown
                      const searchInput = document.querySelector('input[type="text"]') as HTMLInputElement;
                      if (searchInput && userDropdownOpen) {
                        searchInput.focus();
                      }
                    }}
                  >
                    <div
                      className="px-2 pt-2 pb-2 sticky top-0 z-10 bg-background border-b"
                      onKeyDown={(e) => e.stopPropagation()}
                    >
                      <DropdownSearchInput
                        value={userSearchTerm}
                        onChange={setUserSearchTerm}
                        isOpen={userDropdownOpen}
                        autoFocus
                        placeholder={t("common.search") || "Search..."}
                      />
                    </div>
                    {/* Always show selected user first if it exists - pinned below search */}
                    {(() => {
                      if (!field.value) return null;

                      const selectedUser = users.find(u => u._id === field.value);
                      if (!selectedUser) return null;

                      return (
                        <div key="selected-user-section" className="bg-muted/50 border-b">
                          <SelectItem
                            value={selectedUser._id}
                            onMouseEnter={() => {
                              // Keep search input focused when hovering over items
                              const searchInput = document.querySelector('input[type="text"]') as HTMLInputElement;
                              if (searchInput && document.activeElement !== searchInput) {
                                searchInput.focus();
                              }
                            }}
                          >
                            <div className="flex items-center gap-2">
                              <span className="text-xs bg-primary text-primary-foreground px-1.5 py-0.5 rounded">Selected</span>
                              <span>{selectedUser.name} ({selectedUser.email})</span>
                            </div>
                          </SelectItem>
                        </div>
                      );
                    })()}
                    {/* Show other users (excluding selected user) */}
                    {filteredUsers
                      .filter((user) => typeof user?._id === 'string' && !!user._id.trim() && user._id !== field.value)
                      .map((user) => (
                        <SelectItem
                          key={user._id}
                          value={user._id}
                          onMouseEnter={() => {
                            // Keep search input focused when hovering over items
                            const searchInput = document.querySelector('input[type="text"]') as HTMLInputElement;
                            if (searchInput && document.activeElement !== searchInput) {
                              searchInput.focus();
                            }
                          }}
                        >
                          {user.name} ({user.email})
                        </SelectItem>
                      ))}
                    {/* Show no results message only if search term exists and no filtered results (excluding selected user) */}
                    {userSearchTerm && filteredUsers.filter(user => user._id !== field.value).length === 0 && (
                      <div className="text-xs text-muted-foreground px-2 py-2 mt-2">
                        {t("table.noResults") || "No users found"}
                      </div>
                    )}
                  </SelectContent>
                  </Select>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="taxTypeId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t("billing.taxType") || "Tax Type"}</FormLabel>
                <FormControl>
                  <Select
                    value={field.value}
                    onValueChange={field.onChange}
                    disabled={taxTypesLoading || !usersLoaded}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={
                        !usersLoaded
                          ? t("common.loading") || "Loading users..."
                          : taxTypesLoading
                            ? t("common.loading") || "Loading tax types..."
                            : taxTypesError
                              ? taxTypesError
                              : taxTypes.length === 0
                                ? (t("billing.noTaxTypes") || "No tax types available")
                                : (t("billing.selectTaxType") || "Select tax type")
                      } />
                    </SelectTrigger>
                    <SelectContent>
                      {Array.isArray(taxTypes) && taxTypes
                        .filter((type: any) => typeof type?._id === 'string' && !!type._id.trim())
                        .map((type: any) => (
                          <SelectItem key={type._id} value={type._id}>
                            {type.code} - {type.names?.[0] || type.code}
                          </SelectItem>
                        ))}
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {error && (
            <div className="text-sm font-medium text-destructive">{error}</div>
          )}

          <Button type="submit" disabled={isLoading || taxTypesLoading}>
            {isEditing ? t("billing.updateInvoice") || "Update Invoice" : t("billing.createInvoice") || "Create Invoice"}
          </Button>
        </form>
      </Form>

      {process.env.NODE_ENV === "development" && mounted && (
        <DebugForm
          formValues={form.getValues()}
          users={users}
          errors={form.formState.errors}
          isEditing={isEditing}
          existingInvoice={existingInvoice}
        />
      )}
    </Card>
  );
}

function DebugForm({
  formValues,
  users,
  errors,
  isEditing,
  existingInvoice
}: {
  formValues: any;
  users: User[];
  errors: any;
  isEditing: boolean;
  existingInvoice?: Invoice;
}) {
  return (
    <details className="mt-8 p-4 border border-dashed border-gray-400 bg-gray-50 text-xs">
      <summary>Debug: Form Data</summary>
      <div className="grid grid-cols-2 gap-4">
        <div>
          <h4 className="font-bold mb-2">Form Values</h4>
          <pre>{JSON.stringify(formValues, null, 2)}</pre>
        </div>
        <div>
          <h4 className="font-bold mb-2">Errors</h4>
          <pre>{JSON.stringify(errors, null, 2)}</pre>
        </div>
        <div className="col-span-2">
          <h4 className="font-bold mb-2">Mode</h4>
          <p>Is Editing: {String(isEditing)}</p>
          {isEditing && existingInvoice && (
            <div>
              <h4 className="font-bold mb-2 mt-4">Existing Invoice</h4>
              <pre>{JSON.stringify(existingInvoice, null, 2)}</pre>
            </div>
          )}
        </div>
        <div className="col-span-2">
          <h4 className="font-bold mb-2">Available Users</h4>
          <pre>{JSON.stringify(users, null, 2)}</pre>
        </div>
      </div>
    </details>
  );
}