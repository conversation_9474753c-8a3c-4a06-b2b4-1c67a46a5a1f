'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { useAppSelector } from '@/lib/redux/hooks';
import { canUserSuperviseEvents, canUserViewEventReports } from '@/lib/utils/permissions-utils';

export default function EventsDashboardPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const permissions = useAppSelector(state => state.permissions);

  // Check permissions and redirect
  useEffect(() => {
    if (status === 'loading') return;
    
    if (!session) {
      router.push('/auth/signin');
      return;
    }
    
    // Check if user has supervisor permissions and can view reports
    const hasSupervisionPermissions = permissions && canUserSuperviseEvents(permissions);
    const hasReportPermissions = permissions && canUserViewEventReports(permissions);
    
    if (hasSupervisionPermissions && hasReportPermissions) {
      // Redirect to reports page since "My events" is removed
      router.replace('/events/dashboard/reports');
    } else {
      // Redirect to main events page if no supervisor permissions
      router.replace('/events/calendar');
    }
  }, [session, status, permissions, router]);

  // Show loading state while redirecting
  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
        <p className="mt-2 text-gray-600">Redirecting...</p>
      </div>
    </div>
  );
}
