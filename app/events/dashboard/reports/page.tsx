'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useLanguage } from '@/lib/contexts/language-context';
import { useAppSelector } from '@/lib/redux/hooks';
import { canUserViewEventReports, canUserEditEventReports, canUserSuperviseEvents } from '@/lib/utils/permissions-utils';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';


import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  FileText, 
  Search, 
  Filter, 

  RefreshCw,
  AlertTriangle,
  Clock,
  CheckCircle,
  Eye,
  Edit,
  DollarSign
} from 'lucide-react';
import { toast } from 'sonner';
import { format } from 'date-fns';

interface EventReport {
  _id: string;
  eventId: {
    _id: string;
    name: string;
    startDate: string;
    endDate: string;
    location: string;
    branchId: {
      _id: string;
      name: string;
    };
  };
  status: 'pending' | 'processing' | 'submitted' | 'validated';
  createdAt: string;
  updatedAt: string;
  submittedAt?: string;
  validatedAt?: string;
  totalCommissions?: number;
  personnelData?: {
    supervisors: Array<{
      userId: string;
      name: string;
      commission: number;
    }>;
    paps: Array<{
      userId: string;
      name: string;
      commission: number;
      hoursWorked: number;
    }>;
    cooks: Array<{
      userId: string;
      name: string;
      commission: number;
      hoursWorked: number;
      percentage: number;
    }>;
  };
  reservationCount?: number;
  notes?: string;
}

export default function ReportsDashboardPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const searchParams = useSearchParams();
  const { t } = useLanguage();
  const permissions = useAppSelector(state => state.permissions);

  const [reports, setReports] = useState<EventReport[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  // Filters
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('updatedAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  // Check permissions using Redux store
  const hasReportPermissions = permissions && canUserSuperviseEvents(permissions);

  // Initialize filters from URL params
  useEffect(() => {
    const status = searchParams?.get('status');
    if (status) setStatusFilter(status);
  }, [searchParams]);

  // Redirect if not authorized
  useEffect(() => {
    if (status === 'loading') return;
    
    if (!session) {
      router.push('/auth/signin');
      return;
    }
    
    if (!hasReportPermissions) {
      toast.error(t('events.errors.noPermissionViewAll'));
      router.push('/');
      return;
    }
  }, [session, status, hasReportPermissions, router]);

  // Fetch reports
  const fetchReports = async (showRefreshIndicator = false) => {
    try {
      if (showRefreshIndicator) setRefreshing(true);
      
      const response = await fetch('/api/event-reports');
      
      if (!response.ok) {
        throw new Error(`Failed to fetch reports: ${response.statusText}`);
      }
      
      const data = await response.json();
      setReports(data.reports || []);
      setError(null);
    } catch (err) {
      console.error('Error fetching reports:', err);
      setError(err instanceof Error ? err.message : t('events.errors.failedToLoad'));
      toast.error(t('events.errors.failedToLoad'));
    } finally {
      setLoading(false);
      if (showRefreshIndicator) setRefreshing(false);
    }
  };

  useEffect(() => {
    if (hasReportPermissions) {
      fetchReports();
    }
  }, [hasReportPermissions]);

  // Filter and sort reports
  const filteredReports = reports.filter(report => {
    // Filter out reports with null eventId
    if (!report.eventId) {
      return false;
    }

    // Search filter
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      const matchesSearch =
        report.eventId.name.toLowerCase().includes(searchLower) ||
        report.eventId.location.toLowerCase().includes(searchLower) ||
        (report.eventId.branchId?.name || '').toLowerCase().includes(searchLower);

      if (!matchesSearch) return false;
    }

    // Status filter
    if (statusFilter !== 'all' && report.status !== statusFilter) {
      return false;
    }

    return true;
  }).sort((a, b) => {
    let aValue: any, bValue: any;

    // Additional null checks for sorting
    if (!a.eventId || !b.eventId) {
      return 0; // Keep original order if either eventId is null
    }

    switch (sortBy) {
      case 'eventName':
        aValue = a.eventId.name.toLowerCase();
        bValue = b.eventId.name.toLowerCase();
        break;
      case 'eventDate':
        aValue = new Date(a.eventId.startDate);
        bValue = new Date(b.eventId.startDate);
        break;
      case 'status':
        aValue = a.status;
        bValue = b.status;
        break;
      case 'updatedAt':
        aValue = new Date(a.updatedAt);
        bValue = new Date(b.updatedAt);
        break;
      default:
        aValue = new Date(a.updatedAt);
        bValue = new Date(b.updatedAt);
    }

    if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
    if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
    return 0;
  });

  // Group reports by status for tabs
  const reportsByStatus = {
    all: filteredReports,
    pending: filteredReports.filter(r => r.status === 'pending'),
    processing: filteredReports.filter(r => r.status === 'processing'),
    submitted: filteredReports.filter(r => r.status === 'submitted'),
    validated: filteredReports.filter(r => r.status === 'validated')
  };

  if (status === 'loading' || loading) {
    return <ReportsPageSkeleton />;
  }

  if (!session || !hasReportPermissions) {
    return null; // Will redirect
  }

  if (error) {
    return (
      <div className="container mx-auto py-6">
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {error}
            <Button 
              variant="outline" 
              size="sm" 
              className="ml-4"
              onClick={() => fetchReports()}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              {t('common.retry')}
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            {t('events.reports.title')}
          </h1>
          <p className="text-muted-foreground">
            {t('events.reports.description')}
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => fetchReports(true)}
            disabled={refreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            {t('common.refresh')}
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            {t('events.filtersAndSearch')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder={t('events.reports.searchPlaceholder')}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder={t('events.filterByStatus')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{t('events.allStatuses')}</SelectItem>
                <SelectItem value="pending">{t('events.status.pending')}</SelectItem>
                <SelectItem value="processing">{t('events.status.processing')}</SelectItem>
                <SelectItem value="submitted">{t('events.status.submitted')}</SelectItem>
                <SelectItem value="validated">{t('events.status.validated')}</SelectItem>
              </SelectContent>
            </Select>

            <Select value={`${sortBy}-${sortOrder}`} onValueChange={(value) => {
              const [field, order] = value.split('-');
              setSortBy(field);
              setSortOrder(order as 'asc' | 'desc');
            }}>
              <SelectTrigger>
                <SelectValue placeholder={t('events.sortBy')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="updatedAt-desc">{t('events.reports.recentlyUpdated')}</SelectItem>
                <SelectItem value="updatedAt-asc">{t('events.reports.oldestUpdated')}</SelectItem>
                <SelectItem value="eventDate-desc">{t('events.reports.eventDateLatest')}</SelectItem>
                <SelectItem value="eventDate-asc">{t('events.reports.eventDateEarliest')}</SelectItem>
                <SelectItem value="eventName-asc">{t('events.sort.nameAZ')}</SelectItem>
                <SelectItem value="eventName-desc">{t('events.sort.nameZA')}</SelectItem>
                <SelectItem value="status-asc">{t('events.sort.status')}</SelectItem>
              </SelectContent>
            </Select>

            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">
                {filteredReports.length} {filteredReports.length !== 1 ? t('events.reports.reports') : t('events.reports.report')}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Reports Tabs */}
      <Tabs defaultValue="all" className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="all" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            {t('events.tabs.all')} ({reportsByStatus.all.length})
          </TabsTrigger>
          <TabsTrigger value="pending" className="flex items-center gap-2">
            <Clock className="h-4 w-4" />
            {t('events.status.pending')} ({reportsByStatus.pending.length})
          </TabsTrigger>
          <TabsTrigger value="processing" className="flex items-center gap-2">
            <Edit className="h-4 w-4" />
            {t('events.status.processing')} ({reportsByStatus.processing.length})
          </TabsTrigger>
          <TabsTrigger value="submitted" className="flex items-center gap-2">
            <CheckCircle className="h-4 w-4" />
            {t('events.status.submitted')} ({reportsByStatus.submitted.length})
          </TabsTrigger>
          <TabsTrigger value="validated" className="flex items-center gap-2">
            <DollarSign className="h-4 w-4" />
            {t('events.status.validated')} ({reportsByStatus.validated.length})
          </TabsTrigger>
        </TabsList>

        {Object.entries(reportsByStatus).map(([tabKey, tabReports]) => (
          <TabsContent key={tabKey} value={tabKey} className="space-y-4">
            {tabReports.length > 0 ? (
              <div className="grid grid-cols-1 gap-4">
                {tabReports.map((report) => (
                  <ReportCard
                    key={report._id}
                    report={report}
                    onView={() => report.eventId ? router.push(`/events/${report.eventId._id}/report`) : null}
                    onEdit={() => report.eventId ? router.push(`/events/${report.eventId._id}/report/edit`) : null}
                  />
                ))}
              </div>
            ) : (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-12">
                  <FileText className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">
                    {t('events.reports.noReportsFound')}
                  </h3>
                  <p className="text-muted-foreground text-center">
                    {tabKey === 'all'
                      ? t('events.reports.noReportsMatchFilters')
                      : `${t('events.noEventsFoundFor')} ${tabKey} ${t('events.reports.reportsFound')}`
                    }
                  </p>
                  {searchTerm || statusFilter !== 'all' ? (
                    <Button
                      variant="outline"
                      className="mt-4"
                      onClick={() => {
                        setSearchTerm('');
                        setStatusFilter('all');
                      }}
                    >
                      {t('events.clearFilters')}
                    </Button>
                  ) : null}
                </CardContent>
              </Card>
            )}
          </TabsContent>
        ))}
      </Tabs>

      {/* Summary Stats */}
      {reports.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>{t('events.summary.title')}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">
                  {reportsByStatus.pending.length}
                </div>
                <div className="text-sm text-muted-foreground">{t('events.status.pending')}</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {reportsByStatus.processing.length}
                </div>
                <div className="text-sm text-muted-foreground">{t('events.status.processing')}</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {reportsByStatus.submitted.length}
                </div>
                <div className="text-sm text-muted-foreground">{t('events.status.submitted')}</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {reportsByStatus.validated.length}
                </div>
                <div className="text-sm text-muted-foreground">{t('events.status.validated')}</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

// Helper Components
interface ReportCardProps {
  report: EventReport;
  onView: () => void;
  onEdit: () => void;
}

function ReportCard({ report, onView, onEdit }: ReportCardProps) {
  const permissions = useAppSelector(state => state.permissions);
  const canEdit = canUserEditEventReports(permissions) && ['pending', 'processing'].includes(report.status);
  const {t}= useLanguage();

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-orange-100 text-orange-800';
      case 'processing': return 'bg-blue-100 text-blue-800';
      case 'submitted': return 'bg-purple-100 text-purple-800';
      case 'validated': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // Handle case where eventId is null
  if (!report.eventId) {
    return (
      <Card className="hover:shadow-md transition-shadow">
        <CardContent className="p-6">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-2">
                <h3 className="text-lg font-semibold text-muted-foreground">Event data unavailable</h3>
                <Badge className={getStatusColor(report.status)}>
                  {report.status.charAt(0).toUpperCase() + report.status.slice(1)}
                </Badge>
              </div>
              <div className="space-y-1 text-sm text-muted-foreground mb-4">
                <p>Report ID: {report._id}</p>
                <p>Updated: {format(new Date(report.updatedAt), 'PPP p')}</p>
              </div>
            </div>
            <div className="flex items-center gap-2 ml-4">
              <Badge variant="destructive">Missing Event Data</Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardContent className="p-6">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-2">
              <h3 className="text-lg font-semibold">{report.eventId.name}</h3>
              <Badge className={getStatusColor(report.status)}>
                {report.status.charAt(0).toUpperCase() + report.status.slice(1)}
              </Badge>
            </div>

            <div className="space-y-1 text-sm text-muted-foreground mb-4">
              <p>{report.eventId.location}</p>
              <p>{report.eventId.branchId?.name || 'Branch not available'}</p>
              <p>Event: {format(new Date(report.eventId.startDate), 'PPP')}</p>
              <p>Updated: {format(new Date(report.updatedAt), 'PPP p')}</p>
            </div>

            {report.totalCommissions && (
              <div className="flex items-center gap-2 text-sm">
                <DollarSign className="h-4 w-4 text-green-600" />
                <span className="font-medium text-green-600">
                  ${report.totalCommissions.toFixed(2)} total commissions
                </span>
              </div>
            )}

            {report.reservationCount && (
              <div className="flex items-center gap-2 text-sm mt-1">
                <span className="text-muted-foreground">
                  {report.reservationCount} reservations linked
                </span>
              </div>
            )}
          </div>

          <div className="flex items-center gap-2 ml-4">
            <Button
              variant="outline"
              size="sm"
              onClick={onView}
              disabled={!report.eventId}
            >
              <Eye className="h-4 w-4 mr-2" />
              {t('common.view')}
            </Button>
            {canEdit && report.eventId && (
              <Button size="sm" onClick={onEdit}>
                <Edit className="h-4 w-4 mr-2" />
                {t('common.edit')}
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Loading skeleton
function ReportsPageSkeleton() {
  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <div className="h-8 w-48 bg-gray-200 rounded mb-2"></div>
          <div className="h-4 w-64 bg-gray-200 rounded"></div>
        </div>
        <div className="flex items-center gap-2">
          <div className="h-9 w-20 bg-gray-200 rounded"></div>
        </div>
      </div>

      <Card>
        <CardHeader>
          <div className="h-6 w-32 bg-gray-200 rounded"></div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-10 bg-gray-200 rounded"></div>
            ))}
          </div>
        </CardContent>
      </Card>

      <div className="space-y-4">
        <div className="h-10 bg-gray-200 rounded"></div>
        <div className="grid grid-cols-1 gap-4">
          {[...Array(3)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="space-y-3">
                  <div className="h-6 w-3/4 bg-gray-200 rounded"></div>
                  <div className="h-4 w-1/2 bg-gray-200 rounded"></div>
                  <div className="h-4 w-2/3 bg-gray-200 rounded"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
}
