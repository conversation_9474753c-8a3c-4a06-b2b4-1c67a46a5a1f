'use client';

import { useSession } from 'next-auth/react';
import { useRouter, usePathname } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useAppSelector } from '@/lib/redux/hooks';
import { useLanguage } from '@/lib/contexts/language-context';
import { canUserViewEvents } from '@/lib/utils/permissions-utils';
import { EventContextProvider } from '@/lib/contexts/event-context';
import { EventsSidebar } from '@/components/events/EventsSidebar';
import { EventsBreadcrumb } from '@/components/events/EventsBreadcrumb';


interface EventsLayoutProps {
  children: React.ReactNode;
}

export default function EventsLayout({ children }: EventsLayoutProps) {
  const { data: session, status } = useSession();
  const router = useRouter();
  const pathname = usePathname();
  const permissions = useAppSelector(state => state.permissions);
  const { t } = useLanguage();

  // Sidebar state - collapsed by default
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(true);

  // Check permissions using Redux store
  const hasEventAccess = permissions && canUserViewEvents(permissions);

  // Check if current page is calendar
  const isCalendarPage = pathname === '/events/calendar';

  // Show loading state while session is loading
  if (status === 'loading') {
    return null;
  }

  // Redirect to login if not authenticated
  if (!session) {
    router.push('/auth/signin');
    return null;
  }

  // Show access denied if no permissions
  if (!hasEventAccess) {
    return (
      <div className="container mx-auto py-10">
        <div className="p-6">
          <h1 className="text-2xl font-bold text-destructive">
            {t('events.accessDenied.title')}
          </h1>
          <p className="text-muted-foreground">
            {t('events.accessDenied.description')}
          </p>
        </div>
      </div>
    );
  }

  return (
    <EventContextProvider>
      <div className="events-layout min-h-screen bg-background">
        <div className="flex">
          <EventsSidebar
            pathname={pathname}
            permissions={permissions}
            isCollapsed={isSidebarCollapsed}
            onToggleCollapse={() => setIsSidebarCollapsed(!isSidebarCollapsed)}
          />
          <main className="flex-1">
            <div className="container mx-auto py-6 space-y-6">
              {!isCalendarPage && <EventsBreadcrumb />}
              {children}
            </div>
          </main>
        </div>
      </div>
    </EventContextProvider>
  );
}
