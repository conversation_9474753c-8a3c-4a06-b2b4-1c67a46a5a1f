'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter, useSearchParams } from 'next/navigation';

import { useAppSelector } from '@/lib/redux/hooks';
import { useLanguage } from '@/lib/contexts/language-context';
import { canUserViewAllEvents, canUserCreateEvents } from '@/lib/utils/permissions-utils';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Calendar,
  Search,
  Filter,
  Plus,
  Refresh<PERSON>w,
  AlertTriangle,
  Clock,
  CheckCircle,
  FileText,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight
} from 'lucide-react';
import { EventCard } from './components/EventCard';

import { toast } from 'sonner';

interface Event {
  _id: string;
  name: string;
  startDate: string;
  endDate: string;
  location: string;
  status: 'new' | 'in_progress' | 'cancelled' | 'processing_report' | 'awaiting_validation' | 'done';
  branchId: {
    _id: string;
    name: string;
  };
  partnerId: {
    _id: string;
    name: string;
  };
  eventTypeId?: {
    _id: string;
    name: string;
    code: string;
  };
  supervisors: Array<{
    _id: string;
    name: string;
    email: string;
  }>;
  cooks: Array<{
    _id: string;
    name: string;
    email: string;
  }>;
  agents?: {
    requiredCount?: number;
    assignedAgents?: Array<{
      _id: string;
      name: string;
      email: string;
    }>;
  };
  reportId?: {
    _id: string;
    status: 'pending' | 'processing' | 'submitted' | 'validated';
  };
  clientGoal?: number;
  notes?: string;
}

export default function EventsPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const searchParams = useSearchParams();

  const permissions = useAppSelector(state => state.permissions);
  const { t } = useLanguage();

  const [events, setEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalEvents, setTotalEvents] = useState(0);
  const [pageSize] = useState(20);
  const [paginationLoading, setPaginationLoading] = useState(false);

  // Status counts and active tab
  const [statusCounts, setStatusCounts] = useState({
    all: 0,
    active: 0,
    reporting: 0,
    completed: 0,
    overdue: 0
  });
  const [activeTab, setActiveTab] = useState('all');

  // Filters
  const [searchInput, setSearchInput] = useState(''); // User input (immediate)
  const [searchTerm, setSearchTerm] = useState(''); // Debounced search term (used for API calls)
  const [isSearching, setIsSearching] = useState(false); // Search debounce indicator
  const [dateFilter, setDateFilter] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('createdAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  // Check permissions using Redux store
  const hasAllEventsAccess = permissions && canUserViewAllEvents(permissions);
  const canCreateEvents = permissions && canUserCreateEvents(permissions);

  // Initialize filters from URL params
  useEffect(() => {
    if (!searchParams) return;

    const status = searchParams.get('status');
    const filter = searchParams.get('filter');

    if (status) setActiveTab(status);
    if (filter === 'overdue') setActiveTab('overdue');
  }, [searchParams]);

  // Redirect if not authorized
  useEffect(() => {
    if (status === 'loading') return;
 
    if (!session) {
      router.push('/auth/signin');
      return;
    }

    if (!hasAllEventsAccess) {
      toast.error(t('events.errors.noPermissionViewAll'));
      router.push('/');
      return;
    }
  }, [session, status, hasAllEventsAccess, router]);

  // Fetch events
  const fetchEvents = async (page = currentPage, tab = activeTab, showRefreshIndicator = false): Promise<void> => {
    try {
      if (showRefreshIndicator) setRefreshing(true);

      const params = new URLSearchParams({
        page: page.toString(),
        limit: pageSize.toString(),
        status: tab,
        search: searchTerm,
        dateFilter: dateFilter,
        sortBy: sortBy,
        sortOrder: sortOrder
      });

      const response = await fetch(`/api/events?${params}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch events: ${response.statusText}`);
      }

      const data = await response.json();
      setEvents(data.events || []);

      // Update pagination state
      if (data.pagination) {
        setTotalPages(data.pagination.totalPages);
        setTotalEvents(data.pagination.total);
        setCurrentPage(data.pagination.page);
      }

      // Update status counts
      if (data.statusCounts) {
        setStatusCounts(data.statusCounts);
      }

      setError(null);
    } catch (err) {
      console.error('Error fetching events:', err);
      setError(err instanceof Error ? err.message : t('events.errors.failedToLoad'));
      toast.error(t('events.errors.failedToLoad'));
    } finally {
      setLoading(false);
      if (showRefreshIndicator) setRefreshing(false);
    }
  };

  useEffect(() => {
    if (hasAllEventsAccess) {
      fetchEvents();
    }
  }, [hasAllEventsAccess]);

  // Debounce search input
  useEffect(() => {
    // Set searching indicator if there's a difference between input and term
    if (searchInput !== searchTerm) {
      setIsSearching(true);
    }

    const debounceTimer = setTimeout(() => {
      setSearchTerm(searchInput);
      setIsSearching(false);
    }, 500); // 500ms debounce delay

    return () => clearTimeout(debounceTimer);
  }, [searchInput]);

  // Refetch when filters change
  useEffect(() => {
    if (hasAllEventsAccess) {
      setCurrentPage(1); // Reset to first page when filters change
      fetchEvents(1, activeTab);
    }
  }, [searchTerm, dateFilter, sortBy, sortOrder, activeTab]);

  // Pagination handlers
  const handlePageChange = (newPage: number) => {
    setPaginationLoading(true);
    setCurrentPage(newPage);
    fetchEvents(newPage, activeTab).finally(() => setPaginationLoading(false));
  };

  const handleFirstPage = () => {
    if (currentPage > 1) {
      handlePageChange(1);
    }
  };

  const handlePrevPage = () => {
    if (currentPage > 1) {
      handlePageChange(currentPage - 1);
    }
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      handlePageChange(currentPage + 1);
    }
  };

  const handleLastPage = () => {
    if (currentPage < totalPages) {
      handlePageChange(totalPages);
    }
  };

  // Handle status change
  const handleStatusChange = async (eventId: string, newStatus: string) => {
    try {
      const response = await fetch(`/api/events/${eventId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      });

      if (!response.ok) {
        throw new Error(t('events.errors.failedToUpdateStatus'));
      }

      // Update local state
      setEvents(events.map(event =>
        event._id === eventId
          ? { ...event, status: newStatus as any }
          : event
      ));

      toast.success(t('events.statusUpdatedSuccessfully'));
    } catch (error) {
      console.error('Error updating event status:', error);
      toast.error(t('events.errors.failedToUpdateStatus'));
    }
  };

  // Handle tab change
  const handleTabChange = (newTab: string) => {
    setActiveTab(newTab);
    setCurrentPage(1);
  };

  if (status === 'loading' || loading) {
    return <EventsPageSkeleton />;
  }

  if (!session || !hasAllEventsAccess) {
    return null; // Will redirect
  }



  if (error) {
    return (
      <div className="container mx-auto py-6">
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {error}
            <Button
              variant="outline"
              size="sm"
              className="ml-4"
              onClick={() => fetchEvents()}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              {t('common.retry')}
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            {t('events.title')}
          </h1>
          <p className="text-muted-foreground">
            {t('events.viewAndManage')}
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          {canCreateEvents && (
            <Button
              onClick={() => router.push('/events/new')}
              size="sm"
            >
              <Plus className="h-4 w-4 mr-2" />
              {t('events.createEvent')}
            </Button>
          )}
          <Button
            variant="outline"
            size="sm"
            onClick={() => fetchEvents(currentPage, activeTab, true)}
            disabled={refreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            {t('common.refresh')}
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            {t('events.filtersAndSearch')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="relative">
              <Search className={`absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground ${isSearching ? 'animate-pulse' : ''}`} />
              <Input
                placeholder={t('events.searchPlaceholder')}
                value={searchInput}
                onChange={(e) => setSearchInput(e.target.value)}
                className="pl-10"
              />
              {isSearching && (
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <RefreshCw className="h-4 w-4 text-muted-foreground animate-spin" />
                </div>
              )}
            </div>

            <Select value={dateFilter} onValueChange={setDateFilter}>
              <SelectTrigger>
                <SelectValue placeholder={t('events.filterByDate')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{t('events.allDates')}</SelectItem>
                <SelectItem value="upcoming">{t('events.upcoming')}</SelectItem>
                <SelectItem value="this_month">{t('events.thisMonth')}</SelectItem>
                <SelectItem value="next_month">{t('events.nextMonth')}</SelectItem>
                <SelectItem value="past">{t('events.pastEvents')}</SelectItem>
              </SelectContent>
            </Select>

            <Select value={`${sortBy}-${sortOrder}`} onValueChange={(value) => {
              const [field, order] = value.split('-');
              setSortBy(field);
              setSortOrder(order as 'asc' | 'desc');
            }}>
              <SelectTrigger>
                <SelectValue placeholder={t('events.sortBy')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="createdAt-desc">{t('events.sort.createdNewestFirst')}</SelectItem>
                <SelectItem value="createdAt-asc">{t('events.sort.createdOldestFirst')}</SelectItem>
                <SelectItem value="startDate-asc">{t('events.sort.dateEarliestFirst')}</SelectItem>
                <SelectItem value="startDate-desc">{t('events.sort.dateLatestFirst')}</SelectItem>
                <SelectItem value="name-asc">{t('events.sort.nameAZ')}</SelectItem>
                <SelectItem value="name-desc">{t('events.sort.nameZA')}</SelectItem>
                <SelectItem value="status-asc">{t('events.sort.status')}</SelectItem>
                <SelectItem value="location-asc">{t('events.sort.location')}</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Events Tabs */}
      <Tabs value={activeTab} onValueChange={handleTabChange} className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="all" className="flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            {t('events.tabs.all')} ({statusCounts.all})
          </TabsTrigger>
          <TabsTrigger value="active" className="flex items-center gap-2">
            <Clock className="h-4 w-4" />
            {t('events.tabs.active')} ({statusCounts.active})
          </TabsTrigger>
          <TabsTrigger value="reporting" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            {t('events.tabs.reporting')} ({statusCounts.reporting})
          </TabsTrigger>
          <TabsTrigger value="completed" className="flex items-center gap-2">
            <CheckCircle className="h-4 w-4" />
            {t('events.tabs.completed')} ({statusCounts.completed})
          </TabsTrigger>
          <TabsTrigger value="overdue" className="flex items-center gap-2">
            <AlertTriangle className="h-4 w-4" />
            {t('events.tabs.overdue')} ({statusCounts.overdue})
          </TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="space-y-4">
          {events.length > 0 ? (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {events.map((event) => (
                <EventCard
                  key={event._id}
                  event={event}
                  onStatusChange={handleStatusChange}
                  shouldShowViewDetailsButton={true}
                />
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <Calendar className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">
                  {t('events.noEventsFound')}
                </h3>
                <p className="text-muted-foreground text-center">
                  {activeTab === 'all'
                    ? t('events.noEventsMatchFilters')
                    : `${t('events.noEventsFoundFor')} ${activeTab} ${t('events.eventsFound')}`
                  }
                </p>
                {searchInput || dateFilter !== 'all' ? (
                  <Button
                    variant="outline"
                    className="mt-4"
                    onClick={() => {
                      setSearchInput('');
                      setSearchTerm('');
                      setDateFilter('all');
                    }}
                  >
                    {t('events.clearFilters')}
                  </Button>
                ) : null}
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>

      {/* Pagination Controls */}
      {totalPages > 1 && (
        <Card>
          <CardContent className="py-4">
            <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
              <div className="text-sm text-muted-foreground">
                {t('events.pagination.showing')} {((currentPage - 1) * pageSize) + 1} {t('events.pagination.to')} {Math.min(currentPage * pageSize, totalEvents)} {t('events.pagination.of')} {totalEvents} {t('events.pagination.events')}
              </div>

              <div className="flex items-center gap-2">
                {/* First Page Button */}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleFirstPage}
                  disabled={currentPage <= 1 || paginationLoading}
                  title="First page"
                >
                  <ChevronsLeft className="h-4 w-4" />
                </Button>

                {/* Previous Page Button */}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handlePrevPage}
                  disabled={currentPage <= 1 || paginationLoading}
                >
                  <ChevronLeft className="h-4 w-4 mr-1" />
                  {t('common.previous')}
                </Button>

                {/* Page Numbers */}
                <div className="flex items-center gap-1">
                  {/* Show first page if not in visible range */}
                  {currentPage > 3 && totalPages > 5 && (
                    <>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(1)}
                        disabled={paginationLoading}
                        className="w-8 h-8 p-0"
                      >
                        1
                      </Button>
                      {currentPage > 4 && (
                        <span className="px-2 text-muted-foreground">...</span>
                      )}
                    </>
                  )}

                  {/* Visible page range */}
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    const pageNum = Math.max(1, Math.min(totalPages - 4, currentPage - 2)) + i;
                    if (pageNum > totalPages) return null;

                    return (
                      <Button
                        key={pageNum}
                        variant={pageNum === currentPage ? "default" : "outline"}
                        size="sm"
                        onClick={() => handlePageChange(pageNum)}
                        disabled={paginationLoading}
                        className="w-8 h-8 p-0"
                      >
                        {pageNum}
                      </Button>
                    );
                  })}

                  {/* Show last page if not in visible range */}
                  {currentPage < totalPages - 2 && totalPages > 5 && (
                    <>
                      {currentPage < totalPages - 3 && (
                        <span className="px-2 text-muted-foreground">...</span>
                      )}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(totalPages)}
                        disabled={paginationLoading}
                        className="w-8 h-8 p-0"
                      >
                        {totalPages}
                      </Button>
                    </>
                  )}
                </div>

                {/* Next Page Button */}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleNextPage}
                  disabled={currentPage >= totalPages || paginationLoading}
                >
                  {t('common.next')}
                  <ChevronRight className="h-4 w-4 ml-1" />
                </Button>

                {/* Last Page Button */}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleLastPage}
                  disabled={currentPage >= totalPages || paginationLoading}
                  title="Last page"
                >
                  <ChevronsRight className="h-4 w-4" />
                </Button>
              </div>

              {/* Page Info */}
              <div className="text-sm text-muted-foreground">
                Page {currentPage} of {totalPages}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Summary Stats */}
      {statusCounts.all > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>{t('events.summary.title')}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {statusCounts.active}
                </div>
                <div className="text-sm text-muted-foreground">{t('events.summary.activeEvents')}</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {statusCounts.reporting}
                </div>
                <div className="text-sm text-muted-foreground">{t('events.summary.inReporting')}</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {statusCounts.completed}
                </div>
                <div className="text-sm text-muted-foreground">{t('events.summary.completed')}</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">
                  {statusCounts.overdue}
                </div>
                <div className="text-sm text-muted-foreground">{t('events.summary.overdue')}</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

// Loading skeleton component
function EventsPageSkeleton() {
  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header skeleton */}
      <div className="flex justify-between items-center">
        <div className="space-y-2">
          <div className="h-8 w-32 bg-gray-200 rounded animate-pulse" />
          <div className="h-4 w-48 bg-gray-200 rounded animate-pulse" />
        </div>
        <div className="flex gap-2">
          <div className="h-9 w-24 bg-gray-200 rounded animate-pulse" />
          <div className="h-9 w-20 bg-gray-200 rounded animate-pulse" />
        </div>
      </div>

      {/* Filters skeleton */}
      <div className="h-32 bg-gray-200 rounded animate-pulse" />

      {/* Tabs skeleton */}
      <div className="space-y-4">
        <div className="h-12 bg-gray-200 rounded animate-pulse" />

        {/* Event cards skeleton */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {Array.from({ length: 6 }).map((_, i) => (
            <div key={i} className="h-48 bg-gray-200 rounded animate-pulse" />
          ))}
        </div>

        {/* Pagination skeleton */}
        <div className="h-16 bg-gray-200 rounded animate-pulse" />

        {/* Summary stats skeleton */}
        <div className="h-32 bg-gray-200 rounded animate-pulse" />
      </div>
    </div>
  );
}
