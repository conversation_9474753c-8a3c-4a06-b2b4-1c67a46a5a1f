import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { User } from '@/types/user';
import React, { useRef, useEffect, useState, useMemo } from 'react';
import { Input } from '@/components/ui/input';
import { useLanguage } from '@/lib/contexts/language-context';

interface UserSelectProps {
  users: User[];
  value?: string;
  onValueChange: (value: string) => void;
  disabled?: boolean;
}

interface DropdownSearchInputProps {
  value: string;
  onChange: (v: string) => void;
  isOpen: boolean;
  autoFocus?: boolean;
  placeholder?: string;
}

const DropdownSearchInput: React.FC<DropdownSearchInputProps> = ({ value, onChange, isOpen, autoFocus, placeholder }) => {
  const { t } = useLanguage();
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (isOpen && autoFocus && inputRef.current) {
      // Use setTimeout to ensure the dropdown is fully rendered before focusing
      const timer = setTimeout(() => {
        if (inputRef.current) {
          inputRef.current.focus();
        }
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [isOpen, autoFocus]);

  return (
    <input
      ref={inputRef}
      value={value}
      onChange={e => onChange(e.target.value)}
      placeholder={placeholder || t('common.search')}
      className="h-8 px-2 text-sm border-muted focus:border-primary rounded shadow-none w-full"
      autoFocus={false}
      type="text"
      onMouseDown={(e) => e.stopPropagation()} // Prevent dropdown from closing
      onKeyDown={(e) => e.stopPropagation()} // Prevent dropdown from closing
    />
  );
};

const UserSelect: React.FC<UserSelectProps> = ({ users, value, onValueChange, disabled }) => {
  const { t } = useLanguage();
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [search, setSearch] = useState('');
  const filteredUsers = useMemo(() => {
    if (!search) return users;
    return users.filter(user =>
      user.name.toLowerCase().includes(search.toLowerCase()) ||
      user.email.toLowerCase().includes(search.toLowerCase())
    );
  }, [users, search]);

  return (
    <Select value={value} onValueChange={onValueChange} disabled={disabled} open={dropdownOpen} onOpenChange={setDropdownOpen}>
      <SelectTrigger>
        <SelectValue placeholder={t('events.selectUser')} />
      </SelectTrigger>
      <SelectContent>
        <div className="px-2 pt-2 pb-1 sticky top-0 z-10 bg-background">
          <DropdownSearchInput
            value={search}
            onChange={setSearch}
            isOpen={dropdownOpen}
            autoFocus
            placeholder={t('common.search')}
          />
        </div>
        {filteredUsers.map((user) => (
          <SelectItem key={user._id} value={user._id}>
            {user.name} <span className="text-xs text-muted-foreground">({user.email})</span>
          </SelectItem>
        ))}
        {filteredUsers.length === 0 && <div className="text-xs text-muted-foreground px-2 py-1">{t('events.form.noUsersFound')}</div>}
      </SelectContent>
    </Select>
  );
};

export default UserSelect;

// Debug panel
if (process.env.NODE_ENV === 'development') {
  // eslint-disable-next-line
  (UserSelect as any).displayName = 'UserSelect (Dev)';
} 