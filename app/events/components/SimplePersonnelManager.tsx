'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Plus, X, Users, UserCheck, ChefHat, Search } from 'lucide-react';
import { useLanguage } from '@/lib/contexts/language-context';

interface User {
  _id: string;
  name: string;
  email: string;
}

interface Personnel {
  supervisors: Array<{
    userId: string;
    name: string;
    email: string;
  }>;
  paps: Array<{
    userId: string;
    name: string;
    email: string;
  }>;
  cooks: Array<{
    userId: string;
    name: string;
    email: string;
  }>;
}

interface SimplePersonnelManagerProps {
  initialPersonnel: Personnel;
  onPersonnelChange: (personnel: Personnel) => void;
  availableSupervisors: User[];
  availableCooks: User[];
  readOnly?: boolean;
  maxSupervisors?: number;
}

export function SimplePersonnelManager({
  initialPersonnel,
  onPersonnelChange,
  availableSupervisors,
  availableCooks,
  readOnly = false,
  maxSupervisors = undefined
}: SimplePersonnelManagerProps) {
  const { t } = useLanguage();
  const [personnel, setPersonnel] = useState<Personnel>(initialPersonnel);
  const [availablePAPs, setAvailablePAPs] = useState<User[]>([]);
  const [supervisorSearch, setSupervisorSearch] = useState('');
  const [papSearch, setPapSearch] = useState('');
  const [cookSearch, setCookSearch] = useState('');

  // Fetch available PAPs
  useEffect(() => {
    fetchAvailablePAPs();
  }, []);

  // Update parent when personnel changes
  useEffect(() => {
    onPersonnelChange(personnel);
  }, [personnel, onPersonnelChange]);

  const fetchAvailablePAPs = async () => {
    try {
      const response = await fetch('/api/users?role=pap');
      if (response.ok) {
        const data = await response.json();
        setAvailablePAPs(data.users || []);
      }
    } catch (error) {
      console.error('Error fetching PAPs:', error);
    }
  };



  const removePersonnel = (userId: string, role: 'supervisor' | 'pap' | 'cook') => {
    const updatedPersonnel = {
      ...personnel,
      [role === 'supervisor' ? 'supervisors' : role === 'pap' ? 'paps' : 'cooks']: 
        personnel[role === 'supervisor' ? 'supervisors' : role === 'pap' ? 'paps' : 'cooks']
          .filter(p => p.userId !== userId)
    };

    setPersonnel(updatedPersonnel);
  };

  const addPersonnelByRole = (user: User, role: 'supervisor' | 'pap' | 'cook') => {
    // Check supervisor limit if specified
    if (role === 'supervisor' && maxSupervisors !== undefined && personnel.supervisors.length >= maxSupervisors) {
      return; // Don't add if limit is reached
    }

    const newAssignment = {
      userId: user._id,
      name: user.name,
      email: user.email
    };

    const updatedPersonnel = {
      ...personnel,
      [role === 'supervisor' ? 'supervisors' : role === 'pap' ? 'paps' : 'cooks']: [
        ...personnel[role === 'supervisor' ? 'supervisors' : role === 'pap' ? 'paps' : 'cooks'],
        newAssignment
      ]
    };

    setPersonnel(updatedPersonnel);
  };

  const getAvailableUsersByRole = (role: 'supervisor' | 'pap' | 'cook') => {
    let availableUsers: User[] = [];
    let searchTerm = '';

    if (role === 'supervisor') {
      availableUsers = availableSupervisors;
      searchTerm = supervisorSearch;
    } else if (role === 'pap') {
      availableUsers = availablePAPs;
      searchTerm = papSearch;
    } else if (role === 'cook') {
      availableUsers = availableCooks;
      searchTerm = cookSearch;
    }

    // Filter out already assigned users
    const assignedUserIds = personnel[role === 'supervisor' ? 'supervisors' : role === 'pap' ? 'paps' : 'cooks'].map(p => p.userId);
    const unassignedUsers = availableUsers.filter(user => !assignedUserIds.includes(user._id));

    // Apply search filter
    if (searchTerm.trim() === '') {
      return unassignedUsers;
    }

    return unassignedUsers.filter(user =>
      user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase())
    );
  };

  return (
    <div className="space-y-6">

      {/* Supervisors */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <UserCheck className="h-5 w-5" />
            {t('events.form.supervisors')}
            <Badge variant="secondary">{personnel.supervisors.length}</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* Assigned Supervisors */}
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-muted-foreground">{t('events.assigned')}</h4>
              <div className="space-y-2 min-h-[150px] h-[200px] overflow-y-auto border rounded-md p-2 bg-gray-50">
                {personnel.supervisors.length > 0 ? (
                  personnel.supervisors.map((supervisor) => (
                    <div key={supervisor.userId} className="flex items-center justify-between p-2 border rounded bg-green-50 border-green-200">
                      <div className="flex-1 min-w-0">
                        <p className="font-medium text-sm truncate">{supervisor.name}</p>
                        <p className="text-xs text-muted-foreground truncate">{supervisor.email}</p>
                      </div>
                      {!readOnly && (
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => removePersonnel(supervisor.userId, 'supervisor')}
                          className="h-6 w-6 p-0"
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      )}
                    </div>
                  ))
                ) : (
                  <div className="flex items-center justify-center h-full">
                    <p className="text-muted-foreground text-sm">
                      {t('events.form.noSupervisorsAssigned')}
                    </p>
                  </div>
                )}
              </div>
            </div>

            {/* Available Supervisors */}
            {!readOnly && (
              <div className="space-y-2">
                <h4 className="text-sm font-medium text-muted-foreground">
                  {t('events.availableToAssign')}
                  {maxSupervisors !== undefined && (
                    <span className="ml-2 text-xs">
                      ({personnel.supervisors.length}/{maxSupervisors} {t('events.max')})
                    </span>
                  )}
                </h4>
                {maxSupervisors !== undefined && personnel.supervisors.length >= maxSupervisors && (
                  <div className="p-2 bg-yellow-50 border border-yellow-200 rounded text-sm text-yellow-800">
                    {t('events.maxSupervisorsReached')} ({maxSupervisors})
                  </div>
                )}
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder={t('events.searchSupervisors')}
                    value={supervisorSearch}
                    onChange={(e) => setSupervisorSearch(e.target.value)}
                    className="pl-8"
                    disabled={maxSupervisors !== undefined && personnel.supervisors.length >= maxSupervisors}
                  />
                </div>
                <div className="space-y-2 min-h-[150px] h-[200px] overflow-y-auto border rounded-md p-2 bg-white">
                  {getAvailableUsersByRole('supervisor').length > 0 ? (
                    getAvailableUsersByRole('supervisor').map((supervisor) => {
                      const isDisabled = maxSupervisors !== undefined && personnel.supervisors.length >= maxSupervisors;
                      return (
                        <div
                          key={supervisor._id}
                          className={`flex items-center justify-between p-2 border rounded ${
                            isDisabled
                              ? 'opacity-50 cursor-not-allowed bg-gray-50'
                              : 'hover:bg-muted cursor-pointer'
                          }`}
                          onClick={() => !isDisabled && addPersonnelByRole(supervisor, 'supervisor')}
                        >
                          <div className="flex-1 min-w-0">
                            <p className="font-medium text-sm truncate">{supervisor.name}</p>
                            <p className="text-xs text-muted-foreground truncate">{supervisor.email}</p>
                          </div>
                          <Button
                            size="sm"
                            variant="ghost"
                            className="h-6 w-6 p-0"
                            disabled={isDisabled}
                          >
                            <Plus className="h-3 w-3" />
                          </Button>
                        </div>
                      );
                    })
                  ) : (
                    <div className="flex items-center justify-center h-full">
                      <p className="text-muted-foreground text-sm">
                        {supervisorSearch.trim() ? t('events.noSupervisorsFound') : t('events.noAvailableSupervisors')}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* PAPs */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            {t('events.form.papAgents')}
            <Badge variant="secondary">{personnel.paps.length}</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* Assigned PAPs */}
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-muted-foreground">{t('events.assigned')}</h4>
              <div className="space-y-2 min-h-[150px] h-[200px] overflow-y-auto border rounded-md p-2 bg-gray-50">
                {personnel.paps.length > 0 ? (
                  personnel.paps.map((pap) => (
                    <div key={pap.userId} className="flex items-center justify-between p-2 border rounded bg-blue-50 border-blue-200">
                      <div className="flex-1 min-w-0">
                        <p className="font-medium text-sm truncate">{pap.name}</p>
                        <p className="text-xs text-muted-foreground truncate">{pap.email}</p>
                      </div>
                      {!readOnly && (
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => removePersonnel(pap.userId, 'pap')}
                          className="h-6 w-6 p-0"
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      )}
                    </div>
                  ))
                ) : (
                  <div className="flex items-center justify-center h-full">
                    <p className="text-muted-foreground text-sm">
                      {t('events.form.noPapAgentsAssigned')}
                    </p>
                  </div>
                )}
              </div>
            </div>

            {/* Available PAPs */}
            {!readOnly && (
              <div className="space-y-2">
                <h4 className="text-sm font-medium text-muted-foreground">{t('events.availableToAssign')}</h4>
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder={t('events.searchPapAgents')}
                    value={papSearch}
                    onChange={(e) => setPapSearch(e.target.value)}
                    className="pl-8"
                  />
                </div>
                <div className="space-y-2 min-h-[150px] h-[200px] overflow-y-auto border rounded-md p-2 bg-white">
                  {getAvailableUsersByRole('pap').length > 0 ? (
                    getAvailableUsersByRole('pap').map((pap) => (
                      <div
                        key={pap._id}
                        className="flex items-center justify-between p-2 border rounded hover:bg-muted cursor-pointer"
                        onClick={() => addPersonnelByRole(pap, 'pap')}
                      >
                        <div className="flex-1 min-w-0">
                          <p className="font-medium text-sm truncate">{pap.name}</p>
                          <p className="text-xs text-muted-foreground truncate">{pap.email}</p>
                        </div>
                        <Button size="sm" variant="ghost" className="h-6 w-6 p-0">
                          <Plus className="h-3 w-3" />
                        </Button>
                      </div>
                    ))
                  ) : (
                    <div className="flex items-center justify-center h-full">
                      <p className="text-muted-foreground text-sm">
                        {papSearch.trim() ? t('events.noPapAgentsFound') : t('events.noAvailablePapAgents')}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Cooks */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ChefHat className="h-5 w-5" />
            {t('events.form.cooks')}
            <Badge variant="secondary">{personnel.cooks.length}</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* Assigned Cooks */}
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-muted-foreground">{t('events.assigned')}</h4>
              <div className="space-y-2 min-h-[150px] h-[200px] overflow-y-auto border rounded-md p-2 bg-gray-50">
                {personnel.cooks.length > 0 ? (
                  personnel.cooks.map((cook) => (
                    <div key={cook.userId} className="flex items-center justify-between p-2 border rounded bg-orange-50 border-orange-200">
                      <div className="flex-1 min-w-0">
                        <p className="font-medium text-sm truncate">{cook.name}</p>
                        <p className="text-xs text-muted-foreground truncate">{cook.email}</p>
                      </div>
                      {!readOnly && (
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => removePersonnel(cook.userId, 'cook')}
                          className="h-6 w-6 p-0"
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      )}
                    </div>
                  ))
                ) : (
                  <div className="flex items-center justify-center h-full">
                    <p className="text-muted-foreground text-sm">
                      {t('events.form.noCooksAssigned')}
                    </p>
                  </div>
                )}
              </div>
            </div>

            {/* Available Cooks */}
            {!readOnly && (
              <div className="space-y-2">
                <h4 className="text-sm font-medium text-muted-foreground">{t('events.availableToAssign')}</h4>
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder={t('events.searchCooks')}
                    value={cookSearch}
                    onChange={(e) => setCookSearch(e.target.value)}
                    className="pl-8"
                  />
                </div>
                <div className="space-y-2 min-h-[150px] h-[200px] overflow-y-auto border rounded-md p-2 bg-white">
                  {getAvailableUsersByRole('cook').length > 0 ? (
                    getAvailableUsersByRole('cook').map((cook) => (
                      <div
                        key={cook._id}
                        className="flex items-center justify-between p-2 border rounded hover:bg-muted cursor-pointer"
                        onClick={() => addPersonnelByRole(cook, 'cook')}
                      >
                        <div className="flex-1 min-w-0">
                          <p className="font-medium text-sm truncate">{cook.name}</p>
                          <p className="text-xs text-muted-foreground truncate">{cook.email}</p>
                        </div>
                        <Button size="sm" variant="ghost" className="h-6 w-6 p-0">
                          <Plus className="h-3 w-3" />
                        </Button>
                      </div>
                    ))
                  ) : (
                    <div className="flex items-center justify-center h-full">
                      <p className="text-muted-foreground text-sm">
                        {cookSearch.trim() ? t('events.noCooksFound') : t('events.noAvailableCooks')}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
