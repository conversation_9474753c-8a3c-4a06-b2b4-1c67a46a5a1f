'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { CalendarIcon, Clock } from 'lucide-react';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { cn } from '@/lib/utils';
import { useLanguage } from '@/lib/contexts/language-context';

interface DateTimePickerProps {
  type: 'date' | 'time';
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  error?: boolean;
  disabled?: boolean;
}

export function DateTimePicker({
  type,
  value,
  onChange,
  placeholder,
  error = false,
  disabled = false
}: DateTimePickerProps) {
  const [open, setOpen] = useState(false);
  const { t, language } = useLanguage();
  const locale = language === 'fr' ? fr : undefined;

  if (type === 'date') {
    const selectedDate = value ? new Date(value + 'T00:00:00') : undefined;

    return (
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={cn(
              'w-full justify-start text-left font-normal',
              !value && 'text-muted-foreground',
              error && 'border-red-500',
              'hover:bg-gray-50'
            )}
            disabled={disabled}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {value ? format(new Date(value + 'T00:00:00'), 'PPP', { locale }) : placeholder || t('common.pickADate')}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <Calendar
            mode="single"
            selected={selectedDate}
            onSelect={(date) => {
              if (date) {
                onChange(format(date, 'yyyy-MM-dd'));
              }
              setOpen(false);
            }}
            disabled={(date) => date < new Date('1900-01-01')}
            initialFocus
            locale={locale}
          />
        </PopoverContent>
      </Popover>
    );
  }

  if (type === 'time') {
    return (
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={cn(
              'w-full justify-start text-left font-normal',
              !value && 'text-muted-foreground',
              error && 'border-red-500',
              'hover:bg-gray-50'
            )}
            disabled={disabled}
          >
            <Clock className="mr-2 h-4 w-4" />
            {value || placeholder || t('events.reports.form.selectTime')}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-4" align="start">
          <div className="space-y-4">
            <div className="text-sm font-medium">{t('events.reports.form.selectTime')}</div>
            <div className="grid grid-cols-2 gap-2">
              <div>
                <label className="text-xs text-muted-foreground">{t('common.time')}</label></div>
                <select
                  className="w-full p-2 border rounded text-sm"
                  value={value ? value.split(':')[0] : '09'}
                  onChange={(e) => {
                    const hour = e.target.value;
                    const minute = value ? value.split(':')[1] : '00';
                    onChange(`${hour}:${minute}`);
                  }}
                >
                  {Array.from({ length: 24 }, (_, i) => (
                    <option key={i} value={i.toString().padStart(2, '0')}>
                      {i.toString().padStart(2, '0')}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="text-xs text-muted-foreground">{t('common.minute')}</label>
                <select
                  className="w-full p-2 border rounded text-sm"
                  value={value ? value.split(':')[1] : '00'}
                  onChange={(e) => {
                    const minute = e.target.value;
                    const hour = value ? value.split(':')[0] : '09';
                    onChange(`${hour}:${minute}`);
                  }}
                >
                  {Array.from({ length: 60 }, (_, i) => (
                    <option key={i} value={i.toString().padStart(2, '0')}>
                      {i.toString().padStart(2, '0')}
                    </option>
                  ))}
                </select>
              </div>
            </div>
            <div className="flex justify-end gap-2">
              <Button
                size="sm"
                variant="outline"
                onClick={() => setOpen(false)}
              >
                {t('common.cancel')}
              </Button>
              <Button
                size="sm"
                onClick={() => setOpen(false)}
              >
                {t('common.confirm')}
              </Button>
            </div>
        </PopoverContent>
      </Popover>
    );
  }

  return null;
}
