'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAppSelector } from '@/lib/redux/hooks';
import { useLanguage } from '@/lib/contexts/language-context';
import { canUserEditEvents, canUserViewEvents, canUserSuperviseEvents } from '@/lib/utils/permissions-utils';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Calendar,
  MapPin,
  Users,
  Clock,
  ArrowRight,
  Edit,
  FileText,
  AlertTriangle
} from 'lucide-react';
import { EventStatusBadge } from './EventStatusBadge';
import { formatDistanceToNow, format } from 'date-fns';
import { fr } from 'date-fns/locale';

interface EventCardProps {
  event: {
    _id: string;
    name: string;
    startDate: string;
    endDate: string;
    location: string;
    status: 'new' | 'in_progress' | 'cancelled' | 'processing_report' | 'awaiting_validation' | 'done';
    branchId?: {
      _id: string;
      name: string;
    } | null;
    partnerId?: {
      _id: string;
      name: string;
    } | null;
    eventTypeId?: {
      _id: string;
      name: string;
      code: string;
    } | null;
    supervisors?: Array<{
      _id: string;
      name: string;
      email: string;
    }> | null;
    cooks?: Array<{
      _id: string;
      name: string;
      email: string;
    }> | null;
    agents?: {
      requiredCount?: number;
      assignedAgents?: Array<{
        _id: string;
        name: string;
        email: string;
      }>;
    } | null;
    reportId?: {
      _id: string;
      status: 'pending' | 'processing' | 'submitted' | 'validated';
    };
    clientGoal?: number;
    notes?: string;
  };
  showActions?: boolean;
  compact?: boolean;
  onStatusChange?: (eventId: string, newStatus: string) => void;
  shouldShowViewDetailsButton?: boolean;
}

export function EventCard({
  event,
  showActions = true,
  compact = false,
  onStatusChange,
  shouldShowViewDetailsButton = true
}: EventCardProps) {
  const router = useRouter();
  const [isUpdating, setIsUpdating] = useState(false);
  const permissions = useAppSelector(state => state.permissions);
  const { t } = useLanguage();

  // Check permissions
  const canEditEvents = permissions && canUserEditEvents(permissions);
  const canViewEvents = permissions && canUserViewEvents(permissions);
  const canSuperviseEvents = permissions && canUserSuperviseEvents(permissions);

  // Supervisors should not see "See Details" button since they get redirected to reports

  const formatEventDate = (startDate: string, endDate: string) => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const { language } = useLanguage();
    const locale = language === 'fr' ? fr : undefined;

    if (format(start, 'yyyy-MM-dd') === format(end, 'yyyy-MM-dd')) {
      // Same day
      return `${format(start, 'MMM d, yyyy', { locale })} • ${format(start, 'HH:mm')} - ${format(end, 'HH:mm')}`;
    } else {
      // Different days
      return `${format(start, 'MMM d', { locale })} - ${format(end, 'MMM d, yyyy', { locale })}`;
    }
  };

  const getTimeUntilEvent = () => {
    const now = new Date();
    const startDate = new Date(event.startDate);
    const endDate = new Date(event.endDate);
    const { language } = useLanguage();
    const locale = language === 'fr' ? fr : undefined;

    if (now > endDate) {
      // French: "Terminé il y a 3 mois" / English: "Ended 3 months ago"
      return language === 'fr'
        ? `${t('events.card.ended')} ${t('events.card.ago')} ${formatDistanceToNow(endDate, { locale })}`
        : `${t('events.card.ended')} ${formatDistanceToNow(endDate, { locale })} ${t('events.card.ago')}`;
    } else if (now > startDate) {
      return t('events.card.inProgress');
    } else {
      // French: "Commence dans 3 heures" / English: "Starts in 3 hours"
      return `${t('events.card.starts')} ${formatDistanceToNow(startDate, { locale })}`;
    }
  };

  const isOverdue = () => {
    const now = new Date();
    const endDate = new Date(event.endDate);
    return now > endDate && !['done', 'cancelled'].includes(event.status);
  };

  const needsAttention = () => {
    return isOverdue() || (event.status === 'processing_report' && !event.reportId);
  };

  const handleViewEvent = () => {
    router.push(`/events/${event._id}`);
  };

  const handleEditEvent = () => {
    router.push(`/events/${event._id}/edit`);
  };

  const handleManageReport = () => {
    if (event.reportId) {
      router.push(`/events/${event._id}/report`);
    } else {
      router.push(`/events/${event._id}/report/edit`);
    }
  };

  const handleEditReport = () => {
    router.push(`/events/${event._id}/report/edit`);
  };

  const handleStatusChange = async (newStatus: string) => {
    if (!onStatusChange) return;
    
    setIsUpdating(true);
    try {
      await onStatusChange(event._id, newStatus);
    } finally {
      setIsUpdating(false);
    }
  };

  if (compact) {
    return (
      <Card className={`hover:shadow-md transition-shadow ${needsAttention() ? 'ring-2 ring-orange-200' : ''}`}>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <h3 className="font-medium truncate">{event.name}</h3>
                <EventStatusBadge status={event.status} />
                {needsAttention() && (
                  <AlertTriangle className="h-4 w-4 text-orange-500 flex-shrink-0" />
                )}
              </div>
              <p className="text-sm text-muted-foreground truncate">
                {event.location} • {formatEventDate(event.startDate, event.endDate)}
              </p>
            </div>
            {shouldShowViewDetailsButton && (
              <Button variant="ghost" size="sm" onClick={handleViewEvent}>
                <ArrowRight className="h-4 w-4" />
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`hover:shadow-md transition-shadow ${needsAttention() ? 'ring-2 ring-orange-200' : ''}`}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-2">
              <CardTitle className="text-lg truncate">{event.name}</CardTitle>
              <EventStatusBadge status={event.status} />
              {needsAttention() && (
                <AlertTriangle className="h-4 w-4 text-orange-500 flex-shrink-0" />
              )}
            </div>
            <div className="flex items-center gap-4 text-sm text-muted-foreground">
              <div className="flex items-center gap-1">
                <MapPin className="h-4 w-4" />
                <span className="truncate">{event.location}</span>
              </div>
              <div className="flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                <span>{formatEventDate(event.startDate, event.endDate)}</span>
              </div>
            </div>
          </div>
          

        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        <div className="space-y-3">
          {/* Event Details */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm">
            <div>
              <span className="text-muted-foreground">{t('events.card.branch')}:</span>
              <span className="ml-2 font-medium">{event.branchId?.name || t('common.na')}</span>
            </div>
            <div>
              <span className="text-muted-foreground">{t('events.card.partner')}:</span>
              <span className="ml-2 font-medium">{event.partnerId?.name || t('common.na')}</span>
            </div>
            {event.eventTypeId && (
              <div>
                <span className="text-muted-foreground">{t('events.card.type')}:</span>
                <span className="ml-2 font-medium">{event.eventTypeId?.name || t('common.na')}</span>
              </div>
            )}
            {event.clientGoal && (
              <div>
                <span className="text-muted-foreground">{t('events.card.goal')}:</span>
                <span className="ml-2 font-medium">{event.clientGoal} {t('events.card.clients')}</span>
              </div>
            )}
          </div>

          {/* Personnel */}
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm">
              <Users className="h-4 w-4 text-muted-foreground" />
              <span className="text-muted-foreground">{t('events.card.personnel')}:</span>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-2 text-sm">
              <div>
                <span className="text-muted-foreground">{t('events.card.supervisors')}:</span>
                <div className="mt-1">
                  {event.supervisors && event.supervisors.length > 0 ? (
                    event.supervisors.map((supervisor) => (
                      <Badge key={supervisor?._id || Math.random()} variant="secondary" className="mr-1 mb-1">
                        {supervisor?.name || t('common.unknown')}
                      </Badge>
                    ))
                  ) : (
                    <span className="text-muted-foreground italic">{t('events.card.noneAssigned')}</span>
                  )}
                </div>
              </div>
              <div>
                <span className="text-muted-foreground">{t('events.card.papAgents')}:</span>
                <div className="mt-1">
                  {event.agents?.assignedAgents && event.agents.assignedAgents.length > 0 ? (
                    event.agents.assignedAgents.map((agent) => (
                      <Badge key={agent?._id || Math.random()} variant="default" className="mr-1 mb-1">
                        {agent?.name || t('common.unknown')}
                      </Badge>
                    ))
                  ) : (
                    <span className="text-muted-foreground italic">{t('events.card.noneAssigned')}</span>
                  )}
                </div>
              </div>
              <div>
                <span className="text-muted-foreground">{t('events.card.cooks')}:</span>
                <div className="mt-1">
                  {event.cooks && event.cooks.length > 0 ? (
                    event.cooks.map((cook) => (
                      <Badge key={cook?._id || Math.random()} variant="outline" className="mr-1 mb-1">
                        {cook?.name || t('common.unknown')}
                      </Badge>
                    ))
                  ) : (
                    <span className="text-muted-foreground italic">{t('events.card.noneAssigned')}</span>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Timing Info */}
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Clock className="h-4 w-4" />
            <span>{getTimeUntilEvent()}</span>
          </div>

          {/* Report Status */}
          {event.reportId && (
            <div className="flex items-center gap-2 text-sm">
              <FileText className="h-4 w-4 text-muted-foreground" />
              <span className="text-muted-foreground">{t('events.card.report')}:</span>
              <EventStatusBadge status={event.reportId.status} />
            </div>
          )}

          {/* Notes */}
          {event.notes && (
            <div className="text-sm">
              <span className="text-muted-foreground">{t('events.card.notes')}:</span>
              <p className="mt-1 text-sm bg-muted p-2 rounded">{event.notes}</p>
            </div>
          )}

          {/* Actions */}
          {showActions && (
            <div className="flex gap-2 pt-2">
              {shouldShowViewDetailsButton && (
                <Button variant="outline" size="sm" onClick={handleViewEvent}>
                  {t('events.card.viewDetails')}
                </Button>
              )}
              <Button variant="outline" size="sm" onClick={handleManageReport}>
                {event.reportId ? t('events.card.viewReport') : t('events.card.createReport')}
              </Button>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
