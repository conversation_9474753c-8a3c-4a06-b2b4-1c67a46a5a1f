'use client';

import { Badge } from '@/components/ui/badge';
import { useLanguage } from '@/lib/contexts/language-context';
import { 
  Circle, 
  Clock, 
  Play, 
  FileText, 
  AlertCircle, 
  CheckCircle, 
  XCircle,
  Pause
} from 'lucide-react';

interface EventStatusBadgeProps {
  status: string;
  size?: 'sm' | 'default' | 'lg';
  showIcon?: boolean;
  className?: string;
}

export function EventStatusBadge({
  status,
  size = 'default',
  showIcon = true,
  className = ''
}: EventStatusBadgeProps) {
  const { t } = useLanguage();

  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'new':
        return {
          label: t('events.status.new'),
          variant: 'default' as const,
          className: 'bg-blue-100 text-blue-800 border-blue-200 hover:bg-blue-200',
          icon: <Circle className="h-3 w-3" />
        };
      case 'in_progress':
        return {
          label: t('events.status.inProgress'),
          variant: 'default' as const,
          className: 'bg-yellow-100 text-yellow-800 border-yellow-200 hover:bg-yellow-200',
          icon: <Play className="h-3 w-3" />
        };
      case 'processing_report':
        return {
          label: t('events.status.processingReport'),
          variant: 'default' as const,
          className: 'bg-purple-100 text-purple-800 border-purple-200 hover:bg-purple-200',
          icon: <FileText className="h-3 w-3" />
        };
      case 'awaiting_validation':
        return {
          label: t('events.status.awaitingValidation'),
          variant: 'default' as const,
          className: 'bg-orange-100 text-orange-800 border-orange-200 hover:bg-orange-200',
          icon: <AlertCircle className="h-3 w-3" />
        };
      case 'done':
        return {
          label: t('events.status.completed'),
          variant: 'default' as const,
          className: 'bg-green-100 text-green-800 border-green-200 hover:bg-green-200',
          icon: <CheckCircle className="h-3 w-3" />
        };
      case 'cancelled':
        return {
          label: t('events.status.cancelled'),
          variant: 'default' as const,
          className: 'bg-red-100 text-red-800 border-red-200 hover:bg-red-200',
          icon: <XCircle className="h-3 w-3" />
        };
      // Report statuses
      case 'pending':
        return {
          label: t('events.status.pending'),
          variant: 'default' as const,
          className: 'bg-gray-100 text-gray-800 border-gray-200 hover:bg-gray-200',
          icon: <Pause className="h-3 w-3" />
        };
      case 'processing':
        return {
          label: t('events.status.processing'),
          variant: 'default' as const,
          className: 'bg-blue-100 text-blue-800 border-blue-200 hover:bg-blue-200',
          icon: <Clock className="h-3 w-3" />
        };
      case 'submitted':
        return {
          label: t('events.status.submitted'),
          variant: 'default' as const,
          className: 'bg-indigo-100 text-indigo-800 border-indigo-200 hover:bg-indigo-200',
          icon: <FileText className="h-3 w-3" />
        };
      case 'validated':
        return {
          label: t('events.status.validated'),
          variant: 'default' as const,
          className: 'bg-green-100 text-green-800 border-green-200 hover:bg-green-200',
          icon: <CheckCircle className="h-3 w-3" />
        };
      default:
        return {
          label: status.charAt(0).toUpperCase() + status.slice(1).replace('_', ' '),
          variant: 'secondary' as const,
          className: 'bg-gray-100 text-gray-800 border-gray-200',
          icon: <Circle className="h-3 w-3" />
        };
    }
  };

  const config = getStatusConfig(status);
  
  const sizeClasses = {
    sm: 'text-xs px-2 py-0.5',
    default: 'text-xs px-2.5 py-0.5',
    lg: 'text-sm px-3 py-1'
  };

  return (
    <Badge 
      variant={config.variant}
      className={`
        inline-flex items-center gap-1 font-medium border
        ${sizeClasses[size]}
        ${config.className}
        ${className}
      `}
    >
      {showIcon && config.icon}
      <span>{config.label}</span>
    </Badge>
  );
}

// Helper function to get status priority for sorting
export function getStatusPriority(status: string): number {
  const priorities = {
    'cancelled': 0,
    'done': 1,
    'validated': 1,
    'awaiting_validation': 2,
    'submitted': 3,
    'processing_report': 4,
    'processing': 4,
    'in_progress': 5,
    'pending': 6,
    'new': 7
  };
  
  return priorities[status as keyof typeof priorities] ?? 999;
}

// Helper function to check if status indicates completion
export function isCompletedStatus(status: string): boolean {
  return ['done', 'validated', 'cancelled'].includes(status);
}

// Helper function to check if status indicates active work
export function isActiveStatus(status: string): boolean {
  return ['in_progress', 'processing_report', 'processing'].includes(status);
}

// Helper function to check if status needs attention
export function needsAttentionStatus(status: string): boolean {
  return ['awaiting_validation', 'submitted'].includes(status);
}

// Helper function to get next possible statuses for transitions
export function getNextStatuses(currentStatus: string): string[] {
  switch (currentStatus) {
    case 'new':
      return ['in_progress', 'cancelled'];
    case 'in_progress':
      return ['processing_report', 'cancelled'];
    case 'processing_report':
      return ['awaiting_validation', 'in_progress'];
    case 'awaiting_validation':
      return ['done', 'processing_report'];
    case 'pending':
      return ['processing', 'submitted'];
    case 'processing':
      return ['submitted', 'pending'];
    case 'submitted':
      return ['validated', 'processing'];
    default:
      return [];
  }
}

// Status transition descriptions
export function getStatusDescription(status: string): string {
  const descriptions = {
    'new': 'Event has been created and is awaiting assignment',
    'in_progress': 'Event is currently active with assigned personnel',
    'processing_report': 'Event has ended and report is being prepared',
    'awaiting_validation': 'Report has been submitted and awaits admin validation',
    'done': 'Event and report have been completed and validated',
    'cancelled': 'Event has been cancelled',
    'pending': 'Report is pending completion',
    'processing': 'Report is being processed by supervisor',
    'submitted': 'Report has been submitted for validation',
    'validated': 'Report has been validated by admin'
  };
  
  return descriptions[status as keyof typeof descriptions] ?? 'Unknown status';
}
