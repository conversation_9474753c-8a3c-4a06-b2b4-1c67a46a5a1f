'use client';

import { useState, useEffect } from 'react';
import { Calendar } from '@/components/ui/calendar';
import { useLanguage } from '@/lib/contexts/language-context';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { format, addMonths, subMonths, startOfMonth } from 'date-fns';
import { fr } from 'date-fns/locale';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface Event {
  id: string;
  name: string;
  startDate: Date;
  endDate: Date;
  location: string;
  type: string;
  partnerId: string;
  clientGoal: number;
  branchId: string;
}

interface EventsCalendarProps {
  view: 'month' | 'week' | 'day';
}

const mockEvents: Event[] = [
  // Sample data for development
];

const colorMap: Record<string, string> = {
  // Define colors for branches
  'branch1': 'bg-red-500',
  'branch2': 'bg-blue-500',
  'branch3': 'bg-green-500',
  'branch4': 'bg-purple-500',
  'branch5': 'bg-yellow-500',
};

export default function EventsCalendar({ view }: EventsCalendarProps) {
  const { t, language } = useLanguage();
  const locale = language === 'fr' ? fr : undefined;
  const [date, setDate] = useState<Date>(new Date());
  const [events, setEvents] = useState<Event[]>(mockEvents);
  const [selectedBranch, setSelectedBranch] = useState<string>('all');
  const [isLoading, setIsLoading] = useState(false);
  
  // These would be fetched from an API
  const branches = [
    { id: 'branch1', name: 'Branch 1' },
    { id: 'branch2', name: 'Branch 2' },
    { id: 'branch3', name: 'Branch 3' },
  ];
  
  const fetchEvents = async () => {
    // This would be an API call in production
    setIsLoading(true);
    try {
      // Simulate API call with setTimeout
      await new Promise(resolve => setTimeout(resolve, 500));
      setEvents(mockEvents);
    } catch (error) {
      console.error('Error fetching events:', error);
    } finally {
      setIsLoading(false);
    }
  };
  
  useEffect(() => {
    fetchEvents();
  }, [date, selectedBranch]);
  
  const handlePrevMonth = () => {
    setDate(prevDate => subMonths(prevDate, 1));
  };
  
  const handleNextMonth = () => {
    setDate(prevDate => addMonths(prevDate, 1));
  };
  
  const renderDay = (day: Date) => {
    const dayEvents = events.filter(event => {
      const eventDate = new Date(event.startDate);
      return eventDate.getDate() === day.getDate() && 
             eventDate.getMonth() === day.getMonth() && 
             eventDate.getFullYear() === day.getFullYear() &&
             (selectedBranch === 'all' || event.branchId === selectedBranch);
    });
    
    return (
      <div className="h-full min-h-[80px]">
        <div className="font-medium">{day.getDate()}</div>
        <div className="space-y-1 mt-1">
          {dayEvents.map(event => (
            <div 
              key={event.id}
              className={`text-xs p-1 rounded-sm truncate ${colorMap[event.branchId] || 'bg-gray-200'} text-white`}
              title={event.name}
            >
              {event.name}
            </div>
          ))}
        </div>
      </div>
    );
  };
  
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="icon"
            onClick={handlePrevMonth}
            disabled={isLoading}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <div className="font-medium">
            {format(date, 'MMMM yyyy', { locale })}
          </div>
          <Button
            variant="outline"
            size="icon"
            onClick={handleNextMonth}
            disabled={isLoading}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
        
        <div className="flex items-center gap-2">
          <Select 
            value={selectedBranch} 
            onValueChange={setSelectedBranch}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder={t('events.selectBranch')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t('events.allBranches')}</SelectItem>
              {branches.map(branch => (
                <SelectItem key={branch.id} value={branch.id}>
                  {branch.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => fetchEvents()}
            disabled={isLoading}
          >
            {t('common.refresh')}
          </Button>
        </div>
      </div>
      
      <Card>
        <CardContent className="p-0">
          <Calendar
            defaultMonth={date}
            showOutsideDays
            fixedWeeks
            locale={locale}
            footer={
              isLoading
                ? t('events.loading')
                : events.length === 0
                  ? t('events.noEvents')
                  : undefined
            }
            modifiers={{ 
              hasEvent: (day) => {
                return events.some(event => {
                  const eventDate = new Date(event.startDate);
                  return eventDate.getDate() === day.getDate() && 
                         eventDate.getMonth() === day.getMonth() && 
                         eventDate.getFullYear() === day.getFullYear() &&
                         (selectedBranch === 'all' || event.branchId === selectedBranch);
                });
              }
            }}
            modifiersClassNames={{
              hasEvent: 'has-event'
            }}
            components={{
              Day: (props) => {
                // Custom day rendering
                const { date: day } = props;
                
                const dayEvents = events.filter(event => {
                  const eventDate = new Date(event.startDate);
                  return eventDate.getDate() === day.getDate() && 
                         eventDate.getMonth() === day.getMonth() && 
                         eventDate.getFullYear() === day.getFullYear() &&
                         (selectedBranch === 'all' || event.branchId === selectedBranch);
                });
                
                return (
                  <div className="h-full min-h-[80px]">
                    <div className="font-medium">{day.getDate()}</div>
                    <div className="space-y-1 mt-1">
                      {dayEvents.map(event => (
                        <div 
                          key={event.id}
                          className={`text-xs p-1 rounded-sm truncate ${colorMap[event.branchId] || 'bg-gray-200'} text-white`}
                          title={event.name}
                        >
                          {event.name}
                        </div>
                      ))}
                    </div>
                  </div>
                );
              }
            }}
          />
        </CardContent>
      </Card>
      
      {/* Color legend */}
      <div className="flex flex-wrap gap-2 mt-4">
        {branches.map(branch => (
          <div key={branch.id} className="flex items-center gap-1">
            <div className={`w-3 h-3 rounded-full ${colorMap[branch.id] || 'bg-gray-200'}`}></div>
            <span className="text-xs">{branch.name}</span>
          </div>
        ))}
      </div>
    </div>
  );
} 