'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { useLanguage } from '@/lib/contexts/language-context';
import { 
  Card, 
  CardContent,
  CardFooter,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import {
  ChevronLeft,
  ChevronRight,
  Calendar as CalendarIcon,
  RefreshCw,
  Plus,
  Users,
  FileText,
  AlertTriangle,
  Eye,
  Edit,
  Filter,
  Search,
  X
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  format,
  addMonths,
  subMonths,
  startOfMonth,
  endOfMonth,
  startOfWeek,
  endOfWeek,
  eachDayOfInterval,
  isSameMonth,
  isSameDay,
  addDays,
  formatISO,
} from 'date-fns';
import { fr } from 'date-fns/locale';
import { useAppSelector } from '@/lib/redux/hooks';
import { toast } from 'sonner';
import EventForm from './EventForm';

// Types import
import { Event } from '@/types/event';
import { Partner } from '@/types/partner';
import { EventType } from '@/types/event';
import { Branch } from '@/types/branch';
import { canUserCreateEvents, canUserDeleteEvents, canUserEditEvents } from '@/lib/utils/permissions-utils';

interface FullCalendarProps {
  className?: string;
  onAddEvent?: (date?: Date) => void;
  onEditEvent?: (eventId: string) => void;
  refresh?: number;
}

interface EventTooltipProps {
  event: Event;
  onDuplicate: (event: Event) => void;
  onDelete: (eventId: string) => void;
  getBranchName: (branchId: string | { _id: string; name: string }) => string;
}

function EventTooltipContent({ event, onDuplicate, onDelete, getBranchName }: EventTooltipProps) {
  const { t, language } = useLanguage();
  const locale = language === 'fr' ? fr : undefined;
  const permissions = useAppSelector(state => state.permissions);
  const canCreateEvents = canUserCreateEvents(permissions);
  const canDeleteEvents = canUserDeleteEvents(permissions);
  return (
    <div className="min-w-[280px] max-w-[360px] p-3 space-y-3 text-sm">
      <div className="font-bold text-base break-words">{event.name}</div>

      <div className="space-y-2">
        <div className="flex items-center gap-2">
          <span className="font-medium">{t('events.branch')}:</span>
          <span>{getBranchName(event.branchId)}</span>
        </div>

        <div className="flex items-center gap-2">
          <span className="font-medium">{t('events.time')}:</span>
          <span>{format(new Date(event.startDate), 'MMM d, h:mm a', { locale })} - {format(new Date(event.endDate), 'h:mm a', { locale })}</span>
        </div>

        <div className="flex items-center gap-2">
          <span className="font-medium">{t('events.location')}:</span>
          <span>{event.location}</span>
        </div>

        <div className="flex items-center gap-2">
          <span className="font-medium">{t('events.status.label')}:</span>
          <Badge variant="secondary" className="text-xs">
            {t(`events.statuses.${event.status}`) || event.status?.replace('_', ' ')}
          </Badge>
        </div>

        {event.supervisors && event.supervisors.length > 0 && (
          <div className="flex items-center gap-2">
            <span className="font-medium">{t('events.supervisors')}:</span>
            <Badge variant="outline" className="text-xs">
              <Users className="h-3 w-3 mr-1" />
              {event.supervisors.length}
            </Badge>
          </div>
        )}

        {event.reportId && (
          <div className="flex items-center gap-2">
            <span className="font-medium">{t('events.report')}:</span>
            <Badge variant={getReportStatusVariant((event.reportId as any)?.status || 'pending')} className="text-xs">
              <FileText className="h-3 w-3 mr-1" />
              {t(`events.reportStatuses.${(event.reportId as any)?.status}`) || (event.reportId as any)?.status || t('events.reportStatuses.pending')}
            </Badge>
            {isReportOverdue(event) && (
              <Badge variant="destructive" className="text-xs">
                <AlertTriangle className="h-3 w-3 mr-1" />
                {t('events.overdue')}
              </Badge>
            )}
          </div>
        )}
      </div>

      <div className="flex gap-2 pt-2 border-t">
        {canCreateEvents && (
          <Button size="sm" variant="outline" onClick={e => { e.stopPropagation(); onDuplicate(event); }}>
            {t('common.duplicate')}
          </Button>
        )}
        {!canCreateEvents && process.env.NODE_ENV === 'development' && (
          <span className="ml-2 text-xs text-destructive">[No CREATE_EVENTS permission]</span>
        )}
        {canDeleteEvents && (
          <Button size="sm" variant="destructive" onClick={e => { e.stopPropagation(); onDelete(event._id); }}>
            {t('common.delete')}
          </Button>
        )}
        {!canDeleteEvents && process.env.NODE_ENV === 'development' && (
          <span className="ml-2 text-xs text-destructive">[No DELETE_EVENTS permission]</span>
        )}
      </div>
    </div>
  );
}

// Define color palette for different branches
const branchColors = [
  'bg-blue-500 hover:bg-blue-600',
  'bg-green-500 hover:bg-green-600',
  'bg-purple-500 hover:bg-purple-600',
  'bg-red-500 hover:bg-red-600',
  'bg-amber-500 hover:bg-amber-600',
  'bg-teal-500 hover:bg-teal-600',
  'bg-pink-500 hover:bg-pink-600',
  'bg-indigo-500 hover:bg-indigo-600',
  'bg-orange-500 hover:bg-orange-600',
  'bg-emerald-500 hover:bg-emerald-600',
];

// Status color mapping for enhanced calendar
const statusColors = {
  new: 'border-l-4 border-l-gray-500',
  in_progress: 'border-l-4 border-l-blue-500',
  cancelled: 'border-l-4 border-l-red-500',
  processing_report: 'border-l-4 border-l-orange-500',
  awaiting_validation: 'border-l-4 border-l-purple-500',
  done: 'border-l-4 border-l-green-500'
};

// Utility functions for enhanced calendar
const getStatusColor = (status: string) => {
  return statusColors[status as keyof typeof statusColors] || 'border-l-4 border-l-gray-500';
};

const getReportStatusVariant = (status: string) => {
  switch (status) {
    case 'pending': return 'secondary';
    case 'processing': return 'default';
    case 'submitted': return 'outline';
    case 'validated': return 'default';
    default: return 'secondary';
  }
};

const isReportOverdue = (event: any) => {
  if (!event.reportId || event.status === 'done' || event.status === 'cancelled') {
    return false;
  }

  // Consider a report overdue if event ended more than 24 hours ago and report is still pending
  const eventEndTime = new Date(event.endDate);
  const now = new Date();
  const hoursSinceEnd = (now.getTime() - eventEndTime.getTime()) / (1000 * 60 * 60);

  return hoursSinceEnd > 24 && (!event.reportId?.status || event.reportId.status === 'pending');
};

// Add DropdownSearchInput component
interface DropdownSearchInputProps {
  value: string;
  onChange: (v: string) => void;
  isOpen: boolean;
  autoFocus?: boolean;
  placeholder?: string;
}
const DropdownSearchInput: React.FC<DropdownSearchInputProps> = ({ value, onChange, isOpen, autoFocus, placeholder }) => {
  const { t } = useLanguage();
  const inputRef = useRef<HTMLInputElement>(null);
  useEffect(() => {
    if (isOpen && autoFocus && inputRef.current) {
      inputRef.current.focus();
      if (process.env.NODE_ENV === 'development') {
        // eslint-disable-next-line no-console
        console.log('DropdownSearchInput focused');
      }
    }
  }, [isOpen, autoFocus]);
  return (
    <input
      ref={inputRef}
      value={value}
      onChange={e => onChange(e.target.value)}
      placeholder={placeholder || t('common.search')}
      className="h-8 px-2 text-sm border-muted focus:border-primary rounded shadow-none w-full"
      autoFocus={false}
      type="text"
    />
  );
};

export default function FullCalendar({ className, onAddEvent, onEditEvent, refresh }: FullCalendarProps) {
  const { t, language } = useLanguage();
  const locale = language === 'fr' ? fr : undefined;
  const permissions = useAppSelector(state => state.permissions);
  const [currentDate, setCurrentDate] = useState<Date>(new Date());
  const [events, setEvents] = useState<Event[]>([]);
  const [branches, setBranches] = useState<Branch[]>([]);
  const [partners, setPartners] = useState<Partner[]>([]);
  const [eventTypes, setEventTypes] = useState<EventType[]>([]);
  
  const [selectedBranch, setSelectedBranch] = useState<string>('all');
  const [selectedPartner, setSelectedPartner] = useState<string>('all');
  const [selectedEventType, setSelectedEventType] = useState<string>('all');
  const [selectedSupervisor, setSelectedSupervisor] = useState<string>('all');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [selectedReportStatus, setSelectedReportStatus] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState<string>('');

  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const [branchColorMap, setBranchColorMap] = useState<Record<string, string>>({});
  const [supervisors, setSupervisors] = useState<any[]>([]);

  const [duplicateEvent, setDuplicateEvent] = useState<Event | null>(null);
  const [showDuplicateModal, setShowDuplicateModal] = useState(false);

  const [calendarKey, setCalendarKey] = useState(0);

  // Permission check for creating events
  const canCreateEvents = canUserCreateEvents(permissions);
  const canEditEvents = canUserEditEvents(permissions);

  // Add state for dropdown open and search for each filter
  const [branchDropdownOpen, setBranchDropdownOpen] = useState(false);
  const [partnerDropdownOpen, setPartnerDropdownOpen] = useState(false);
  const [eventTypeDropdownOpen, setEventTypeDropdownOpen] = useState(false);
  const [supervisorDropdownOpen, setSupervisorDropdownOpen] = useState(false);
  const [statusDropdownOpen, setStatusDropdownOpen] = useState(false);
  const [reportStatusDropdownOpen, setReportStatusDropdownOpen] = useState(false);
  const [branchSearch, setBranchSearch] = useState('');
  const [partnerSearch, setPartnerSearch] = useState('');
  const [eventTypeSearch, setEventTypeSearch] = useState('');
  const [supervisorSearch, setSupervisorSearch] = useState('');

  // Filtered lists with safety checks
  const filteredBranches = branchSearch ?
    (Array.isArray(branches) ? branches.filter(b => b.name.toLowerCase().includes(branchSearch.toLowerCase())) : []) :
    (Array.isArray(branches) ? branches : []);
  const filteredPartners = partnerSearch ?
    (Array.isArray(partners) ? partners.filter(p => p.name.toLowerCase().includes(partnerSearch.toLowerCase())) : []) :
    (Array.isArray(partners) ? partners : []);
  const filteredEventTypes = eventTypeSearch ?
    (Array.isArray(eventTypes) ? eventTypes.filter(e => e.name.toLowerCase().includes(eventTypeSearch.toLowerCase())) : []) :
    (Array.isArray(eventTypes) ? eventTypes : []);
  const filteredSupervisors = supervisorSearch ?
    (Array.isArray(supervisors) ? supervisors.filter(s => s.name.toLowerCase().includes(supervisorSearch.toLowerCase())) : []) :
    (Array.isArray(supervisors) ? supervisors : []);

  // Fetch branches, partners, and event types
  const fetchMetadata = useCallback(async () => {
    try {
      // Fetch branches
      const branchesResponse = await fetch('/api/events/branches');
      if (!branchesResponse.ok) throw new Error('Failed to fetch branches');
      const branchesData = await branchesResponse.json();
      setBranches(branchesData);
      
      // Create a color map for branches
      const colorMap: Record<string, string> = {};
      branchesData.forEach((branch: Branch, index: number) => {
        const branchId = branch._id;
        colorMap[branchId] = branchColors[index % branchColors.length];
      });
      setBranchColorMap(colorMap);
      
      // Fetch partners
      const partnersResponse = await fetch('/api/events/partners');
      if (!partnersResponse.ok) throw new Error('Failed to fetch partners');
      const partnersData = await partnersResponse.json();
      setPartners(partnersData);
      
      // Fetch event types
      const eventTypesResponse = await fetch('/api/events/types');
      if (!eventTypesResponse.ok) throw new Error('Failed to fetch event types');
      const eventTypesData = await eventTypesResponse.json();
      setEventTypes(eventTypesData);

      // Fetch supervisors (users with supervisor permissions)
      try {
        const supervisorsResponse = await fetch('/api/users?role=supervisor');
        if (supervisorsResponse.ok) {
          const supervisorsData = await supervisorsResponse.json();
          console.log('Supervisors API response:', supervisorsData);
          // Ensure we always set an array
          const supervisorsArray = Array.isArray(supervisorsData) ? supervisorsData : (supervisorsData.users || []);
          setSupervisors(supervisorsArray);
        } else {
          console.warn('Failed to fetch supervisors:', supervisorsResponse.statusText);
          setSupervisors([]);
        }
      } catch (supervisorError) {
        console.error('Error fetching supervisors:', supervisorError);
        setSupervisors([]);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      console.error('Error fetching metadata:', err);
    }
  }, []);
  
  // Fetch events
  const fetchEvents = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Calculate date range based on view
      let startDateStr, endDateStr;
      
      const start = startOfWeek(startOfMonth(currentDate));
      const end = endOfWeek(endOfMonth(currentDate));
      startDateStr = formatISO(start, { representation: 'date' });
      endDateStr = formatISO(end, { representation: 'date' });
      
      // Build query params
      const params = new URLSearchParams();
      params.append('startDate', startDateStr);
      params.append('endDate', endDateStr);
      
      if (selectedBranch !== 'all') {
        params.append('branchId', selectedBranch);
      }
      
      if (selectedPartner !== 'all') {
        params.append('partnerId', selectedPartner);
      }
      
      if (selectedEventType !== 'all') {
        params.append('eventTypeId', selectedEventType);
      }

      if (selectedSupervisor !== 'all') {
        params.append('supervisorId', selectedSupervisor);
      }

      if (selectedStatus !== 'all') {
        params.append('status', selectedStatus);
      }

      if (selectedReportStatus !== 'all') {
        params.append('reportStatus', selectedReportStatus);
      }

      if (searchQuery.trim()) {
        params.append('search', searchQuery.trim());
      }

      // Fetch events
      const response = await fetch(`/api/events?${params.toString()}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch events: ${response.statusText}`);
      }

      const data = await response.json();
      // Handle both old format (array) and new format (object with events property)
      const eventsArray = Array.isArray(data) ? data : (data.events || []);
      setEvents(eventsArray);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      console.error('Error fetching events:', err);
    } finally {
      setIsLoading(false);
    }
  }, [currentDate, selectedBranch, selectedPartner, selectedEventType, selectedSupervisor, selectedStatus, selectedReportStatus, searchQuery]);
  
  // Initialize and fetch data
  useEffect(() => {
    fetchMetadata();
  }, [fetchMetadata]);
  
  useEffect(() => {
    fetchEvents();
  }, [fetchEvents, refresh]);
  
  // Navigation functions
  const navigatePrevious = () => {
    setCurrentDate(prev => subMonths(prev, 1));
  };
  
  const navigateNext = () => {
    setCurrentDate(prev => addMonths(prev, 1));
  };
  
  const navigateToday = () => {
    setCurrentDate(new Date());
  };
  
  // Get days to display based on view
  const getDaysToDisplay = () => {
    const monthStart = startOfMonth(currentDate);
    const calendarStart = startOfWeek(monthStart);
    
    // Calculate days for a 5-row grid (35 days) instead of a 6-row grid
    // This will always show at least the full current month
    const calendarEnd = addDays(calendarStart, 34); // 35 days total (5 weeks)
    
    return eachDayOfInterval({ start: calendarStart, end: calendarEnd });
  };
  
  // Get events for a specific day
  const getEventsForDay = (day: Date) => {
    if (!Array.isArray(events)) {
      return [];
    }
    return events.filter(event => {
      const eventStart = new Date(event.startDate);
      return isSameDay(eventStart, day) &&
        (selectedBranch === 'all' || event.branchId === selectedBranch || (typeof event.branchId === 'object' && event.branchId._id === selectedBranch)) &&
        (selectedPartner === 'all' || event.partnerId === selectedPartner);
    });
  };
  
  // Determine if a day is special (e.g., holiday, weekend)
  const isSpecialDay = (day: Date) => {
    const isWeekend = day.getDay() === 0 || day.getDay() === 6;
    
    // Mock holidays - in production, you'd get these from an API or config
    const holidays = [
      new Date(currentDate.getFullYear(), 0, 1),  // New Year's Day
      new Date(currentDate.getFullYear(), 6, 4),  // Independence Day
      new Date(currentDate.getFullYear(), 11, 25), // Christmas
      // Add more holidays as needed
    ];
    
    const isHoliday = holidays.some(holiday => isSameDay(holiday, day));
    
    return { isWeekend, isHoliday };
  };
  
  // Get theme classes for a day
  const getDayThemeClasses = (day: Date, isCurrentMonth: boolean) => {
    const { isWeekend, isHoliday } = isSpecialDay(day);
    const isToday = isSameDay(day, new Date());
    const dayEvents = getEventsForDay(day);
    
    let baseClasses = 'group relative flex flex-col p-2 border overflow-hidden transition-all duration-200 hover:bg-primary/5 cursor-pointer';
    
    // Border styling based on current day and current month
    if (isToday) {
      baseClasses += ' border-2 border-primary/60 border-solid';
    } else {
      baseClasses += ' border-border';
    }
    
    // Background styling based on month, weekends, and holidays
    if (!isCurrentMonth) {
      baseClasses += ' bg-muted/10';
    } else if (isHoliday) {
      baseClasses += ' bg-red-50 dark:bg-red-950/20';
    } else if (isWeekend) {
      baseClasses += ' bg-slate-50 dark:bg-slate-900/40';
    } else {
      baseClasses += ' bg-background';
    }
    
    // Additional styling based on events
    if (dayEvents.length > 5) {
      baseClasses += ' shadow-sm';
    }
    
    return baseClasses;
  };
  
  // Get the title for the current view
  const getViewTitle = () => {
    return format(currentDate, 'MMMM yyyy', { locale });
  };
  
  // Get the Branch name by ID
  const getBranchName = (branchId: string | { _id: string; name: string }) => {
    if (typeof branchId === 'object' && branchId !== null) {
      return branchId.name;
    }
    
    const branch = branches.find(b => b._id === branchId);
    return branch ? branch.name : 'Unknown Branch';
  };
  
  // Get the color for a specific branch
  const getBranchColor = (branchId: string | { _id: string; name: string }) => {
    const id = typeof branchId === 'object' && branchId !== null ? branchId._id : branchId;
    return branchColorMap[id] || 'bg-gray-500 hover:bg-gray-600';
  };
  
  // Handle event click for editing
  const handleEventClick = (e: React.MouseEvent, eventId: string) => {
    e.stopPropagation();
    if (canEditEvents) {
      if (onEditEvent) {
        onEditEvent(eventId);
      }
    } else {
      toast.error(t('common.noPermission'));
    }
  };
  
  const handleDuplicate = (event: Event) => {
    if (canCreateEvents) {
      setDuplicateEvent(event);
      setShowDuplicateModal(true);
    } else {
      toast.error(t('common.noPermission'));
    }
  };
  
  const handleDelete = async (eventId: string) => {
    if (!window.confirm(t('events.confirmDelete'))) return;
    try {
      const res = await fetch(`/api/events/${eventId}`, { method: 'DELETE' });
      if (!res.ok) throw new Error('Failed to delete');
      toast.success(t('events.deleted'));
      await fetchEvents();
      setCalendarKey(k => k + 1); // force rerender to close tooltips
    } catch (err) {
      toast.error(t('events.deleteError'));
    }
  };
  
  // Render day cells
  const renderCalendarDays = () => {
    const days = getDaysToDisplay();
    
    return (
      <div key={calendarKey} className="grid grid-cols-7 grid-rows-[auto_repeat(5,1fr)] gap-px h-full overflow-hidden">
        {/* Day headers */}
        {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((dayName, index) => (
          <div 
            key={`header-${index}`} 
            className="text-center font-semibold p-2 bg-muted text-muted-foreground"
          >
            {t(`common.days.short.${dayName.toLowerCase()}`)}
          </div>
        ))}
        
        {/* Calendar cells */}
        {days.map((day, index) => {
          const dayEvents = getEventsForDay(day);
          const isCurrentMonth = isSameMonth(day, currentDate);
          const isToday = isSameDay(day, new Date());
          
          return (
            <div 
              key={`day-${index}`}
              className={
                getDayThemeClasses(day, isCurrentMonth) +
                (!canCreateEvents ? ' cursor-not-allowed opacity-70 hover:bg-transparent' : '')
              }
              onClick={canCreateEvents ? (() => onAddEvent && onAddEvent(day)) : undefined}
              tabIndex={canCreateEvents ? 0 : -1}
              aria-disabled={!canCreateEvents}
            >
              {/* Date indicator */}
              <div className="text-sm font-medium mb-2 flex justify-between items-start">
                <div className="flex items-center gap-1">
                  <span className={`
                    flex items-center justify-center 
                    ${isToday ? 
                      'bg-primary text-primary-foreground font-bold w-8 h-8 rounded-full' : 
                      isCurrentMonth ? 'text-foreground' : 'text-muted-foreground'
                    }
                  `}>
                    {format(day, 'd')}
                  </span>
                  {/* Weekend indicator */}
                  {(day.getDay() === 0 || day.getDay() === 6) && 
                    <div className="w-1.5 h-1.5 rounded-full bg-amber-500 dark:bg-amber-400 ml-1" />
                  }
                </div>
                
                <div className="flex flex-col items-end">
                  {/* Events count indicator */}
                  {dayEvents.length > 0 && (
                    <div className="flex gap-0.5 mb-1">
                      {Array.from({length: Math.min(dayEvents.length, 3)}).map((_, i) => (
                        <div 
                          key={i}
                          className={`w-1.5 h-1.5 rounded-full ${
                            i === 0 ? 'bg-primary/70' : 
                            i === 1 ? 'bg-primary/50' : 
                            'bg-primary/30'
                          }`}
                        />
                      ))}
                      {dayEvents.length > 3 && (
                        <span className="text-[10px] text-muted-foreground ml-0.5">+{dayEvents.length - 3}</span>
                      )}
                    </div>
                  )}
                  
                  <span className="opacity-0 group-hover:opacity-100 transition-opacity text-xs flex items-center text-primary">
                    <Plus className="h-3 w-3 mr-1" />
                    {t('events.add')}
                    {!canCreateEvents && process.env.NODE_ENV === 'development' && (
                      <span className="ml-2 text-xs text-destructive">[No CREATE_EVENTS permission]</span>
                    )}
                  </span>
                </div>
              </div>
              
              {/* Events area with improved styling */}
              <div className="space-y-1 overflow-y-auto flex-1 min-h-0 max-h-full">
                {dayEvents.length > 0 ? (
                  <>
                    {dayEvents.map(event => (
                      <TooltipProvider key={event._id}>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div
                              className={`${getBranchColor(event.branchId)} ${getStatusColor(event.status)} text-white text-xs p-1.5 rounded-md cursor-pointer
                                hover:opacity-90 hover:shadow-md transition-all relative group`}
                              onClick={(e) => handleEventClick(e, event._id)}
                            >
                              {/* Event name */}
                              <div className="truncate font-medium mb-1">{event.name}</div>

                              {/* Status and indicators row */}
                              <div className="flex items-center gap-1 flex-wrap">
                                {/* Status badge */}
                                <Badge variant="secondary" className="text-[10px] px-1 py-0 h-4 bg-white/20 text-white border-white/30">
                                  {event.status?.replace('_', ' ')}
                                </Badge>

                                {/* Supervisor indicator */}
                                {event.supervisors && event.supervisors.length > 0 && (
                                  <Badge variant="outline" className="text-[10px] px-1 py-0 h-4 bg-white/20 text-white border-white/30">
                                    <Users className="h-2 w-2 mr-0.5" />
                                    {event.supervisors.length}
                                  </Badge>
                                )}

                                {/* Report status indicator */}
                                {event.reportId && (
                                  <Badge variant="outline" className="text-[10px] px-1 py-0 h-4 bg-white/20 text-white border-white/30">
                                    <FileText className="h-2 w-2 mr-0.5" />
                                    {(event.reportId as any)?.status || 'pending'}
                                  </Badge>
                                )}

                                {/* Urgency indicator for overdue reports */}
                                {isReportOverdue(event) && (
                                  <Badge variant="destructive" className="text-[10px] px-1 py-0 h-4 bg-red-500 text-white">
                                    <AlertTriangle className="h-2 w-2 mr-0.5" />
                                    {t('events.overdue')}
                                  </Badge>
                                )}
                              </div>

                              {/* Quick actions on hover */}
                              <div className="absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity">
                                <div className="flex gap-0.5">
                                  <Button size="sm" variant="ghost" className="h-4 w-4 p-0 text-white hover:bg-white/20">
                                    <Eye className="h-2 w-2" />
                                  </Button>
                                  <Button size="sm" variant="ghost" className="h-4 w-4 p-0 text-white hover:bg-white/20">
                                    <Edit className="h-2 w-2" />
                                  </Button>
                                </div>
                              </div>
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>
                            <EventTooltipContent
                              event={event}
                              onDuplicate={handleDuplicate}
                              onDelete={handleDelete}
                              getBranchName={getBranchName}
                            />
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    ))}
                  </>
                ) : dayEvents.length === 0 && isCurrentMonth ? (
                  <div className="h-full flex flex-col justify-center items-center text-xs text-muted-foreground italic opacity-60 text-center">
                    <CalendarIcon className="h-4 w-4 mb-1 opacity-40" />
                    <span>{t('events.noEvents')}</span>
                  </div>
                ) : null}
              </div>
              
              {/* Background pattern for visual interest on empty days */}
              {dayEvents.length === 0 && isCurrentMonth && (
                <div className="absolute inset-0 opacity-[0.02] pointer-events-none">
                  <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
                    <pattern id={`smallGrid-${index}`} width="8" height="8" patternUnits="userSpaceOnUse">
                      <path d="M 8 0 L 0 0 0 8" fill="none" stroke="currentColor" strokeWidth="0.5" opacity="0.5" />
                    </pattern>
                    <rect width="100%" height="100%" fill={`url(#smallGrid-${index})`} />
                  </svg>
                </div>
              )}
              
              {/* Hover effect gradient at the bottom of the cell */}
              <div className="absolute inset-x-0 bottom-0 h-1 bg-gradient-to-r from-transparent via-transparent to-transparent group-hover:from-primary/20 group-hover:via-primary/30 group-hover:to-primary/20 transition-all duration-300"></div>
            </div>
          );
        })}
      </div>
    );
  };
  
  return (
    <div className={`h-full flex flex-col ${className}`}>
      {/* Calendar controls */}
      <div className="flex flex-col gap-4 p-4">
        {/* Navigation Row */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="icon"
              onClick={navigatePrevious}
              disabled={isLoading}
              className="h-8 w-8"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              onClick={navigateToday}
              className="h-8 px-3 text-xs"
            >
              {t('common.today')}
            </Button>
            <Button
              variant="outline"
              size="icon"
              onClick={navigateNext}
              disabled={isLoading}
              className="h-8 w-8"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
            <div className="font-medium text-lg ml-4">
              {getViewTitle()}
            </div>
          </div>
        </div>
        
        <div className="flex flex-col lg:flex-row gap-4">
          {/* Search input */}
          <div className="flex flex-col">
            <label className="text-xs text-muted-foreground mb-1 font-medium">{t('common.search')}</label>
            <div className="relative">
              <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder={t('events.calendar.searchEvents')}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-8 w-40 sm:w-48"
              />
              {searchQuery && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
                  onClick={() => setSearchQuery('')}
                >
                  <X className="h-3 w-3" />
                </Button>
              )}
            </div>
          </div>

          {/* Filters Row */}
          <div className="flex flex-wrap gap-2 items-end">

          {/* Branch filter */}
          <div className="flex flex-col min-w-0">
            <label className="text-xs text-muted-foreground mb-1 font-medium">{t('events.branch')}</label>
            <Select value={selectedBranch} onValueChange={setSelectedBranch} open={branchDropdownOpen} onOpenChange={setBranchDropdownOpen}>
              <SelectTrigger className="w-32 sm:w-36">
                <SelectValue placeholder={t('common.all')} />
              </SelectTrigger>
            <SelectContent>
              <div className="px-2 pt-2 pb-1 sticky top-0 z-10 bg-background">
                <DropdownSearchInput
                  value={branchSearch}
                  onChange={setBranchSearch}
                  isOpen={branchDropdownOpen}
                  autoFocus
                  placeholder={t('common.search')}
                />
              </div>
              <SelectItem value="all">{t('common.all')}</SelectItem>
              {Array.isArray(filteredBranches) && filteredBranches.map(branch => (
                <SelectItem key={branch._id} value={branch._id}>
                  {branch.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          </div>

          {/* Partner filter */}
          <div className="flex flex-col min-w-0">
            <label className="text-xs text-muted-foreground mb-1 font-medium">{t('events.partner')}</label>
            <Select value={selectedPartner} onValueChange={setSelectedPartner} open={partnerDropdownOpen} onOpenChange={setPartnerDropdownOpen}>
              <SelectTrigger className="w-32 sm:w-36">
                <SelectValue placeholder={t('common.all')} />
              </SelectTrigger>
            <SelectContent>
              <div className="px-2 pt-2 pb-1 sticky top-0 z-10 bg-background">
                <DropdownSearchInput
                  value={partnerSearch}
                  onChange={setPartnerSearch}
                  isOpen={partnerDropdownOpen}
                  autoFocus
                  placeholder={t('common.search')}
                />
              </div>
              <SelectItem value="all">{t('common.all')}</SelectItem>
              {Array.isArray(filteredPartners) && filteredPartners.map(partner => (
                <SelectItem key={partner._id} value={partner._id}>
                  {partner.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          </div>

          {/* Event Type filter */}
          <div className="flex flex-col min-w-0">
            <label className="text-xs text-muted-foreground mb-1 font-medium">{t('events.eventType')}</label>
            <Select value={selectedEventType} onValueChange={setSelectedEventType} open={eventTypeDropdownOpen} onOpenChange={setEventTypeDropdownOpen}>
              <SelectTrigger className="w-32 sm:w-36">
                <SelectValue placeholder={t('common.all')} />
              </SelectTrigger>
            <SelectContent>
              <div className="px-2 pt-2 pb-1 sticky top-0 z-10 bg-background">
                <DropdownSearchInput
                  value={eventTypeSearch}
                  onChange={setEventTypeSearch}
                  isOpen={eventTypeDropdownOpen}
                  autoFocus
                  placeholder={t('common.search')}
                />
              </div>
              <SelectItem value="all">{t('common.all')}</SelectItem>
              {Array.isArray(filteredEventTypes) && filteredEventTypes.map(type => (
                <SelectItem key={type._id} value={type._id}>
                  {type.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          </div>

          {/* Supervisor filter */}
          <div className="flex flex-col min-w-0">
            <label className="text-xs text-muted-foreground mb-1 font-medium">{t('events.supervisors')}</label>
            <Select value={selectedSupervisor} onValueChange={setSelectedSupervisor} open={supervisorDropdownOpen} onOpenChange={setSupervisorDropdownOpen}>
              <SelectTrigger className="w-32 sm:w-36">
                <SelectValue placeholder={t('common.all')} />
              </SelectTrigger>
            <SelectContent>
              <div className="px-2 pt-2 pb-1 sticky top-0 z-10 bg-background">
                <DropdownSearchInput
                  value={supervisorSearch}
                  onChange={setSupervisorSearch}
                  isOpen={supervisorDropdownOpen}
                  autoFocus
                  placeholder={t('common.search')}
                />
              </div>
              <SelectItem value="all">{t('common.all')}</SelectItem>
              {Array.isArray(filteredSupervisors) && filteredSupervisors.map(supervisor => (
                <SelectItem key={supervisor._id} value={supervisor._id}>
                  {supervisor.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          </div>

          {/* Status filter */}
          <div className="flex flex-col min-w-0">
            <label className="text-xs text-muted-foreground mb-1 font-medium">{t('events.status.label')}</label>
            <Select value={selectedStatus} onValueChange={setSelectedStatus} open={statusDropdownOpen} onOpenChange={setStatusDropdownOpen}>
              <SelectTrigger className="w-32 sm:w-36">
                <SelectValue placeholder={t('common.all')} />
              </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t('common.all')}</SelectItem>
              <SelectItem value="new">{t('events.statuses.new')}</SelectItem>
              <SelectItem value="in_progress">{t('events.statuses.in_progress')}</SelectItem>
              <SelectItem value="cancelled">{t('events.statuses.cancelled')}</SelectItem>
              <SelectItem value="processing_report">{t('events.statuses.processing_report')}</SelectItem>
              <SelectItem value="awaiting_validation">{t('events.statuses.awaiting_validation')}</SelectItem>
              <SelectItem value="done">{t('events.statuses.done')}</SelectItem>
            </SelectContent>
          </Select>
          </div>

          {/* Report Status filter */}
          <div className="flex flex-col min-w-0">
            <label className="text-xs text-muted-foreground mb-1 font-medium">{t('events.reportStatus')}</label>
            <Select value={selectedReportStatus} onValueChange={setSelectedReportStatus} open={reportStatusDropdownOpen} onOpenChange={setReportStatusDropdownOpen}>
              <SelectTrigger className="w-32 sm:w-36">
                <SelectValue placeholder={t('common.all')} />
              </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t('common.all')}</SelectItem>
              <SelectItem value="pending">{t('events.reportStatuses.pending')}</SelectItem>
              <SelectItem value="processing">{t('events.reportStatuses.processing')}</SelectItem>
              <SelectItem value="submitted">{t('events.reportStatuses.submitted')}</SelectItem>
              <SelectItem value="validated">{t('events.reportStatuses.validated')}</SelectItem>
              <SelectItem value="overdue">{t('events.reportStatuses.overdue')}</SelectItem>
            </SelectContent>
          </Select>
          </div>

            {/* Action Buttons */}
            <div className="flex gap-2 ml-auto">
              <Button
                variant="outline"
                onClick={() => {
                  setSelectedBranch('all');
                  setSelectedPartner('all');
                  setSelectedEventType('all');
                  setSelectedSupervisor('all');
                  setSelectedStatus('all');
                  setSelectedReportStatus('all');
                  setSearchQuery('');
                  setBranchSearch('');
                  setPartnerSearch('');
                  setEventTypeSearch('');
                  setSupervisorSearch('');
                }}
                className="flex items-center gap-2 whitespace-nowrap"
                size="sm"
              >
                <Filter className="h-4 w-4" />
                {t('events.calendar.clearAll')}
              </Button>

              <Button variant="outline" onClick={fetchEvents} disabled={isLoading} size="sm">
                <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                {t('common.refresh')}
              </Button>
            </div>
          </div>
        </div>
      </div>
      
      <Card className="flex-1 grid grid-rows-[1fr] overflow-hidden">
        <CardContent className="p-0 overflow-auto">
          {isLoading ? (
            <CalendarSkeleton />
          ) : error ? (
            <Alert variant="destructive" className="m-4">
              <AlertDescription>
                {error}
              </AlertDescription>
            </Alert>
          ) : (
            renderCalendarDays()
          )}
        </CardContent>
        
        {/* Color legend */}
        <CardFooter className="pt-4 pb-2 px-4 border-t">
          <div className="flex flex-wrap gap-2">
            {Array.isArray(branches) && branches.map(branch => (
              <div key={branch._id} className="flex items-center gap-1">
                <div className={`w-3 h-3 rounded-full ${branchColorMap[branch._id] || 'bg-gray-200'}`}></div>
                <span className="text-xs">{branch.name}</span>
              </div>
            ))}
          </div>
        </CardFooter>
      </Card>
      
      {/* Debug component - only visible in development */}
      {process.env.NODE_ENV === 'development' && (
        <Card className="mt-4 bg-slate-50 dark:bg-slate-900 border-dashed">
          <CardContent className="p-4 text-xs">
            <h3 className="font-bold mb-2">{t('common.debugInfo')}</h3>
            <div className="grid grid-cols-2 gap-2">
              <div>
                <p><span className="font-semibold">{t('events.currentDate')}:</span> {format(currentDate, 'yyyy-MM-dd')}</p>
                <p><span className="font-semibold">{t('events.selectedBranch')}:</span> {selectedBranch === 'all' ? t('events.allBranches') : getBranchName(selectedBranch)}</p>
                <p><span className="font-semibold">{t('events.selectedPartner')}:</span> {selectedPartner === 'all' ? t('events.allPartners') : selectedPartner}</p>
              </div>
              <div>
                <p><span className="font-semibold">{t('events.totalEvents')}:</span> {events.length}</p>
                <p><span className="font-semibold">{t('events.branchesCount')}:</span> {branches.length}</p>
                <p><span className="font-semibold">{t('events.eventTypesCount')}:</span> {eventTypes.length}</p>
                <p><span className="font-semibold">{t('common.loading')}:</span> {isLoading ? t('common.yes') : t('common.no')}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
      
      {showDuplicateModal && duplicateEvent && (
        <EventForm
          open={showDuplicateModal}
          onClose={() => setShowDuplicateModal(false)}
          initialDate={new Date(duplicateEvent.startDate)}
          onEventSaved={() => {
            setShowDuplicateModal(false);
            fetchEvents();
          }}
          initialValues={{
            name: `${t('events.copyOf')} ${duplicateEvent.name}`,
            branchId: typeof duplicateEvent.branchId === 'object' ? duplicateEvent.branchId._id : duplicateEvent.branchId,
            partnerId: typeof duplicateEvent.partnerId === 'object' ? duplicateEvent.partnerId._id : duplicateEvent.partnerId,
            eventTypeId: typeof duplicateEvent.eventTypeId === 'object' ? duplicateEvent.eventTypeId._id : duplicateEvent.eventTypeId,
            startDate: new Date(duplicateEvent.startDate),
            startTime: format(new Date(duplicateEvent.startDate), 'HH:mm'),
            endTime: format(new Date(duplicateEvent.endDate), 'HH:mm'),
            location: duplicateEvent.location,
            notes: duplicateEvent.notes,
            clientGoal: duplicateEvent.clientGoal,
          }}
          autoFocusName
        />
      )}
    </div>
  );
}

function CalendarSkeleton() {
  return (
    <div className="grid grid-cols-7 grid-rows-[auto_repeat(5,1fr)] gap-px h-full overflow-hidden">
      {/* Day headers */}
      {Array(7).fill(0).map((_, i) => (
        <Skeleton key={`header-${i}`} className="h-10 w-full" />
      ))}
      
      {/* Calendar cells */}
      {Array(35).fill(0).map((_, i) => (
        <Skeleton key={`cell-${i}`} className="min-h-0 w-full h-full" />
      ))}
    </div>
  );
} 