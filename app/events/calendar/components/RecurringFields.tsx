import React, { useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { useLanguage } from '@/lib/contexts/language-context';
import DatePickerDialog from './DatePickerDialog';

export type RecurringRule = {
  enabled: boolean;
  frequency: 'day' | 'week' | 'month' | 'year';
  dayOfWeek?: number[]; // array of 0-6 (Sun-Sat)
  dayOfMonth?: number; // 1-31
  monthDay?: { month: number; day: number }; // for yearly
  endDate?: Date;
  mainDate?: Date;
};

const getWeekDays = (t: any) => [
  t('common.days.full.sunday'),
  t('common.days.full.monday'),
  t('common.days.full.tuesday'),
  t('common.days.full.wednesday'),
  t('common.days.full.thursday'),
  t('common.days.full.friday'),
  t('common.days.full.saturday')
];

interface RecurringFieldsProps {
  value: RecurringRule;
  onChange: (val: RecurringRule) => void;
  disabled?: boolean;
}

const RecurringFields: React.FC<RecurringFieldsProps> = ({ value, onChange, disabled }) => {
  const { t } = useLanguage();
  const weekDays = getWeekDays(t);
  // 1. Clear irrelevant fields when frequency changes
  useEffect(() => {
    if (!value.enabled) return;
    if (value.frequency === 'day') {
      if (value.dayOfWeek || value.dayOfMonth || value.monthDay) {
        onChange({ ...value, dayOfWeek: undefined, dayOfMonth: undefined, monthDay: undefined });
      }
    } else if (value.frequency === 'week') {
      if (!Array.isArray(value.dayOfWeek)) {
        onChange({ ...value, dayOfWeek: [] });
      }
      if (value.dayOfMonth || value.monthDay) {
        onChange({ ...value, dayOfMonth: undefined, monthDay: undefined });
      }
    } else if (value.frequency === 'month') {
      if (typeof value.dayOfMonth !== 'number') {
        onChange({ ...value, dayOfMonth: 1 });
      }
      if (value.dayOfWeek || value.monthDay) {
        onChange({ ...value, dayOfWeek: undefined, monthDay: undefined });
      }
    } else if (value.frequency === 'year') {
      if (!value.monthDay) {
        onChange({ ...value, monthDay: { month: 0, day: 1 } });
      }
      if (value.dayOfWeek || value.dayOfMonth) {
        onChange({ ...value, dayOfWeek: undefined, dayOfMonth: undefined });
      }
    }
  }, [value.frequency, value.enabled]);

  // 2. If endDate is not set, initialize to main date (passed via value.mainDate if available)
  useEffect(() => {
    if (value.enabled && !value.endDate && value.mainDate) {
      onChange({ ...value, endDate: value.mainDate });
    }
  }, [value.enabled, value.mainDate]);

  // 3. Validation
  const errors: string[] = [];
  if (value.enabled) {
    if (!value.frequency) errors.push('Frequency is required');
    if (!value.endDate) errors.push('End date is required');
    if (value.frequency === 'week' && (!Array.isArray(value.dayOfWeek) || value.dayOfWeek.length === 0)) errors.push('At least one day of week is required');
    if (value.frequency === 'month' && (typeof value.dayOfMonth !== 'number' || value.dayOfMonth < 1 || value.dayOfMonth > 31)) errors.push('Day of month is required (1-31)');
    if (value.frequency === 'year' && (!value.monthDay || typeof value.monthDay.month !== 'number' || typeof value.monthDay.day !== 'number')) errors.push('Month and day are required for yearly recurrence');
  }

  return (
    <div className="mt-4 p-4 rounded bg-muted/40 border">
      <div className="flex items-center gap-2 mb-2">
        <Checkbox
          checked={value.enabled}
          onCheckedChange={checked => onChange({ ...value, enabled: !!checked })}
          disabled={disabled}
        />
        <span className="font-medium">{t('events.form.recurrent')}</span>
      </div>
      {value.enabled && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
          <div>
            <label className="block mb-1 text-sm font-medium">{t('events.form.repeatEvery')}</label>
            <Select
              value={value.frequency}
              onValueChange={freq => onChange({ ...value, frequency: freq as any })}
              disabled={disabled}
            >
              <SelectTrigger><SelectValue placeholder={t('events.form.frequency')} /></SelectTrigger>
              <SelectContent>
                <SelectItem value="day">{t('events.form.frequencies.day')}</SelectItem>
                <SelectItem value="week">{t('events.form.frequencies.week')}</SelectItem>
                <SelectItem value="month">{t('events.form.frequencies.month')}</SelectItem>
                <SelectItem value="year">{t('events.form.frequencies.year')}</SelectItem>
              </SelectContent>
            </Select>
          </div>
          {value.frequency === 'week' && (
            <div>
              <label className="block mb-1 text-sm font-medium">{t('events.form.daysOfWeek')}</label>
              <div className="flex flex-wrap gap-2">
                {weekDays.map((d, i) => (
                  <label key={i} className="flex items-center gap-1 text-xs">
                    <Checkbox
                      checked={Array.isArray(value.dayOfWeek) && value.dayOfWeek.includes(i)}
                      onCheckedChange={checked => {
                        let newDays = Array.isArray(value.dayOfWeek) ? [...value.dayOfWeek] : [];
                        if (checked) {
                          if (!newDays.includes(i)) newDays.push(i);
                        } else {
                          newDays = newDays.filter(day => day !== i);
                        }
                        onChange({ ...value, dayOfWeek: newDays });
                      }}
                      disabled={disabled}
                    />
                    {d}
                  </label>
                ))}
              </div>
            </div>
          )}
          {value.frequency === 'month' && (
            <div>
              <label className="block mb-1 text-sm font-medium">{t('events.form.dayOfMonth')}</label>
              <Input
                type="number"
                min={1}
                max={31}
                value={value.dayOfMonth ?? ''}
                onChange={e => onChange({ ...value, dayOfMonth: Number(e.target.value) })}
                disabled={disabled}
                className="w-24"
              />
            </div>
          )}
          {value.frequency === 'year' && (
            <div>
              <label className="block mb-1 text-sm font-medium">{t('events.date')}</label>
              <DatePickerDialog
                value={value.monthDay ? new Date(2000, value.monthDay.month, value.monthDay.day) : null}
                onChange={date => {
                  if (date) onChange({ ...value, monthDay: { month: date.getMonth(), day: date.getDate() } });
                }}
                disabled={disabled}
                label="Pick date"
                renderButtonLabel={(date: Date | null) =>
                  date ? date.toLocaleString(undefined, { month: 'long', day: 'numeric' }) : 'Pick date'
                }
              />
            </div>
          )}
          <div>
            <label className="block mb-1 text-sm font-medium">{t('events.form.endDateInclusive')}</label>
            <DatePickerDialog
              value={value.endDate ?? undefined}
              onChange={date => onChange({ ...value, endDate: date ?? undefined })}
              disabled={disabled}
              label={t('events.form.endDateInclusive')}
            />
          </div>
        </div>
      )}
      {errors.length > 0 && (
        <div className="text-xs text-destructive mb-2">
          {errors.map((err, i) => <div key={i}>{err}</div>)}
        </div>
      )}
      {process.env.NODE_ENV === 'development' && (
        <div className="mt-2 text-xs text-blue-700 border border-dashed border-blue-300 rounded p-2">
          <strong>Debug:</strong> {JSON.stringify(value)}
        </div>
      )}
    </div>
  );
};

export default RecurringFields; 