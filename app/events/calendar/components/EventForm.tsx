'use client';

import { useState, useEffect, useRef, useMemo } from 'react';
import { useLanguage } from '@/lib/contexts/language-context';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import { CalendarIcon, Loader2, Alert<PERSON>riangle, CheckCircle2, <PERSON>, Phone } from 'lucide-react';
import { toast } from 'sonner';
import { Branch } from '@/types/branch';
import { Partner } from '@/types/partner';
import { EventType } from '@/types/event';
import { Progress } from '@/components/ui/progress';
import { canUserEditEvents } from '@/lib/utils/permissions-utils';
import { useAppSelector } from '@/lib/redux/hooks';
import DatePickerDialog from './DatePickerDialog';
import { User } from '@/types/user';
import UserSelect from '../../components/form/UserSelect';
import { normalizePhoneNumber } from '../../components/form/phone-utils';
import PhoneField from '../../components/form/PhoneField';
import RecurringFields, { RecurringRule } from './RecurringFields';
import { SimplePersonnelManager } from '../../components/SimplePersonnelManager';
import { createLocalDateTime, convertTo24HourFormat } from '@/lib/utils/date-utils';
import React from 'react';

interface EventFormProps {
  open: boolean;
  onClose: () => void;
  eventId?: string;
  initialDate?: Date | null;
  onEventSaved?: () => void;
  initialValues?: Partial<EventFormValues>;
  autoFocusName?: boolean;
}

interface Personnel {
  supervisors: Array<{
    userId: string;
    name: string;
    email: string;
  }>;
  paps: Array<{
    userId: string;
    name: string;
    email: string;
  }>;
  cooks: Array<{
    userId: string;
    name: string;
    email: string;
  }>;
}

// Form schema using Zod
const eventFormSchema = z.object({
  name: z.string().min(2, { message: 'Event name must be at least 2 characters' }),
  branchId: z.string({ required_error: 'Please select a branch' }),
  partnerId: z.string({ required_error: 'Please select a partner' }),
  eventTypeId: z.string({ required_error: 'Please select an event type' }),
  startDate: z.date({ required_error: 'Please select a date' }),
  startTime: z.string().refine((val) => {
    // Allow both 12-hour format (e.g., "10:22 AM") and 24-hour format (e.g., "10:22")
    const time24h = /^([01]\d|2[0-3]):([0-5]\d)$/.test(val);
    const time12h = /^(1[0-2]|0?[1-9]):([0-5]\d)\s*(AM|PM)$/i.test(val);
    return time24h || time12h;
  }, { message: 'Please enter a valid time (HH:MM or HH:MM AM/PM)' }),
  endTime: z.string().refine((val) => {
    // Allow both 12-hour format (e.g., "10:22 AM") and 24-hour format (e.g., "10:22")
    const time24h = /^([01]\d|2[0-3]):([0-5]\d)$/.test(val);
    const time12h = /^(1[0-2]|0?[1-9]):([0-5]\d)\s*(AM|PM)$/i.test(val);
    return time24h || time12h;
  }, { message: 'Please enter a valid time (HH:MM or HH:MM AM/PM)' }),
  location: z.string().min(1, { message: 'Location is required' }),
  clientGoal: z.coerce.number().int().nonnegative().optional(),
  notes: z.string().optional(),
  resId: z.string().optional(),
  contactInfo: z.object({
    name: z.string().optional(),
    role: z.string().optional(),
    phone: z.string().optional().refine(
      (val) => !val || /^\d{10}$/.test(val),
      { message: 'Phone number must be exactly 10 digits' }
    ),
  }).optional(),
  // Remove agents field as we'll use personnel instead
  recurringRule: z.object({
    enabled: z.boolean(),
    frequency: z.enum(['day', 'week', 'month', 'year']).optional(),
    dayOfWeek: z.array(z.number()).optional(),
    dayOfMonth: z.number().optional(),
    monthDay: z.object({ month: z.number(), day: z.number() }).optional(),
    endDate: z.date().optional().refine(val => val !== null, { message: 'End date cannot be null' }),
    mainDate: z.date().optional(),
  }).refine((val) => {
    if (!val.enabled) return true;
    if (!val.frequency) return false;
    if (!val.endDate) return false;
    if (val.frequency === 'week' && (!Array.isArray(val.dayOfWeek) || val.dayOfWeek.length === 0)) return false;
    if (val.frequency === 'month' && (typeof val.dayOfMonth !== 'number' || val.dayOfMonth < 1 || val.dayOfMonth > 31)) return false;
    if (val.frequency === 'year' && (!val.monthDay || typeof val.monthDay.month !== 'number' || typeof val.monthDay.day !== 'number')) return false;
    return true;
  }, { message: 'All recurrence fields are required when recurrence is enabled.' }),
}).refine((data) => {
  // Validate that end time is after start time
  // Convert both times to 24-hour format first
  const startTime24h = convertTo24HourFormat(data.startTime);
  const endTime24h = convertTo24HourFormat(data.endTime);

  const [startHours, startMinutes] = startTime24h.split(':').map(Number);
  const [endHours, endMinutes] = endTime24h.split(':').map(Number);

  const startTotalMinutes = startHours * 60 + startMinutes;
  const endTotalMinutes = endHours * 60 + endMinutes;

  return endTotalMinutes > startTotalMinutes;
}, {
  message: 'End time must be after start time',
  path: ['endTime'], // This will show the error on the endTime field
});

type EventFormValues = z.infer<typeof eventFormSchema>;

interface DropdownSearchInputProps {
  value: string;
  onChange: (v: string) => void;
  isOpen: boolean;
  autoFocus?: boolean;
  placeholder?: string;
}

const DropdownSearchInput: React.FC<DropdownSearchInputProps> = ({ value, onChange, isOpen, autoFocus, placeholder }) => {
  const { t } = useLanguage();
  const inputRef = useRef<HTMLInputElement>(null);
  useEffect(() => {
    if (isOpen && autoFocus && inputRef.current) {
      inputRef.current.focus();
      if (process.env.NODE_ENV === 'development') {
        // eslint-disable-next-line no-console
        console.log('DropdownSearchInput focused');
      }
    }
  }, [isOpen, autoFocus]);
  return (
    <input
      ref={inputRef}
      value={value}
      onChange={e => onChange(e.target.value)}
      placeholder={placeholder || t('common.search')}
      className="h-8 px-2 text-sm border-muted focus:border-primary rounded shadow-none w-full"
      autoFocus={false}
      type="text"
    />
  );
};

export default function EventForm({ open, onClose, eventId, initialDate, onEventSaved, initialValues, autoFocusName }: EventFormProps) {
  const { t } = useLanguage();
  const permissions = useAppSelector(state => state.permissions);
  const [loading, setLoading] = useState(false);
  const [branches, setBranches] = useState<Branch[]>([]);
  const [partners, setPartners] = useState<Partner[]>([]);
  const [eventTypes, setEventTypes] = useState<EventType[]>([]);
  const [formError, setFormError] = useState<string | null>(null);
  const [fetchingEvent, setFetchingEvent] = useState(false);
  const [formStatus, setFormStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const [users, setUsers] = useState<User[]>([]);
  const [allDataLoaded, setAllDataLoaded] = useState(false);

  // Personnel management state
  const [personnel, setPersonnel] = useState<Personnel>({
    supervisors: [],
    paps: [],
    cooks: []
  });
  const [availableSupervisors, setAvailableSupervisors] = useState<User[]>([]);
  const [availableCooks, setAvailableCooks] = useState<User[]>([]);

  const nameInputRef = useRef<HTMLInputElement>(null);

  const [branchSearch, setBranchSearch] = useState('');
  const [partnerSearch, setPartnerSearch] = useState('');
  const [eventTypeSearch, setEventTypeSearch] = useState('');
  const [eventTypeDropdownOpen, setEventTypeDropdownOpen] = useState(false);
  const [branchDropdownOpen, setBranchDropdownOpen] = useState(false);
  const [partnerDropdownOpen, setPartnerDropdownOpen] = useState(false);

  const filteredBranches = useMemo(() => {
    if (!branchSearch) return branches;
    return branches.filter(b => b.name.toLowerCase().includes(branchSearch.toLowerCase()));
  }, [branches, branchSearch]);
  const filteredPartners = useMemo(() => {
    if (!partnerSearch) return partners;
    return partners.filter(p => p.name.toLowerCase().includes(partnerSearch.toLowerCase()));
  }, [partners, partnerSearch]);
  const filteredEventTypes = useMemo(() => {
    if (!eventTypeSearch) return eventTypes;
    return eventTypes.filter(e => e.name.toLowerCase().includes(eventTypeSearch.toLowerCase()));
  }, [eventTypes, eventTypeSearch]);
  
  // Initialize form with default values
  const form = useForm<EventFormValues>({
    resolver: zodResolver(eventFormSchema),
    defaultValues: {
      name: '',
      branchId: '',
      partnerId: '',
      eventTypeId: '',
      startDate: initialDate || new Date(),
      startTime: '09:00',
      endTime: '17:00',
      location: '',
      clientGoal: 0,
      notes: '',
      resId: '',
      contactInfo: {
        name: '',
        role: '',
        phone: '',
      },
      recurringRule: {
        enabled: false,
        frequency: 'day',
        endDate: undefined,
        mainDate: initialDate || new Date(),
      } as RecurringRule,
      ...initialValues,
    },
    mode: 'onChange', // Enable validation on change
  });
  
  // Autofocus and select name input if requested
  useEffect(() => {
    if (open && autoFocusName && nameInputRef.current) {
      nameInputRef.current.focus();
      nameInputRef.current.select();
    }
  }, [open, autoFocusName]);
  
  // When initialValues change, reset the form
  useEffect(() => {
    if (initialValues) {
      form.reset({
        ...form.getValues(),
        ...initialValues,
      });
    }
  }, [initialValues]);
  
  // Function to fetch users by role using the new events/users route
  const fetchUsersByRole = async (role: string): Promise<User[]> => {
    const response = await fetch(`/api/events/users?role=${role}`);

    if (!response.ok) {
      throw new Error(`Failed to fetch ${role} users: ${response.statusText}`);
    }

    const data = await response.json();
    return data.users || [];
  };

  // Fetch options for selects
  useEffect(() => {
    let isMounted = true;
    setLoading(true);
    setAllDataLoaded(false);
    const fetchFormData = async () => {
      try {
        const [branchesRes, partnersRes, eventTypesRes, supervisors, cooks, allUsers] = await Promise.all([
          fetch('/api/events/branches'),
          fetch('/api/events/partners'),
          fetch('/api/events/types'),
          fetchUsersByRole('supervisor'),
          fetchUsersByRole('cook'),
          fetchUsersByRole('pap'), // For general user selection
        ]);
        if (!branchesRes.ok || !partnersRes.ok || !eventTypesRes.ok) throw new Error('Failed to fetch form data');
        const [branchesData, partnersData, eventTypesData] = await Promise.all([
          branchesRes.json(),
          partnersRes.json(),
          eventTypesRes.json(),
        ]);
        if (!isMounted) return;
        setBranches(Array.isArray(branchesData) ? branchesData : branchesData.data || []);
        setPartners(Array.isArray(partnersData) ? partnersData : partnersData.data || []);
        setEventTypes(Array.isArray(eventTypesData) ? eventTypesData : eventTypesData.data || []);
        setUsers(allUsers);

        setAvailableSupervisors(supervisors);
        setAvailableCooks(cooks);
        if (eventId) {
          setFetchingEvent(true);
          try {
            const response = await fetch(`/api/events/${eventId}`);
            if (!response.ok) throw new Error('Failed to fetch event');
            const eventData = await response.json();
            console.log('Fetched event data:', eventData);

            // Parse date and times
            const startDate = new Date(eventData.startDate);
            const endDate = new Date(eventData.endDate);
            const startTime = format(startDate, 'HH:mm');
            const endTime = format(endDate, 'HH:mm');

            // Extract IDs from potentially nested objects
            const branchId = typeof eventData.branchId === 'object' ? eventData.branchId._id : eventData.branchId;
            const partnerId = typeof eventData.partnerId === 'object' ? eventData.partnerId._id : eventData.partnerId;
            const eventTypeId = typeof eventData.eventTypeId === 'object' ? eventData.eventTypeId._id : eventData.eventTypeId;

            // Set form values
            form.reset({
              name: eventData.name,
              branchId,
              partnerId,
              eventTypeId,
              startDate,
              startTime,
              endTime,
              location: eventData.location,
              clientGoal: eventData.clientGoal || 0,
              notes: eventData.notes || '',
              resId: eventData.resId || '',
              contactInfo: eventData.contactInfo || {
                name: '',
                role: '',
                phone: '',
              },
              recurringRule: eventData.recurringRule || {
                enabled: false,
                frequency: 'day',
                endDate: undefined,
                mainDate: startDate,
              } as RecurringRule,
            });

            // Set personnel data
            const supervisors = (eventData.supervisors || []).map((s: any) => ({
              userId: typeof s === 'object' ? s._id : s,
              name: typeof s === 'object' ? s.name : '',
              email: typeof s === 'object' ? s.email : '',
            }));

            const cooks = (eventData.cooks || []).map((c: any) => ({
              userId: typeof c === 'object' ? c._id : c,
              name: typeof c === 'object' ? c.name : '',
              email: typeof c === 'object' ? c.email : '',
            }));

            const paps = (eventData.agents?.assignedAgents || []).map((a: any) => ({
              userId: typeof a === 'object' ? a._id : a,
              name: typeof a === 'object' ? a.name : '',
              email: typeof a === 'object' ? a.email : '',
            }));

            setPersonnel({
              supervisors,
              paps,
              cooks
            });
          } catch (error) {
            console.error('Error fetching event:', error);
            toast.error('Failed to load event data');
            setFormError('Failed to load event data. Please try again.');
          } finally {
            setFetchingEvent(false);
          }
        }
        setAllDataLoaded(true);
      } catch (error) {
        console.error('Error fetching form data:', error);
        toast.error('Failed to load form data');
      } finally {
        setLoading(false);
      }
    };
    fetchFormData();
    return () => { isMounted = false; };
  }, [eventId, form]);
  
  // Handle personnel change
  const handlePersonnelChange = (newPersonnel: Personnel) => {
    setPersonnel(newPersonnel);
  };

  // Handle form submission
  const onSubmit = async (values: EventFormValues) => {
    setLoading(true);
    setFormStatus('loading');
    setFormError(null);
    try {
      // Create UTC dates from date and time values
      // Convert time formats to 24-hour format first, then create UTC dates
      const startTime24h = convertTo24HourFormat(values.startTime);
      const endTime24h = convertTo24HourFormat(values.endTime);

      const startDate = createLocalDateTime(values.startDate, startTime24h);
      const endDate = createLocalDateTime(values.startDate, endTime24h);

      // Create payload for API - matching /events/new format
      const payload = {
        name: values.name,
        branchId: values.branchId,
        partnerId: values.partnerId,
        eventTypeId: values.eventTypeId,
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        location: values.location,
        clientGoal: values.clientGoal || 0,
        notes: values.notes || '',
        supervisors: personnel.supervisors.map(s => s.userId),
        cooks: personnel.cooks.map(c => c.userId),
        agents: {
          assignedAgents: personnel.paps.map(p => p.userId)
        },
        contactInfo: values.contactInfo ? {
          ...values.contactInfo,
          phone: values.contactInfo.phone ? normalizePhoneNumber(values.contactInfo.phone) : undefined,
        } : undefined,
        resId: values.resId || undefined,
        recurringRule: values.recurringRule || undefined,
      };
      
      // If recurrence is enabled, use the recurring route
      if (values.recurringRule && values.recurringRule.enabled) {
        const response = await fetch('/api/events/recurring', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ eventTemplate: payload, recurringRule: values.recurringRule }),
        });
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to create recurring events');
        }
        const data = await response.json();
        setFormStatus('success');
        toast.success(`Created ${data.created} events`);
        setTimeout(() => {
          if (onEventSaved) onEventSaved();
          else onClose();
        }, 1000);
        return;
      }
      
      // Otherwise, normal create/update
      const url = eventId ? `/api/events/${eventId}` : '/api/events';
      const method = eventId ? 'PUT' : 'POST';
      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save event');
      }
      
      setFormStatus('success');
      toast.success(eventId ? 'Event updated successfully' : 'Event created successfully');
      
      // Give time for the success state to be visible before closing
      setTimeout(() => {
        if (onEventSaved) onEventSaved();
        else onClose();
      }, 1000);
    } catch (error) {
      console.error('Error saving event:', error);
      setFormStatus('error');
      setFormError(error instanceof Error ? error.message : 'An error occurred');
      toast.error(error instanceof Error ? error.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };
  
  // Calculate form completion percentage
  const calculateFormProgress = (): number => {
    const formValues = form.getValues();
    const totalFields = 8; // Required fields: name, branchId, partnerId, eventTypeId, startDate, location, times, supervisors
    let completedFields = 0;

    if (formValues.name) completedFields++;
    if (formValues.branchId) completedFields++;
    if (formValues.partnerId) completedFields++;
    if (formValues.eventTypeId) completedFields++;
    if (formValues.startDate) completedFields++;
    if (formValues.location) completedFields++;
    if (formValues.startTime && formValues.endTime) completedFields++;
    if (personnel.supervisors.length > 0) completedFields++;

    return Math.round((completedFields / totalFields) * 100);
  };
  
  // Permission check for editing events
  const canEditEvents = canUserEditEvents(permissions as any);
  
  return (
    <Dialog open={open} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-w-2xl w-full max-h-[90vh] p-0 flex flex-col">
        <DialogHeader className="px-6 pt-6 pb-2">
          <div className="flex items-center gap-3 mb-2">
            <span className="inline-flex items-center justify-center rounded-full bg-primary/10 text-primary p-2">
              <CalendarIcon className="h-7 w-7" />
            </span>
            <DialogTitle asChild>
              <h2 className="text-2xl font-extrabold tracking-tight leading-tight text-primary">
                {eventId ? t('events.editEvent') : t('events.createEvent')}
              </h2>
            </DialogTitle>
          </div>
          <DialogDescription className="text-base text-muted-foreground">
            {t('events.fillEventDetails')}
          </DialogDescription>
          {/* Progress indicator */}
          <div className="mt-4">
            <div className="flex items-center justify-between text-sm text-muted-foreground mb-2">
              <span>{t('events.form.formCompletion')}</span>
              <span>{calculateFormProgress()}%</span>
            </div>
            <Progress value={calculateFormProgress()} className="h-2" />
          </div>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="flex-1 flex flex-col overflow-hidden">
            <div className="flex-1 overflow-y-auto px-6 pt-6 pb-2 space-y-8">
              {/* General Info Section */}
              <div className="rounded-lg bg-muted/40 p-4 mb-6 shadow-sm">
                <h3 className="text-lg font-semibold mb-4 border-l-4 border-primary pl-2 bg-primary/5 text-primary rounded-t">
                  {t('events.generalInfo')}
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField control={form.control} name="name" render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('events.name')}</FormLabel>
                      <FormControl><Input {...field} ref={nameInputRef} disabled={loading || !allDataLoaded || fetchingEvent} /></FormControl>
                      <FormMessage />
                    </FormItem>
                  )} />
                  <FormField control={form.control} name="eventTypeId" render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('events.eventType')}</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value} disabled={loading || !allDataLoaded || fetchingEvent} open={eventTypeDropdownOpen} onOpenChange={setEventTypeDropdownOpen}>
                        <FormControl>
                          <SelectTrigger className={!field.value ? "text-muted-foreground" : ""}>
                            <SelectValue placeholder={t('events.selectEventType')} />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <div className="px-2 pt-2 pb-1 sticky top-0 z-10 bg-background">
                            <DropdownSearchInput
                              value={eventTypeSearch}
                              onChange={setEventTypeSearch}
                              isOpen={eventTypeDropdownOpen}
                              autoFocus
                              placeholder={t('common.search')}
                            />
                          </div>
                          {filteredEventTypes.map((eventType) => (
                            <SelectItem key={eventType._id} value={eventType._id}>{eventType.name}</SelectItem>
                          ))}
                          {filteredEventTypes.length === 0 && <div className="text-xs text-muted-foreground px-2 py-1">{t('events.form.noEventTypesFound')}</div>}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )} />
                  <FormField control={form.control} name="branchId" render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('events.branch')}</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value} disabled={loading || !allDataLoaded || fetchingEvent} open={branchDropdownOpen} onOpenChange={setBranchDropdownOpen}>
                        <FormControl>
                          <SelectTrigger className={!field.value ? "text-muted-foreground" : ""}>
                            <SelectValue placeholder={t('events.selectBranch')} />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <div className="px-2 pt-2 pb-1 sticky top-0 z-10 bg-background">
                            <DropdownSearchInput
                              value={branchSearch}
                              onChange={setBranchSearch}
                              isOpen={branchDropdownOpen}
                              autoFocus
                              placeholder={t('common.search')}
                            />
                          </div>
                          {filteredBranches.map((branch) => (
                            <SelectItem key={branch._id} value={branch._id}>{branch.name}</SelectItem>
                          ))}
                          {filteredBranches.length === 0 && <div className="text-xs text-muted-foreground px-2 py-1">{t('events.form.noBranchesFound')}</div>}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )} />
                  <FormField control={form.control} name="partnerId" render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('events.partner')}</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value} disabled={loading || !allDataLoaded || fetchingEvent} open={partnerDropdownOpen} onOpenChange={setPartnerDropdownOpen}>
                        <FormControl>
                          <SelectTrigger className={!field.value ? "text-muted-foreground" : ""}>
                            <SelectValue placeholder={t('events.selectPartner')} />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <div className="px-2 pt-2 pb-1 sticky top-0 z-10 bg-background">
                            <DropdownSearchInput
                              value={partnerSearch}
                              onChange={setPartnerSearch}
                              isOpen={partnerDropdownOpen}
                              autoFocus
                              placeholder={t('common.search')}
                            />
                          </div>
                          {filteredPartners.map((partner) => (
                            <SelectItem key={partner._id} value={partner._id}>{partner.name}</SelectItem>
                          ))}
                          {filteredPartners.length === 0 && <div className="text-xs text-muted-foreground px-2 py-1">{t('events.form.noPartnersFound')}</div>}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )} />
                </div>
                <div className="flex flex-col md:flex-row gap-4 mt-4 items-end">
                  <div className="flex-1 min-w-[180px]">
                    <FormField control={form.control} name="startDate" render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel>{t('events.date')}</FormLabel>
                        <FormControl>
                          <DatePickerDialog value={field.value} onChange={field.onChange} disabled={loading || !allDataLoaded || fetchingEvent} label={t('events.date')} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )} />
                  </div>
                  <div className="flex-1 min-w-[120px]">
                    <FormField control={form.control} name="startTime" render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('events.startTime')}</FormLabel>
                        <FormControl><Input {...field} placeholder={t('events.form.timePlaceholder')} className="h-10" disabled={loading || !allDataLoaded || fetchingEvent} /></FormControl>
                        <FormMessage />
                      </FormItem>
                    )} />
                  </div>
                  <div className="flex-1 min-w-[120px]">
                    <FormField control={form.control} name="endTime" render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('events.endTime')}</FormLabel>
                        <FormControl><Input {...field} placeholder={t('events.form.timePlaceholder')} className="h-10" disabled={loading || !allDataLoaded || fetchingEvent} /></FormControl>
                        <FormMessage />
                      </FormItem>
                    )} />
                  </div>
                </div>
                <RecurringFields
                  value={{
                    ...form.watch('recurringRule'),
                    frequency: form.watch('recurringRule').frequency ?? 'day',
                    mainDate: form.watch('startDate'),
                  }}
                  onChange={val => form.setValue('recurringRule', { ...val, mainDate: form.watch('startDate') })}
                  disabled={loading || !allDataLoaded || fetchingEvent}
                />
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                  <FormField control={form.control} name="location" render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('events.location')}</FormLabel>
                      <FormControl><Input {...field} disabled={loading || !allDataLoaded || fetchingEvent} /></FormControl>
                      <FormMessage />
                    </FormItem>
                  )} />
                  <FormField control={form.control} name="clientGoal" render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('events.clientGoal')}</FormLabel>
                      <FormControl><Input type="number" min="0" {...field} disabled={loading || !allDataLoaded || fetchingEvent} /></FormControl>
                      <FormDescription>{t('events.clientGoalDescription')}</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )} />
                </div>
                <FormField control={form.control} name="notes" render={({ field }) => (
                  <FormItem className="mt-4">
                    <FormLabel>{t('events.notes')}</FormLabel>
                    <FormControl><Textarea {...field} disabled={loading || !allDataLoaded || fetchingEvent} /></FormControl>
                    <FormMessage />
                  </FormItem>
                )} />
              </div>

              {/* Contact Info Section */}
              <div className="rounded-lg bg-muted/40 p-4 mb-6 shadow-sm">
                <h3 className="text-lg font-semibold mb-4 border-l-4 border-primary pl-2 bg-primary/5 text-primary rounded-t">
                  {t('events.contactInfo')}
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <FormField control={form.control} name="contactInfo.name" render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('events.contactName')}</FormLabel>
                      <FormControl><Input {...field} disabled={loading || !allDataLoaded || fetchingEvent} /></FormControl>
                      <FormMessage />
                    </FormItem>
                  )} />
                  <FormField control={form.control} name="contactInfo.role" render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('events.contactRole')}</FormLabel>
                      <FormControl><Input {...field} disabled={loading || !allDataLoaded || fetchingEvent} /></FormControl>
                      <FormMessage />
                    </FormItem>
                  )} />
                  <FormField control={form.control} name="contactInfo.phone" render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('events.contactPhone')}</FormLabel>
                      <FormControl>
                        <PhoneField
                          value={field.value || ''}
                          onChange={field.onChange}
                          error={form.formState.errors?.contactInfo?.phone?.message}
                          label={undefined}
                          placeholder={t('events.contactPhonePlaceholder')}
                          disabled={loading || !allDataLoaded || fetchingEvent}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )} />
                </div>
              </div>

              {/* Personnel Assignment Section */}
              <div className="rounded-lg bg-muted/40 p-4 mb-6 shadow-sm">
                <h3 className="text-lg font-semibold mb-4 border-l-4 border-primary pl-2 bg-primary/5 text-primary rounded-t flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  {t('events.form.personnelAssignment')}
                </h3>
                <SimplePersonnelManager
                  initialPersonnel={personnel}
                  onPersonnelChange={handlePersonnelChange}
                  availableSupervisors={availableSupervisors}
                  availableCooks={availableCooks}
                  readOnly={loading || !allDataLoaded || fetchingEvent}
                  maxSupervisors={1}
                />
                {personnel.supervisors.length === 0 && (
                  <p className="text-sm text-red-500 mt-2">{t('events.form.supervisorRequired')}</p>
                )}
              </div>

              {/* Contact Information Section */}
              <div className="rounded-lg bg-muted/40 p-4 mb-6 shadow-sm">
                <h3 className="text-lg font-semibold mb-4 border-l-4 border-primary pl-2 bg-primary/5 text-primary rounded-t flex items-center gap-2">
                  <Phone className="h-5 w-5" />
                  {t('events.contactInfo')}
                </h3>
                <div className="space-y-4">
                  <FormField control={form.control} name="resId" render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('events.responsibleUser')}</FormLabel>
                      <UserSelect users={users} value={field.value} onValueChange={field.onChange} disabled={loading || !allDataLoaded || fetchingEvent} />
                      <FormMessage />
                    </FormItem>
                  )} />

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <FormField control={form.control} name="contactInfo.name" render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('events.contactName')}</FormLabel>
                        <FormControl><Input {...field} placeholder={t('events.form.enterContactName')} disabled={loading || !allDataLoaded || fetchingEvent} /></FormControl>
                        <FormMessage />
                      </FormItem>
                    )} />
                    <FormField control={form.control} name="contactInfo.role" render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('events.contactRole')}</FormLabel>
                        <FormControl><Input {...field} placeholder={t('events.form.enterContactRole')} disabled={loading || !allDataLoaded || fetchingEvent} /></FormControl>
                        <FormMessage />
                      </FormItem>
                    )} />
                    <FormField control={form.control} name="contactInfo.phone" render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('events.contactPhone')}</FormLabel>
                        <FormControl>
                          <PhoneField
                            value={field.value || ''}
                            onChange={field.onChange}
                            placeholder={t('events.contactPhonePlaceholder')}
                            disabled={loading || !allDataLoaded || fetchingEvent}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )} />
                  </div>
                </div>
              </div>
            </div>

            {/* Sticky Action Bar */}
            <div className="sticky bottom-0 left-0 w-full bg-primary/5 border-t border-primary/20 px-6 py-4 z-10">
              <div className="flex items-center justify-between w-full">
                <div className="flex items-center gap-2">
                  {formStatus === 'success' && (
                    <div className="flex items-center gap-2 text-green-600">
                      <CheckCircle2 className="h-4 w-4" />
                      <span className="text-sm font-medium">{t('events.form.savedSuccessfully')}</span>
                    </div>
                  )}
                  {formStatus === 'error' && formError && (
                    <div className="flex items-center gap-2 text-red-600">
                      <AlertTriangle className="h-4 w-4" />
                      <span className="text-sm font-medium">{formError}</span>
                    </div>
                  )}
                  {personnel.supervisors.length === 0 && (
                    <div className="flex items-center gap-2 text-amber-600">
                      <AlertTriangle className="h-4 w-4" />
                      <span className="text-sm font-medium">{t('events.form.supervisorRequired')}</span>
                    </div>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  <Button type="button" variant="outline" onClick={onClose} disabled={loading}>
                    {t('common.cancel')}
                  </Button>
                  {canEditEvents && (
                    <Button
                      type="submit"
                      disabled={loading || !form.formState.isValid || personnel.supervisors.length === 0}
                      className={cn("relative", formStatus === 'success' && "bg-green-600 hover:bg-green-700", formStatus === 'error' && "bg-red-600 hover:bg-red-700")}
                    >
                      {loading && (<Loader2 className="mr-2 h-4 w-4 animate-spin" />)}
                      {formStatus === 'success' ? t('common.saved') : eventId ? t('common.update') : t('common.create')}
                    </Button>
                  )}
                  {!canEditEvents && process.env.NODE_ENV === 'development' && (
                    <span className="text-xs text-destructive ml-2">[No EDIT_EVENTS permission]</span>
                  )}
                </div>
              </div>
            </div>

            {/* Debug Info (Collapsible, Dev Only) */}
            {process.env.NODE_ENV === "development" && (
              <details className="px-6 pb-4 pt-2 mt-2 border-t border-dashed border-blue-200 text-xs text-blue-700 bg-blue-50 rounded">
                <summary className="cursor-pointer font-semibold">{t('common.debugInfo')}</summary>
                {form.formState.errors && Object.keys(form.formState.errors).length > 0 && (
                  <div className="mb-2">
                    <span className="font-semibold text-destructive">{t('events.form.validationErrors')}:</span>
                    <pre className="overflow-auto max-h-24 text-destructive">{JSON.stringify(form.formState.errors, null, 2)}</pre>
                  </div>
                )}
                {eventId && (
                  <div>
                    <span className="font-semibold">{t('events.form.formValues')}:</span>
                    <pre className="overflow-auto max-h-32">{JSON.stringify(form.getValues(), null, 2)}</pre>
                  </div>
                )}
              </details>
            )}
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
} 