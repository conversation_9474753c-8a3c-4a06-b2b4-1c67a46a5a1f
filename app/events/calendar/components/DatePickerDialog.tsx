import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { CalendarIcon, Loader2 } from 'lucide-react';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { cn } from '@/lib/utils';
import { useLanguage } from '@/lib/contexts/language-context';

interface DatePickerDialogProps {
  value?: Date | null;
  onChange: (date: Date | null | undefined) => void;
  disabled?: boolean;
  label?: string;
  renderButtonLabel?: (date: Date | null) => React.ReactNode;
}

const DatePickerDialog: React.FC<DatePickerDialogProps> = ({ value, onChange, disabled, label, renderButtonLabel }) => {
  const { t, language } = useLanguage();
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);

  // For demo: loading state is not used, but can be set if async logic is needed
  const locale = language === 'fr' ? fr : undefined;

  return (
    <>
      <Button
        type="button"
        variant="outline"
        className={cn(
          'w-full pl-3 text-left font-normal',
          !value && 'text-muted-foreground'
        )}
        onClick={() => setOpen(true)}
        disabled={disabled || loading}
        aria-label={label || t('common.pickDate')}
      >
        {loading ? (
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
        ) : renderButtonLabel ? (
          renderButtonLabel((typeof value === 'undefined' ? null : value))
        ) : value ? (
          format(value, 'PPP', { locale })
        ) : (
          <span>{t('common.pickDate')}</span>
        )}
        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
      </Button>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="max-w-xs p-0">
          <DialogHeader>
            <DialogTitle>{label || t('events.date')}</DialogTitle>
          </DialogHeader>
          <div className="p-4">
            <Calendar
              mode="single"
              selected={value ?? undefined}
              onSelect={(date) => {
                // Prevent deselection - if date is undefined (deselected), keep the current value
                if (date) {
                  onChange(date);
                  setOpen(false);
                }
                // If date is undefined (user clicked same date to deselect), do nothing
              }}
              disabled={date => date < new Date('1900-01-01')}
              initialFocus
              defaultMonth={value ?? undefined}
              locale={locale}
            />
          </div>
        </DialogContent>
      </Dialog>
      {/*process.env.NODE_ENV === 'development' && (
        <div className="mt-2 text-xs text-blue-700 border border-dashed border-blue-300 rounded p-2">
          <strong>Debug:</strong> DatePickerDialog value: {value ? value.toISOString() : 'null'}
        </div>
      )}*/}
    </>
  );
};

export default DatePickerDialog; 