'use client';

import { useState, useEffect } from 'react';
import { Suspense } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useLanguage } from '@/lib/contexts/language-context';
import { useAppSelector } from '@/lib/redux/hooks';
import { canUserViewEvents, canUserCreateEvents } from '@/lib/utils/permissions-utils';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertTriangle } from 'lucide-react';
import { toast } from 'sonner';
import FullCalendar from './components/FullCalendar';
import EventForm from './components/EventForm';

export default function EventsCalendarPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const { t } = useLanguage();
  const permissions = useAppSelector(state => state.permissions);

  const [showEventModal, setShowEventModal] = useState(false);
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [selectedEventId, setSelectedEventId] = useState<string | undefined>(undefined);
  const [refresh, setRefresh] = useState(0);

  // Check permissions using Redux store
  const hasEventAccess = permissions && canUserViewEvents(permissions);
  const canCreateEvents = permissions && canUserCreateEvents(permissions);

  // Redirect if not authorized
  useEffect(() => {
    if (status === 'loading') return;

    if (!session) {
      router.push('/auth/signin');
      return;
    }

    if (!hasEventAccess) {
      toast.error(t('events.calendar.noPermissionAccess'));
      router.push('/');
      return;
    }
  }, [session, status, hasEventAccess, router]);
  
  const handleAddEvent = (date?: Date) => {
    // Check if user can create events
    if (!canCreateEvents) {
      toast.error(t('events.calendar.noPermissionCreate'));
      return;
    }

    if (date) {
      setSelectedDate(date);
    }
    setSelectedEventId(undefined);
    setShowEventModal(true);
  };
  
  const handleEditEvent = (eventId: string) => {
    setSelectedEventId(eventId);
    setSelectedDate(null);
    setShowEventModal(true);
  };
  
  const handleEventSaved = () => {
    setRefresh((r) => r + 1);
    setShowEventModal(false);
  };
  
  // Show loading state while session is loading
  if (status === 'loading') {
    return <CalendarSkeleton />;
  }

  // Redirect if not authorized
  if (!session || !hasEventAccess) {
    return null; // Will redirect
  }

  return (
    <div className="h-[calc(100vh-theme(spacing.16))] w-full overflow-hidden flex flex-col">
      <div className="flex-1 overflow-hidden">
        <Suspense fallback={<CalendarSkeleton />}>
          <FullCalendar
            className="h-full"
            onAddEvent={handleAddEvent}
            onEditEvent={handleEditEvent}
            refresh={refresh}
          />
        </Suspense>
      </div>

      {showEventModal && (
        <EventForm
          open={showEventModal}
          onClose={() => setShowEventModal(false)}
          onEventSaved={handleEventSaved}
          initialDate={selectedDate}
          eventId={selectedEventId}
        />
      )}
    </div>
  );
}

function CalendarSkeleton() {
  const { t } = useLanguage();
  
  return (
    <div className="h-full flex flex-col overflow-hidden">
      <div className="p-4 flex justify-between">
        <Skeleton className="h-10 w-24" />
        <div className="flex gap-2">
          <Skeleton className="h-10 w-10" aria-label={t('common.loading')} />
          <Skeleton className="h-10 w-10" aria-label={t('common.loading')} />
          <Skeleton className="h-10 w-10" aria-label={t('common.loading')} />
        </div>
      </div>
      <div className="flex-1 grid grid-cols-7 grid-rows-[auto_repeat(5,1fr)] gap-px overflow-hidden">
        {Array(7).fill(0).map((_, i) => (
          <Skeleton key={`day-${i}`} className="h-10 w-full" aria-label={t('common.loading')} />
        ))}
        {Array(35).fill(0).map((_, i) => (
          <Skeleton key={`cell-${i}`} className="w-full h-full" aria-label={t('common.loading')} />
        ))}
      </div>
    </div>
  );
} 