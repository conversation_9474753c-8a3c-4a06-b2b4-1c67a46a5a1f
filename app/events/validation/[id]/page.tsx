'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter, useParams, useSearchParams } from 'next/navigation';
import { useAppSelector } from '@/lib/redux/hooks';
import { canUserValidateEventReports } from '@/lib/utils/permissions-utils';
import { useLanguage } from '@/lib/contexts/language-context';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { 
  FileText, 
  AlertTriangle, 
  CheckCircle, 
  XCircle,
  ArrowLeft,
  Users,
  Calendar,
  MapPin,
  Clock,
  DollarSign,
  Eye,
  History,
  AlertCircle,
  Plus,
  Trash2,
  Search
} from 'lucide-react';
import { toast } from 'sonner';
import { format } from 'date-fns';

interface LinkedReservation {
  _id: string;
  customerInfo: {
    client1Name: string;
  };
  partnerId: {
    _id: string;
    name: string;
    email: string;
  };
  createdAt: string;
}

interface EventReservation extends LinkedReservation {
  linkStatus: 'linked' | 'unlinked' | 'linked_other';
  linkedToPapId?: string;
}

interface EventReport {
  _id: string;
  eventId: {
    _id: string;
    name: string;
    location: string;
    startDate: string;
    endDate: string;
    branchId: {
      _id: string;
      name: string;
    };
    partnerId: {
      _id: string;
      name: string;
    };
    eventTypeId: {
      _id: string;
      name: string;
      code: string;
    };
  } | null;
  status: 'pending' | 'processing' | 'submitted' | 'validated';
  supervisors: Array<{
    _id: string;
    name: string;
    email: string;
  }>;
  paps: Array<{
    userId: {
      _id: string;
      name: string;
      email: string;
    };
    timeRange: {
      startTime: string;
      endTime: string;
    };
    linkedReservationIds?: string[];
    linkedReservations?: LinkedReservation[];
  }>;
  cooks: Array<{
    userId: {
      _id: string;
      name: string;
      email: string;
    };
    timeRange: {
      startTime: string;
      endTime: string;
    };
    percentage?: number;
  }>;
  linkedReservationIds?: string[];
  reservationsByPap?: { [papId: string]: EventReservation[] };
  linkedReservationsSummary?: {
    total: number;
    byPap: { [papId: string]: number };
  };
  commissions?: {
    calculatedAt?: string;
    calculatedBy?: {
      _id: string;
      name: string;
    };
    paps?: Array<{
      userId: string;
      amount: number;
      reservationCount: number;
    }>;
    cooks?: Array<{
      userId: string;
      amount: number;
      percentage: number;
    }>;
    total?: number;
  };
  history: Array<{
    action: string;
    changedBy: {
      _id: string;
      name: string;
      email: string;
    };
    changedAt: string;
    previousValue?: any;
    newValue?: any;
  }>;
  validationComments?: string;
}

interface ValidationIssue {
  type: 'error' | 'warning' | 'info';
  category: 'personnel' | 'time' | 'commission' | 'reservation';
  message: string;
  details?: string;
}

export default function ValidateReportPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const params = useParams();
  const searchParams = useSearchParams();
  const reportId = params?.id as string;
  const { t } = useLanguage();
  const permissions = useAppSelector(state => state.permissions);
  const hasValidationPermissions = permissions && canUserValidateEventReports(permissions);

  // Get return URL for context preservation
  const returnUrl = searchParams?.get('return') || '/events/validation';
  const returnContext = returnUrl.includes('/events/') ? 'Event' : 'Reports';

  const [loading, setLoading] = useState(true);
  const [validating, setValidating] = useState(false);
  const [report, setReport] = useState<EventReport | null>(null);
  const [validationIssues, setValidationIssues] = useState<ValidationIssue[]>([]);
  const [validationNotes, setValidationNotes] = useState('');
  const [error, setError] = useState<string | null>(null);

  // Dialog states for reservation management
  const [selectedPap, setSelectedPap] = useState<any>(null);
  const [papReservationsDialogOpen, setPapReservationsDialogOpen] = useState(false);
  const [addReservationDialogOpen, setAddReservationDialogOpen] = useState(false);
  const [reservationSearchTerm, setReservationSearchTerm] = useState('');
  const [selectedReservationIds, setSelectedReservationIds] = useState<Set<string>>(new Set());

  // Loading states
  const [linkingReservation, setLinkingReservation] = useState<string | null>(null);
  const [bulkUpdating, setBulkUpdating] = useState(false);

  // Redirect if not authorized
  useEffect(() => {
    if (status === 'loading') return;

    if (!session) {
      router.push('/auth/signin');
      return;
    }

    if (!hasValidationPermissions) {
      router.push('/access-denied');
      return;
    }
  }, [session, status, hasValidationPermissions, router]);

  // Load report details
  useEffect(() => {
    if (hasValidationPermissions && reportId) {
      loadReportDetails();
    }
  }, [hasValidationPermissions, reportId]);



  const loadReportDetails = async () => {
    try {
      setLoading(true);
      
      const response = await fetch(`/api/event-reports/${reportId}`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch report: ${response.statusText}`);
      }
      
      const data = await response.json();
      setReport(data);
      
      // Run validation checks
      await runValidationChecks(data);
      
      setError(null);
    } catch (err) {
      console.error('Error loading report:', err);
      setError(err instanceof Error ? err.message : 'Failed to load report');
      toast.error('Failed to load report details');
    } finally {
      setLoading(false);
    }
  };

  const runValidationChecks = async (reportData: EventReport) => {
    const issues: ValidationIssue[] = [];

    // Handle case where eventId is null
    if (!reportData.eventId) {
      issues.push({
        type: 'error',
        category: 'personnel',
        message: t('events.validation.eventDataMissing'),
        details: t('events.validation.cannotValidateWithoutEvent')
      });
      setValidationIssues(issues);
      return;
    }

    // Check personnel time ranges
    const eventStart = new Date(reportData.eventId.startDate);
    const eventEnd = new Date(reportData.eventId.endDate);

    // Check PAP time ranges
    reportData.paps.forEach((pap) => {
      const papStart = new Date(pap.timeRange.startTime);
      const papEnd = new Date(pap.timeRange.endTime);
      
      if (papStart < eventStart || papEnd > eventEnd) {
        issues.push({
          type: 'warning',
          category: 'time',
          message: `PAP ${pap.userId.name} has time range outside event duration`,
          details: `PAP time: ${format(papStart, 'HH:mm')} - ${format(papEnd, 'HH:mm')}`
        });
      }
      
      if (papStart >= papEnd) {
        issues.push({
          type: 'error',
          category: 'time',
          message: `PAP ${pap.userId.name} has invalid time range`,
          details: 'Start time must be before end time'
        });
      }
    });

    // Check cook time ranges and percentages
    let totalCookPercentage = 0;
    reportData.cooks.forEach((cook) => {
      const cookStart = new Date(cook.timeRange.startTime);
      const cookEnd = new Date(cook.timeRange.endTime);
      
      if (cookStart < eventStart || cookEnd > eventEnd) {
        issues.push({
          type: 'warning',
          category: 'time',
          message: `Cook ${cook.userId.name} has time range outside event duration`,
          details: `Cook time: ${format(cookStart, 'HH:mm')} - ${format(cookEnd, 'HH:mm')}`
        });
      }
      
      if (cookStart >= cookEnd) {
        issues.push({
          type: 'error',
          category: 'time',
          message: `Cook ${cook.userId.name} has invalid time range`,
          details: 'Start time must be before end time'
        });
      }
      
      totalCookPercentage += cook.percentage || 0;
    });

    // Check cook percentage total
    if (reportData.cooks.length > 0 && Math.abs(totalCookPercentage - 100) > 0.01) {
      issues.push({
        type: 'error',
        category: 'personnel',
        message: 'Cook percentages do not sum to 100%',
        details: `Current total: ${totalCookPercentage.toFixed(1)}%`
      });
    }

    // Check if supervisors are assigned
    if (reportData.supervisors.length === 0) {
      issues.push({
        type: 'error',
        category: 'personnel',
        message: 'No supervisors assigned to this event',
        details: 'At least one supervisor is required'
      });
    }

    // Check commission calculations
    if (reportData.commissions && reportData.commissions.total !== undefined) {
      const totalCommissions = reportData.commissions.total;
      if (totalCommissions <= 0) {
        issues.push({
          type: 'warning',
          category: 'commission',
          message: 'No commissions calculated',
          details: 'This may indicate no linked reservations'
        });
      }
    } else {
      issues.push({
        type: 'info',
        category: 'commission',
        message: 'Commission calculations not yet performed',
        details: 'Commissions will be calculated upon approval'
      });
    }

    setValidationIssues(issues);
  };

  const updateLocalReportState = (papUserId: string, reservationId: string, action: 'link' | 'unlink') => {
    if (!report) return;

    // Update the report state locally
    const updatedReport = { ...report };
    const papIndex = updatedReport.paps.findIndex(pap => pap.userId._id === papUserId);

    if (papIndex !== -1) {
      const pap = { ...updatedReport.paps[papIndex] };
      const currentLinkedIds = pap.linkedReservationIds || [];

      if (action === 'link') {
        pap.linkedReservationIds = [...currentLinkedIds, reservationId];
        // Update linked reservations if they exist
        if (pap.linkedReservations && report.reservationsByPap?.[papUserId]) {
          const reservation = report.reservationsByPap[papUserId].find(r => r._id === reservationId);
          if (reservation) {
            pap.linkedReservations = [...(pap.linkedReservations || []), reservation];
          }
        }
      } else {
        pap.linkedReservationIds = currentLinkedIds.filter(id => id !== reservationId);
        if (pap.linkedReservations) {
          pap.linkedReservations = pap.linkedReservations.filter(r => r._id !== reservationId);
        }
      }

      updatedReport.paps[papIndex] = pap;

      // Update summary
      if (updatedReport.linkedReservationsSummary) {
        const totalChange = action === 'link' ? 1 : -1;
        updatedReport.linkedReservationsSummary.total += totalChange;
        updatedReport.linkedReservationsSummary.byPap[papUserId] = (updatedReport.linkedReservationsSummary.byPap[papUserId] || 0) + totalChange;
      }
    }

    setReport(updatedReport);

    // Update selected PAP if it's the one being modified
    if (selectedPap && selectedPap.userId._id === papUserId) {
      const updatedPap = updatedReport.paps.find(pap => pap.userId._id === papUserId);
      if (updatedPap) {
        setSelectedPap(updatedPap);
      }
    }
  };

  const handleReservationLink = async (papUserId: string, reservationId: string, action: 'link' | 'unlink') => {
    setLinkingReservation(reservationId);

    try {
      const response = await fetch(`/api/event-reports/${reportId}/link-reservation`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          papUserId,
          reservationId,
          action
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to ${action} reservation`);
      }

      const result = await response.json();
      toast.success(`Reservation ${action}ed successfully!`);

      // Update local state instead of reloading entire report
      updateLocalReportState(papUserId, reservationId, action);

      return result;
    } catch (error) {
      console.error(`Error ${action}ing reservation:`, error);
      toast.error(error instanceof Error ? error.message : `Failed to ${action} reservation`);
      throw error;
    } finally {
      setLinkingReservation(null);
    }
  };

  const closeAddReservationDialog = () => {
    setAddReservationDialogOpen(false);
    setReservationSearchTerm('');
    setSelectedReservationIds(new Set());
  };

  const closePapReservationsDialog = () => {
    setPapReservationsDialogOpen(false);
    setSelectedPap(null);
    setReservationSearchTerm('');
    setSelectedReservationIds(new Set());
  };

  const handleBulkReservationUpdate = async () => {
    if (!selectedPap || !report?.reservationsByPap) return;

    const currentlyLinkedIds = new Set(
      selectedPap.linkedReservationIds?.map((id: string) => id.toString()) || []
    );

    setBulkUpdating(true);

    try {
      // Determine which reservations to link and unlink
      const selectedIds = Array.from(selectedReservationIds) as string[];
      const currentIds = Array.from(currentlyLinkedIds) as string[];

      const toLink = selectedIds.filter(id => !currentlyLinkedIds.has(id));
      const toUnlink = currentIds.filter(id => !selectedReservationIds.has(id));

      // Process all changes sequentially to avoid race conditions
      for (const id of toLink) {
        await handleReservationLink(selectedPap.userId._id, id, 'link');
      }

      for (const id of toUnlink) {
        await handleReservationLink(selectedPap.userId._id, id, 'unlink');
      }

      toast.success(`Updated ${toLink.length + toUnlink.length} reservation links`);

      // Close the add reservation dialog
      closeAddReservationDialog();
    } catch (error) {
      console.error('Error updating reservations:', error);
      toast.error('Failed to update some reservation links');
    } finally {
      setBulkUpdating(false);
    }
  };

  const handleValidation = async (action: 'approve' | 'reject') => {
    if (!report) return;

    // Check for blocking errors
    const blockingErrors = validationIssues.filter(issue => issue.type === 'error');
    if (action === 'approve' && blockingErrors.length > 0) {
      toast.error('Cannot approve report with validation errors. Please resolve all errors first.');
      return;
    }

    setValidating(true);
    try {
      const response = await fetch(`/api/event-reports/${reportId}/validate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          approved: action === 'approve',
          comments: validationNotes
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to ${action} report`);
      }

      toast.success(`Report ${action === 'approve' ? 'approved' : 'rejected'} successfully!`);
      router.push(returnUrl);
    } catch (error) {
      console.error(`Error ${action}ing report:`, error);
      toast.error(error instanceof Error ? error.message : `Failed to ${action} report`);
    } finally {
      setValidating(false);
    }
  };

  // Show loading state while session is loading
  if (status === 'loading' || loading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading...</p>
          </div>
        </div>
      </div>
    );
  }

  // Return null if not authorized (will redirect)
  if (!session || !hasValidationPermissions) {
    return null;
  }

  if (error || !report) {
    return (
      <div className="container mx-auto py-6">
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {error || 'Report not found'}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  if (!report.eventId) {
    return (
      <div className="container mx-auto py-6">
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {t('events.validation.missingEventDataMessage')}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  const hasErrors = validationIssues.some(issue => issue.type === 'error');
  const hasWarnings = validationIssues.some(issue => issue.type === 'warning');

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push(returnUrl)}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to {returnContext}
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Validate Report</h1>
            <p className="text-muted-foreground">{report.eventId.name}</p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <Badge variant="outline">
            {report.status.charAt(0).toUpperCase() + report.status.slice(1)}
          </Badge>
        </div>
      </div>

      {/* Validation Issues */}
      {validationIssues.length > 0 && (
        <Card className={hasErrors ? 'border-red-500' : hasWarnings ? 'border-yellow-500' : 'border-blue-500'}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {hasErrors ? (
                <XCircle className="h-5 w-5 text-red-500" />
              ) : hasWarnings ? (
                <AlertTriangle className="h-5 w-5 text-yellow-500" />
              ) : (
                <AlertCircle className="h-5 w-5 text-blue-500" />
              )}
              Validation Issues ({validationIssues.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {validationIssues.map((issue, index) => (
                <Alert key={index} variant={issue.type === 'error' ? 'destructive' : 'default'}>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    <div className="flex items-start justify-between">
                      <div>
                        <p className="font-medium">{issue.message}</p>
                        {issue.details && (
                          <p className="text-sm text-muted-foreground mt-1">{issue.details}</p>
                        )}
                      </div>
                      <Badge variant="outline" className="ml-2">
                        {issue.category}
                      </Badge>
                    </div>
                  </AlertDescription>
                </Alert>
              ))}
            </div>

            {hasErrors && (
              <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
                <p className="text-sm text-red-800">
                  <strong>Action Required:</strong> This report cannot be approved until all errors are resolved.
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Report Details Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="personnel">Personnel</TabsTrigger>
          <TabsTrigger value="commissions">Commissions</TabsTrigger>
          <TabsTrigger value="history">History</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  Event Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Event Name</label>
                  <p className="text-sm">{report.eventId.name}</p>
                </div>

                <div>
                  <label className="text-sm font-medium text-muted-foreground">Location</label>
                  <p className="text-sm flex items-center gap-2">
                    <MapPin className="h-4 w-4" />
                    {report.eventId.location}
                  </p>
                </div>

                <div>
                  <label className="text-sm font-medium text-muted-foreground">Date & Time</label>
                  <p className="text-sm">
                    {format(new Date(report.eventId.startDate), 'PPP p')} - {format(new Date(report.eventId.endDate), 'PPP p')}
                  </p>
                </div>

                <div>
                  <label className="text-sm font-medium text-muted-foreground">Branch</label>
                  <p className="text-sm">{report.eventId.branchId.name}</p>
                </div>

                <div>
                  <label className="text-sm font-medium text-muted-foreground">Partner</label>
                  <p className="text-sm">{report.eventId.partnerId.name}</p>
                </div>

                <div>
                  <label className="text-sm font-medium text-muted-foreground">Event Type</label>
                  <p className="text-sm">{report.eventId.eventTypeId.name} ({report.eventId.eventTypeId.code})</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Personnel Summary
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div>
                    <div className="text-2xl font-bold text-blue-600">
                      {report.supervisors.length}
                    </div>
                    <div className="text-sm text-muted-foreground">Supervisors</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-green-600">
                      {report.paps.length}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      PAPs ({report.linkedReservationsSummary?.total || 0} linked reservations)
                    </div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-orange-600">
                      {report.cooks.length}
                    </div>
                    <div className="text-sm text-muted-foreground">Cooks</div>
                  </div>
                </div>

                <div className="space-y-3">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Supervisors</label>
                    <div className="text-sm">
                      {report.supervisors.map(supervisor => supervisor.name).join(', ') || 'None assigned'}
                    </div>
                  </div>

                  {report.linkedReservationsSummary && report.linkedReservationsSummary.total > 0 && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Linked Reservations ({report.linkedReservationsSummary.total})
                      </label>
                      <div className="mt-2 space-y-1 max-h-32 overflow-y-auto">
                        {report.paps
                          .filter(pap => pap.linkedReservations && pap.linkedReservations.length > 0)
                          .slice(0, 10) // Show max 10 reservations in summary
                          .flatMap(pap =>
                            pap.linkedReservations!.map(reservation => (
                              <div key={reservation._id} className="text-xs p-2 bg-gray-50 rounded border">
                                <div className="flex justify-between items-start">
                                  <div>
                                    <span className="font-medium">{reservation.customerInfo.client1Name}</span>
                                    <span className="text-muted-foreground ml-2">
                                      | Agent: {reservation.partnerId.name}
                                    </span>
                                  </div>
                                  <span className="text-muted-foreground">
                                    {format(new Date(reservation.createdAt), 'MMM d, yyyy')}
                                  </span>
                                </div>
                              </div>
                            ))
                          )}
                        {report.linkedReservationsSummary.total > 10 && (
                          <div className="text-xs text-muted-foreground text-center py-1">
                            ... and {report.linkedReservationsSummary.total - 10} more reservations
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="personnel" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* PAPs */}
            <Card>
              <CardHeader>
                <CardTitle>PAPs ({report.paps.length})</CardTitle>
              </CardHeader>
              <CardContent>
                {report.paps.length > 0 ? (
                  <div className="space-y-3">
                    {report.paps.map((pap, index) => (
                      <div key={index} className="p-3 border rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium">{pap.userId.name}</h4>
                          <div className="flex items-center gap-2">
                            <Badge variant="outline">
                              {pap.linkedReservations?.length || 0} reservations
                            </Badge>
                            <Badge variant="outline">PAP</Badge>
                          </div>
                        </div>
                        <div className="text-sm text-muted-foreground mb-3">
                          <p>{pap.userId.email}</p>
                          <p className="flex items-center gap-1 mt-1">
                            <Clock className="h-3 w-3" />
                            {format(new Date(pap.timeRange.startTime), 'HH:mm')} - {format(new Date(pap.timeRange.endTime), 'HH:mm')}
                          </p>
                        </div>
                        <div className="flex justify-end">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setSelectedPap(pap);
                              setPapReservationsDialogOpen(true);
                            }}
                            className="text-xs"
                          >
                            <Eye className="h-3 w-3 mr-1" />
                            View Linked Reservations
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-muted-foreground text-center py-4">No PAPs assigned</p>
                )}
              </CardContent>
            </Card>

            {/* Cooks */}
            <Card>
              <CardHeader>
                <CardTitle>Cooks ({report.cooks.length})</CardTitle>
              </CardHeader>
              <CardContent>
                {report.cooks.length > 0 ? (
                  <div className="space-y-3">
                    {report.cooks.map((cook, index) => (
                      <div key={index} className="p-3 border rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium">{cook.userId.name}</h4>
                          <div className="flex items-center gap-2">
                            <Badge variant="outline">{cook.percentage?.toFixed(1)}%</Badge>
                            <Badge variant="outline">Cook</Badge>
                          </div>
                        </div>
                        <div className="text-sm text-muted-foreground">
                          <p>{cook.userId.email}</p>
                          <p className="flex items-center gap-1 mt-1">
                            <Clock className="h-3 w-3" />
                            {format(new Date(cook.timeRange.startTime), 'HH:mm')} - {format(new Date(cook.timeRange.endTime), 'HH:mm')}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-muted-foreground text-center py-4">No cooks assigned</p>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="commissions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5" />
                Commission Calculations
              </CardTitle>
            </CardHeader>
            <CardContent>
              {report.commissions && report.commissions.total !== undefined ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="text-center p-4 bg-green-50 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">
                        ${(report.commissions.total || 0).toFixed(2)}
                      </div>
                      <div className="text-sm text-muted-foreground">Total Commissions</div>
                    </div>
                    <div className="text-center p-4 bg-blue-50 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">
                        {report.commissions.paps?.length || 0}
                      </div>
                      <div className="text-sm text-muted-foreground">PAP Commissions</div>
                    </div>
                    <div className="text-center p-4 bg-orange-50 rounded-lg">
                      <div className="text-2xl font-bold text-orange-600">
                        {report.commissions.cooks?.length || 0}
                      </div>
                      <div className="text-sm text-muted-foreground">Cook Commissions</div>
                    </div>
                  </div>

                  <div className="text-sm text-muted-foreground">
                    {report.commissions.calculatedAt && (
                      <p>Calculated on {format(new Date(report.commissions.calculatedAt), 'PPP p')}</p>
                    )}
                    {report.commissions.calculatedBy?.name && (
                      <p>By {report.commissions.calculatedBy.name}</p>
                    )}
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <DollarSign className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">Commission calculations will be performed upon approval</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <History className="h-5 w-5" />
                Audit Trail
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {report.history.map((entry, index) => (
                  <div key={index} className="flex items-start gap-3 p-3 border rounded-lg">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                    <div className="flex-1">
                      <p className="font-medium">{entry.action}</p>
                      <p className="text-sm text-muted-foreground">
                        By {entry.changedBy?.name || 'Unknown User'} on {format(new Date(entry.changedAt), 'PPP p')}
                      </p>
                      {entry.newValue && (
                        <div className="mt-2 text-xs bg-gray-50 p-2 rounded">
                          <pre>{JSON.stringify(entry.newValue, null, 2)}</pre>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Validation Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Validation Decision</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="validationNotes">Validation Notes</Label>
            <Textarea
              id="validationNotes"
              value={validationNotes}
              onChange={(e) => setValidationNotes(e.target.value)}
              placeholder="Enter any comments or notes about this validation..."
              rows={3}
            />
          </div>

          <div className="flex justify-end gap-4">
            <Button
              variant="outline"
              onClick={() => router.push(returnUrl)}
              disabled={validating}
            >
              Return to {returnContext}
            </Button>
            <Button
              variant="destructive"
              onClick={() => handleValidation('reject')}
              disabled={validating}
            >
              {validating ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Rejecting...
                </>
              ) : (
                <>
                  <XCircle className="h-4 w-4 mr-2" />
                  Reject Report
                </>
              )}
            </Button>
            <Button
              onClick={() => handleValidation('approve')}
              disabled={validating || hasErrors}
              className="min-w-[120px]"
            >
              {validating ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Approving...
                </>
              ) : (
                <>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Approve Report
                </>
              )}
            </Button>
          </div>

          {hasErrors && (
            <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-800">
                <strong>Cannot Approve:</strong> Please resolve all validation errors before approving this report.
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* PAP Reservations Dialog */}
      <Dialog open={papReservationsDialogOpen} onOpenChange={(open) => {
        if (!open) {
          closePapReservationsDialog();
        }
      }}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              Linked Reservations - {selectedPap?.userId.name}
            </DialogTitle>
          </DialogHeader>

          {selectedPap && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <p className="text-sm text-muted-foreground">
                  {selectedPap.linkedReservations?.length || 0} reservations linked to this PAP
                </p>
                <Button
                  size="sm"
                  onClick={() => {
                    if (selectedPap) {
                      // Initialize selected reservations with currently linked ones
                      const currentlyLinked = new Set<string>(
                        selectedPap.linkedReservationIds?.map((id: string) => id.toString()) || []
                      );
                      setSelectedReservationIds(currentlyLinked);
                      setReservationSearchTerm('');
                      setAddReservationDialogOpen(true);
                    }
                  }}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Manage Reservations
                </Button>
              </div>

              {selectedPap.linkedReservations && selectedPap.linkedReservations.length > 0 ? (
                <div className="space-y-2">
                  {selectedPap.linkedReservations.map((reservation: LinkedReservation) => (
                    <div key={reservation._id} className="p-3 border rounded-lg">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium">{reservation.customerInfo.client1Name}</h4>
                          <p className="text-sm text-muted-foreground">
                            Agent: {reservation.partnerId.name} |
                            Created: {format(new Date(reservation.createdAt), 'MMM d, yyyy HH:mm')}
                          </p>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          disabled={linkingReservation === reservation._id}
                          onClick={async () => {
                            try {
                              await handleReservationLink(
                                selectedPap.userId._id,
                                reservation._id,
                                'unlink'
                              );
                            } catch (error) {
                              // Error already handled in handleReservationLink
                            }
                          }}
                        >
                          {linkingReservation === reservation._id ? (
                            <>
                              <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-current mr-1"></div>
                              Unlinking...
                            </>
                          ) : (
                            <>
                              <Trash2 className="h-4 w-4 mr-1" />
                              Unlink
                            </>
                          )}
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No reservations linked to this PAP</p>
                  <p className="text-sm">Click "Add Reservation" to link reservations</p>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Add Reservation Dialog */}
      <Dialog open={addReservationDialogOpen} onOpenChange={(open) => {
        if (!open) {
          closeAddReservationDialog();
        }
      }}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              Manage Reservations - {selectedPap?.userId.name}
            </DialogTitle>
          </DialogHeader>

          {selectedPap && report?.reservationsByPap && (
            <div className="space-y-4">
              {/* Search */}
              <div className="flex items-center gap-2">
                <Search className="h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search by customer name or agent name..."
                  value={reservationSearchTerm}
                  onChange={(e) => setReservationSearchTerm(e.target.value)}
                  className="flex-1"
                />
              </div>

              {/* Reservations List */}
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {(report.reservationsByPap[selectedPap.userId._id] || [])
                  .filter((reservation: EventReservation) => {
                    if (!reservationSearchTerm) return true;
                    const searchLower = reservationSearchTerm.toLowerCase();
                    return (
                      reservation.customerInfo.client1Name.toLowerCase().includes(searchLower)
                    );
                  })
                  .map((reservation: EventReservation) => {
                    const isSelected = selectedReservationIds.has(reservation._id);
                    const isLinkedToOtherPap = reservation.linkStatus === 'linked' && reservation.linkedToPapId !== selectedPap.userId._id;
                    const isLinkedToOtherEvent = reservation.linkStatus === 'linked_other';
                    const isDisabled = isLinkedToOtherPap || isLinkedToOtherEvent;

                    return (
                      <div
                        key={reservation._id}
                        className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                          isSelected ? 'bg-blue-50 border-blue-300' : 'hover:bg-gray-50'
                        } ${isDisabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                        onClick={() => {
                          if (isDisabled) return;
                          const newSelected = new Set(selectedReservationIds);
                          if (isSelected) {
                            newSelected.delete(reservation._id);
                          } else {
                            newSelected.add(reservation._id);
                          }
                          setSelectedReservationIds(newSelected);
                        }}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3 flex-1">
                            <input
                              type="checkbox"
                              checked={isSelected}
                              disabled={isDisabled}
                              onChange={() => {}} // Handled by div onClick
                              className="h-4 w-4"
                            />
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-1">
                                <h4 className="font-medium">{reservation.customerInfo.client1Name}</h4>
                                {isLinkedToOtherPap && (
                                  <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                                    🔗 Linked to another PAP
                                  </Badge>
                                )}
                                {isLinkedToOtherEvent && (
                                  <Badge variant="outline" className="bg-gray-100 text-gray-600">
                                    ⭕ Linked to other event
                                  </Badge>
                                )}
                              </div>
                              <p className="text-sm text-muted-foreground">
                                Created: {format(new Date(reservation.createdAt), 'MMM d, yyyy HH:mm')}
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
              </div>

              {(report.reservationsByPap[selectedPap.userId._id] || []).length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No reservations found for this PAP</p>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex justify-end gap-2 pt-4 border-t">
                <Button
                  variant="outline"
                  onClick={closeAddReservationDialog}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleBulkReservationUpdate}
                  disabled={selectedReservationIds.size === 0 || bulkUpdating}
                >
                  {bulkUpdating ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Updating...
                    </>
                  ) : (
                    `Update Links (${selectedReservationIds.size} selected)`
                  )}
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
