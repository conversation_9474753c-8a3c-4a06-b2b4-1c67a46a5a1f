'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useAppSelector } from '@/lib/redux/hooks';
import { useLanguage } from '@/lib/contexts/language-context';
import { canUserValidateEventReports } from '@/lib/utils/permissions-utils';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  FileText, 
  AlertTriangle, 
  Clock, 
  CheckCircle, 
  Search, 
  Filter,
  RefreshCw,
  Users,
  Calendar,
  MapPin,
  Eye,
  CheckSquare,
  MoreH<PERSON>zon<PERSON>
} from 'lucide-react';
import { toast } from 'sonner';
import { format } from 'date-fns';

interface EventReport {
  _id: string;
  eventId: {
    _id: string;
    name: string;
    location: string;
    startDate: string;
    endDate: string;
    branchId: {
      _id: string;
      name: string;
    };
    partnerId: {
      _id: string;
      name: string;
    };
    eventTypeId: {
      _id: string;
      name: string;
      code: string;
    };
  } | null;
  status: 'pending' | 'processing' | 'submitted' | 'validated';
  supervisors: Array<{
    _id: string;
    name: string;
    email: string;
  }>;
  paps: Array<{
    userId: {
      _id: string;
      name: string;
      email: string;
    };
    timeRange: {
      startTime: string;
      endTime: string;
    };
  }>;
  cooks: Array<{
    userId: {
      _id: string;
      name: string;
      email: string;
    };
    timeRange: {
      startTime: string;
      endTime: string;
    };
    percentage?: number;
  }>;
  metadata: {
    priority: 'normal' | 'high' | 'urgent';
    daysSinceSubmission: number;
    submissionDate: string;
    submittedBy: {
      _id: string;
      name: string;
      email: string;
    };
    summary: {
      totalPersonnel: number;
      totalPAPs: number;
      totalCooks: number;
      eventDurationHours: number;
    };
  };
}

interface Summary {
  total: number;
  urgent: number;
  normal: number;
  avgWaitTime: number;
}

export default function AdminEventReportsPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const permissions = useAppSelector(state => state.permissions);
  const { t } = useLanguage();
  const hasValidationPermissions = permissions && canUserValidateEventReports(permissions);

  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [reports, setReports] = useState<EventReport[]>([]);
  const [summary, setSummary] = useState<Summary>({
    total: 0,
    urgent: 0,
    normal: 0,
    avgWaitTime: 0
  });
  const [selectedReports, setSelectedReports] = useState<string[]>([]);
  
  // Filters
  const [searchTerm, setSearchTerm] = useState('');
  const [priorityFilter, setPriorityFilter] = useState('all');
  const [sortBy, setSortBy] = useState('submission');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

  // Redirect if not authorized
  useEffect(() => {
    if (status === 'loading') return;

    if (!session) {
      router.push('/auth/signin');
      return;
    }

    if (!hasValidationPermissions) {
      router.push('/access-denied');
      return;
    }
  }, [session, status, hasValidationPermissions, router]);

  // Load reports
  useEffect(() => {
    if (hasValidationPermissions) {
      loadReports();
    }
  }, [hasValidationPermissions, priorityFilter]);

  const loadReports = async (showRefreshIndicator = false) => {
    try {
      if (showRefreshIndicator) setRefreshing(true);
      
      const params = new URLSearchParams();
      if (priorityFilter !== 'all') {
        params.append('priority', priorityFilter);
      }
      params.append('limit', '100');
      
      const response = await fetch(`/api/admin/event-reports/pending?${params}`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch reports: ${response.statusText}`);
      }
      
      const data = await response.json();
      setReports(data.reports || []);
      setSummary(data.summary || { total: 0, urgent: 0, normal: 0, avgWaitTime: 0 });
    } catch (error) {
      console.error('Error loading reports:', error);
      toast.error(t('events.errors.failedToLoad'));
    } finally {
      setLoading(false);
      if (showRefreshIndicator) setRefreshing(false);
    }
  };

  // Filter and sort reports
  const filteredReports = reports
    .filter(report => {
      if (searchTerm) {
        const searchLower = searchTerm.toLowerCase();
        return (
          (report.eventId?.name?.toLowerCase().includes(searchLower)) ||
          (report.eventId?.location?.toLowerCase().includes(searchLower)) ||
          (report.eventId?.branchId?.name?.toLowerCase().includes(searchLower)) ||
          (report.eventId?.partnerId?.name?.toLowerCase().includes(searchLower)) ||
          report.supervisors.some(s => s.name.toLowerCase().includes(searchLower))
        );
      }
      return true;
    })
    .sort((a, b) => {
      let aValue, bValue;

      switch (sortBy) {
        case 'submission':
          aValue = new Date(a.metadata.submissionDate).getTime();
          bValue = new Date(b.metadata.submissionDate).getTime();
          break;
        case 'priority':
          const priorityOrder = { urgent: 3, high: 2, normal: 1 };
          aValue = priorityOrder[a.metadata.priority];
          bValue = priorityOrder[b.metadata.priority];
          break;
        case 'event':
          aValue = a.eventId?.name?.toLowerCase() || '';
          bValue = b.eventId?.name?.toLowerCase() || '';
          break;
        case 'branch':
          aValue = a.eventId?.branchId?.name?.toLowerCase() || '';
          bValue = b.eventId?.branchId?.name?.toLowerCase() || '';
          break;
        default:
          aValue = new Date(a.metadata.submissionDate).getTime();
          bValue = new Date(b.metadata.submissionDate).getTime();
      }

      if (sortOrder === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

  const handleSelectReport = (reportId: string) => {
    setSelectedReports(prev => 
      prev.includes(reportId) 
        ? prev.filter(id => id !== reportId)
        : [...prev, reportId]
    );
  };

  const handleSelectAll = () => {
    if (selectedReports.length === filteredReports.length) {
      setSelectedReports([]);
    } else {
      setSelectedReports(filteredReports.map(r => r._id));
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-500';
      case 'high': return 'bg-yellow-500';
      default: return 'bg-blue-500';
    }
  };

  const getPriorityBadgeVariant = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'destructive';
      case 'high': return 'secondary';
      default: return 'outline';
    }
  };

  // Show loading state while session is loading
  if (status === 'loading' || loading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">{t('common.loading')}</p>
          </div>
        </div>
      </div>
    );
  }

  // Return null if not authorized (will redirect)
  if (!session || !hasValidationPermissions) {
    return null;
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            {t('events.validation.title')}
          </h1>
          <p className="text-muted-foreground">
            {t('events.validation.description')}
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          {selectedReports.length > 0 && (
            <Button size="sm" className="mr-2">
              <CheckSquare className="h-4 w-4 mr-2" />
              {t('events.validation.bulkActions')} ({selectedReports.length})
            </Button>
          )}
          <Button
            variant="outline"
            size="sm"
            onClick={() => loadReports(true)}
            disabled={refreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            {t('common.refresh')}
          </Button>
        </div>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <FileText className="h-4 w-4 text-muted-foreground" />
              <div className="ml-2">
                <p className="text-sm font-medium text-muted-foreground">{t('events.validation.totalPending')}</p>
                <div className="text-2xl font-bold">{summary.total}</div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <AlertTriangle className="h-4 w-4 text-red-500" />
              <div className="ml-2">
                <p className="text-sm font-medium text-muted-foreground">{t('events.validation.urgent')}</p>
                <div className="text-2xl font-bold text-red-600">{summary.urgent}</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <Clock className="h-4 w-4 text-blue-500" />
              <div className="ml-2">
                <p className="text-sm font-medium text-muted-foreground">{t('events.validation.normal')}</p>
                <div className="text-2xl font-bold text-blue-600">{summary.normal}</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <Clock className="h-4 w-4 text-muted-foreground" />
              <div className="ml-2">
                <p className="text-sm font-medium text-muted-foreground">{t('events.validation.avgWaitTime')}</p>
                <div className="text-2xl font-bold">{summary.avgWaitTime.toFixed(1)}d</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            {t('events.filtersAndSearch')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder={t('events.validation.searchPlaceholder')}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            <Select value={priorityFilter} onValueChange={setPriorityFilter}>
              <SelectTrigger>
                <SelectValue placeholder={t('events.validation.filterByPriority')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{t('events.validation.allPriorities')}</SelectItem>
                <SelectItem value="urgent">{t('events.validation.urgent')}</SelectItem>
                <SelectItem value="high">{t('events.validation.high')}</SelectItem>
                <SelectItem value="normal">{t('events.validation.normal')}</SelectItem>
              </SelectContent>
            </Select>

            <Select value={`${sortBy}-${sortOrder}`} onValueChange={(value) => {
              const [field, order] = value.split('-');
              setSortBy(field);
              setSortOrder(order as 'asc' | 'desc');
            }}>
              <SelectTrigger>
                <SelectValue placeholder={t('events.sortBy')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="submission-asc">{t('events.validation.submissionOldest')}</SelectItem>
                <SelectItem value="submission-desc">{t('events.validation.submissionNewest')}</SelectItem>
                <SelectItem value="priority-desc">{t('events.validation.priorityHighToLow')}</SelectItem>
                <SelectItem value="event-asc">{t('events.sort.nameAZ')}</SelectItem>
                <SelectItem value="branch-asc">{t('events.validation.branchAZ')}</SelectItem>
              </SelectContent>
            </Select>

            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleSelectAll}
                className="flex-1"
              >
                {selectedReports.length === filteredReports.length ? t('events.validation.deselectAll') : t('events.validation.selectAll')}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Reports List */}
      <div className="space-y-4">
        {filteredReports.length > 0 ? (
          filteredReports.map((report) => (
            <ReportCard
              key={report._id}
              report={report}
              isSelected={selectedReports.includes(report._id)}
              onSelect={() => handleSelectReport(report._id)}
              onView={() => router.push(`/events/validation/${report._id}`)}
            />
          ))
        ) : (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <FileText className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">
                {t('events.validation.noReportsFound')}
              </h3>
              <p className="text-muted-foreground text-center">
                {searchTerm || priorityFilter !== 'all'
                  ? t('events.validation.noReportsMatchFilters')
                  : t('events.validation.noReportsPendingValidation')
                }
              </p>
              {(searchTerm || priorityFilter !== 'all') && (
                <Button
                  variant="outline"
                  className="mt-4"
                  onClick={() => {
                    setSearchTerm('');
                    setPriorityFilter('all');
                  }}
                >
                  {t('events.clearFilters')}
                </Button>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}

// Report Card Component
interface ReportCardProps {
  report: EventReport;
  isSelected: boolean;
  onSelect: () => void;
  onView: () => void;
}

function ReportCard({ report, isSelected, onSelect, onView }: ReportCardProps) {
  const priorityColor = report.metadata.priority === 'urgent' ? 'border-red-500' :
                       report.metadata.priority === 'high' ? 'border-yellow-500' :
                       'border-blue-500';
  const {t}= useLanguage();

  // Handle case where eventId is null
  if (!report.eventId) {
    return (
      <Card className={`transition-all hover:shadow-md ${isSelected ? 'ring-2 ring-primary' : ''} ${priorityColor} border-l-4`}>
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex items-start gap-3">
              <input
                type="checkbox"
                checked={isSelected}
                onChange={onSelect}
                className="mt-1"
              />
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <h3 className="font-semibold text-lg text-muted-foreground">{t('events.validation.eventDataUnavailable')}</h3>
                  <Badge variant={getPriorityBadgeVariant(report.metadata.priority)}>
                    {report.metadata.priority.toUpperCase()}
                  </Badge>
                </div>
                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                  <span>Report ID: {report._id}</span>
                  <Badge variant="destructive">{t('events.validation.missingEventData')}</Badge>
                </div>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={onView}
                className="flex items-center gap-1"
              >
                <Eye className="h-3 w-3" />
                {t('events.validation.viewDetails')}
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="text-center py-4">
            <p className="text-muted-foreground">
              {t('events.validation.missingEventDataMessage')}
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`transition-all hover:shadow-md ${isSelected ? 'ring-2 ring-primary' : ''} ${priorityColor} border-l-4`}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-start gap-3">
            <input
              type="checkbox"
              checked={isSelected}
              onChange={onSelect}
              className="mt-1"
            />
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-1">
                <h3 className="font-semibold text-lg">{report.eventId.name}</h3>
                <Badge variant={getPriorityBadgeVariant(report.metadata.priority)}>
                  {report.metadata.priority.toUpperCase()}
                </Badge>
              </div>
              <div className="flex items-center gap-4 text-sm text-muted-foreground">
                <span className="flex items-center gap-1">
                  <MapPin className="h-3 w-3" />
                  {report.eventId.location}
                </span>
                <span className="flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  {format(new Date(report.eventId.startDate), 'MMM d, yyyy')}
                </span>
                <span className="flex items-center gap-1">
                  <Users className="h-3 w-3" />
                  {report.metadata.summary.totalPersonnel} personnel
                </span>
              </div>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-xs">
              {report.metadata.daysSinceSubmission.toFixed(1)}d ago
            </Badge>
            <Button
              variant="outline"
              size="sm"
              onClick={onView}
            >
              <Eye className="h-4 w-4 mr-2" />
              {t('events.validation.review')}
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <p className="text-muted-foreground">{t('events.card.branch')}</p>
            <p className="font-medium">{report.eventId.branchId.name}</p>
          </div>
          <div>
            <p className="text-muted-foreground">{t('events.card.partner')}</p>
            <p className="font-medium">{report.eventId.partnerId.name}</p>
          </div>
          <div>
            <p className="text-muted-foreground">{t('events.validation.supervisor')}</p>
            <p className="font-medium">{report.supervisors[0]?.name || t('common.na')}</p>
          </div>
          <div>
            <p className="text-muted-foreground">{t('events.validation.duration')}</p>
            <p className="font-medium">{report.metadata.summary.eventDurationHours}h</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

function getPriorityBadgeVariant(priority: string) {
  switch (priority) {
    case 'urgent': return 'destructive' as const;
    case 'high': return 'secondary' as const;
    default: return 'outline' as const;
  }
}
