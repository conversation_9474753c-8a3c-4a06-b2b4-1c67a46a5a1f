'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter, useParams } from 'next/navigation';

import { useAppSelector } from '@/lib/redux/hooks';
import { useLanguage } from '@/lib/contexts/language-context';
import { canUserSuperviseEvents, canUserEditEventReports, canUserViewEventCommissions } from '@/lib/utils/permissions-utils';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  ArrowLeft,
  FileText,
  DollarSign,
  Users,
  Calendar,
  AlertTriangle,
  CheckCircle,
  Edit,
  Send,
  RefreshCw,
  Clock,
  MapPin
} from 'lucide-react';
import { toast } from 'sonner';
import { formatInTimeZone } from 'date-fns-tz';

// Timezone-aware formatting helper
const formatDateLocal = (utcDateString: string, formatStr: string, timezone: string) => {
  try {
    const utcDate = new Date(utcDateString);
    return formatInTimeZone(utcDate, timezone, formatStr);
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Invalid date';
  }
};

interface EventReport {
  _id: string;
  eventId: {
    _id: string;
    name: string;
    startDate: string;
    endDate: string;
    location: string;
    branchId: {
      _id: string;
      name: string;
    };
    partnerId: {
      _id: string;
      name: string;
    };
  };
  status: 'pending' | 'processing' | 'submitted' | 'validated';
  eventStartTime?: string;
  eventEndTime?: string;
  createdAt: string;
  updatedAt: string;
  submittedAt?: string;
  validatedAt?: string;
  totalCommissions: number;
  personnelData: {
    supervisors: Array<{
      userId: string;
      name: string;
      email: string;
      commission: number;
      hoursWorked?: number;
    }>;
    paps: Array<{
      userId: string;
      name: string;
      email: string;
      commission: number;
      hoursWorked: number;
      timeRange: {
        startTime: string;
        endTime: string;
      };
    }>;
    cooks: Array<{
      userId: string;
      name: string;
      email: string;
      commission: number;
      hoursWorked: number;
      percentage: number;
      timeRange: {
        startTime: string;
        endTime: string;
      };
    }>;
  };
  reservationData: {
    totalReservations: number;
    linkedReservations: Array<{
      _id: string;
      customerName: string;
      partySize: number;
      reservationTime: string;
      papCommission: number;
    }>;
  };
  commissionRates: {
    supervisorRate: number;
    papBaseRate: number;
    papEarlyBookingRate: number;
    cookBaseRate: number;
  };
  notes?: string;
  validationNotes?: string;
}

export default function ReportDetailsPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const params = useParams();

  const permissions = useAppSelector(state => state.permissions);
  const { t } = useLanguage();

  // Get user's timezone
  const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

  const [report, setReport] = useState<EventReport | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [updating, setUpdating] = useState(false);
  const [reportId, setReportId] = useState<string | null>(null);

  const eventId = params?.id as string;

  // Check permissions using Redux store
  const hasReportPermissions = permissions && (
    canUserSuperviseEvents(permissions) ||
    canUserEditEventReports(permissions)
  );

  const hasCommissionPermissions = permissions && canUserViewEventCommissions(permissions);

  // Redirect if not authorized
  useEffect(() => {
    if (status === 'loading') return;
    
    if (!session) {
      router.push('/auth/signin');
      return;
    }
    
    if (!hasReportPermissions) {
      toast.error(t('events.reports.details.noPermissionAccess'));
      router.push('/');
      return;
    }
  }, [session, status, hasReportPermissions, router]);

  // Fetch event data to get report ID
  const fetchEventData = async () => {
    try {
      const response = await fetch(`/api/events/${eventId}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch event: ${response.statusText}`);
      }

      const eventData = await response.json();

      if (!eventData.reportId) {
        throw new Error(t('events.reports.edit.noReportFound'));
      }

      // Handle both populated and non-populated reportId
      const reportIdValue = typeof eventData.reportId === 'string'
        ? eventData.reportId
        : eventData.reportId._id;

      setReportId(reportIdValue);
      return reportIdValue;
    } catch (err) {
      console.error('Error fetching event data:', err);
      setError(err instanceof Error ? err.message : t('events.reports.details.failedLoadEventData'));
      toast.error(t('events.reports.details.failedLoadEventData'));
      throw err;
    }
  };

  // Fetch report details using report ID
  const fetchReport = async (reportIdToFetch?: string) => {
    try {
      const idToUse = reportIdToFetch || reportId;
      if (!idToUse) {
        throw new Error(t('events.reports.edit.noReportIdAvailable'));
      }

      const response = await fetch(`/api/event-reports/${idToUse}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch report: ${response.statusText}`);
      }

      const data = await response.json();
      setReport(data);
      setError(null);
    } catch (err) {
      console.error('Error fetching report:', err);
      setError(err instanceof Error ? err.message : t('events.reports.details.failedLoadReport'));
      toast.error(t('events.reports.details.failedLoadReport'));
    } finally {
      setLoading(false);
    }
  };

  // Combined fetch function
  const fetchReportData = async () => {
    try {
      setLoading(true);
      const fetchedReportId = await fetchEventData();
      await fetchReport(fetchedReportId);
    } catch (err) {
      // Error handling is done in individual functions
      setLoading(false);
    }
  };

  useEffect(() => {
    if (hasReportPermissions && eventId) {
      fetchReportData();
    }
  }, [hasReportPermissions, eventId]);

  // Handle status change
  const handleStatusChange = async (newStatus: string) => {
    if (!report) return;

    try {
      setUpdating(true);

      if (newStatus === 'submitted') {
        // Use the submit endpoint for submitting reports
        const response = await fetch(`/api/event-reports/${report._id}/submit`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          const errorData = await response.json();
          console.error('Submit validation error:', errorData);

          // Show detailed validation errors if available
          if (errorData.details && Array.isArray(errorData.details)) {
            const errorMessage = `${t('events.reports.details.validationFailed')}:\n${errorData.details.join('\n')}`;
            toast.error(errorMessage);
          } else {
            toast.error(errorData.error || t('events.reports.details.failedSubmitReport'));
          }
          return;
        }

        toast.success(t('events.reports.details.reportSubmittedSuccess'));
        // Refresh the report data to get updated status
        await fetchReportData();
      } else {
        // For other status changes, use PATCH
        const response = await fetch(`/api/event-reports/${report._id}`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ status: newStatus }),
        });

        if (!response.ok) {
          throw new Error(t('events.reports.details.failedUpdateStatus'));
        }

        const updatedReport = { ...report, status: newStatus as any };
        setReport(updatedReport);
        toast.success(t('events.reports.details.reportStatusUpdatedSuccess'));
      }
    } catch (error) {
      console.error('Error updating report status:', error);
      toast.error(t('events.reports.details.failedUpdateStatus'));
    } finally {
      setUpdating(false);
    }
  };

  if (status === 'loading' || loading) {
    return <ReportDetailsPageSkeleton />;
  }

  if (!session || !hasReportPermissions) {
    return null; // Will redirect
  }

  if (error) {
    return (
      <div className="container mx-auto py-6">
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {error}
            <Button
              variant="outline"
              size="sm"
              className="ml-4"
              onClick={fetchReportData}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              {t('events.reports.details.retry')}
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  if (!report) {
    return (
      <div className="container mx-auto py-6">
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {t('events.reports.details.reportNotFound')}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  const canEditReport = canUserEditEventReports(permissions) && ['pending', 'processing'].includes(report.status);
  const canSubmitReport = canUserEditEventReports(permissions) && report.status === 'processing';

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-orange-100 text-orange-800';
      case 'processing': return 'bg-blue-100 text-blue-800';
      case 'submitted': return 'bg-purple-100 text-purple-800';
      case 'validated': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Event Summary Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-2xl">{report.eventId.name}</CardTitle>
              <div className="flex items-center gap-2 text-muted-foreground mt-1">
                <MapPin className="h-4 w-4" />
                {report.eventId.location}
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Badge className={getStatusColor(report.status)}>
                {report.status.charAt(0).toUpperCase() + report.status.slice(1)}
              </Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <p className="text-muted-foreground">
                {report.eventStartTime ? t('events.reports.details.actualEventDate') : t('events.reports.details.scheduledEventDate')}
              </p>
              <p className="font-medium">
                {formatDateLocal(report.eventStartTime || report.eventId.startDate, 'PPP', userTimezone)}
              </p>
              {report.eventStartTime && (
                <p className="text-xs text-green-600 mt-1">{t('events.reports.details.reported')}</p>
              )}
            </div>
            <div>
              <p className="text-muted-foreground">{t('events.reports.details.branch')}</p>
              <p className="font-medium">{report.eventId.branchId.name}</p>
            </div>
            <div>
              <p className="text-muted-foreground">{t('events.reports.details.partner')}</p>
              <p className="font-medium">{report.eventId.partnerId.name}</p>
            </div>
            <div>
              <p className="text-muted-foreground">{t('events.reports.details.reportStatus')}</p>
              <p className="font-medium">{report.status}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => router.back()}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          {t('events.reports.details.back')}
        </Button>

        <div className="flex-1">
          <div className="flex items-center gap-3 mb-2">
            <h1 className="text-3xl font-bold tracking-tight">
              {t('events.reports.details.title')}
            </h1>
          </div>
          <p className="text-muted-foreground">
            {t('events.reports.details.description')}
          </p>
        </div>

        <div className="flex items-center gap-2">
          {canEditReport && (
            <Button
              onClick={() => router.push(`/events/${report.eventId._id}/report/edit`)}
            >
              <Edit className="h-4 w-4 mr-2" />
              {t('events.reports.details.editReport')}
            </Button>
          )}
          {canSubmitReport && (
            <Button
              onClick={() => handleStatusChange('submitted')}
              disabled={updating}
            >
              <Send className="h-4 w-4 mr-2" />
              {t('events.reports.details.submitReport')}
            </Button>
          )}
        </div>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">{t('events.reports.tabs.overview')}</TabsTrigger>
          <TabsTrigger value="personnel">
            {hasCommissionPermissions ? t('events.reports.tabs.personnelCommissions') : t('events.reports.tabs.personnel')}
          </TabsTrigger>
          <TabsTrigger value="reservations">{t('events.reports.tabs.reservations')}</TabsTrigger>
          <TabsTrigger value="history">{t('events.reports.tabs.history')}</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <ReportOverview report={report} showCommissions={hasCommissionPermissions} userTimezone={userTimezone} />
        </TabsContent>

        <TabsContent value="personnel" className="space-y-4">
          <PersonnelCommissions report={report} showCommissions={hasCommissionPermissions} userTimezone={userTimezone} />
        </TabsContent>

        <TabsContent value="reservations" className="space-y-4">
          <ReservationsTab report={report} showCommissions={hasCommissionPermissions} userTimezone={userTimezone} />
        </TabsContent>

        <TabsContent value="history" className="space-y-4">
          <ReportHistory report={report} userTimezone={userTimezone} />
        </TabsContent>
      </Tabs>
    </div>
  );
}

// Helper Components
function ReportOverview({ report, showCommissions, userTimezone }: { report: EventReport; showCommissions: boolean; userTimezone: string }) {
  const { t } = useLanguage();
  return (
    <div className="space-y-6">
      {/* Actual Event Times Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            {t('events.reports.details.actualEventTimes')}
          </CardTitle>
          <p className="text-sm text-muted-foreground">
            {t('events.reports.details.actualEventTimesDescription')}
          </p>
        </CardHeader>
        <CardContent>
          {report.eventStartTime && report.eventEndTime ? (
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-muted-foreground">{t('events.reports.details.startTime')}</label>
                <p className="text-lg font-semibold">
                  {formatDateLocal(report.eventStartTime, 'PPP p', userTimezone)}
                </p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">{t('events.reports.details.endTime')}</label>
                <p className="text-lg font-semibold">
                  {formatDateLocal(report.eventEndTime, 'PPP p', userTimezone)}
                </p>
              </div>
            </div>
          ) : (
            <div className="text-center py-8">
              <Clock className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">
                {t('events.reports.details.actualEventTimesNotReported')}
              </p>
              <p className="text-sm text-muted-foreground mt-1">
                {t('events.reports.details.timesAvailableAfterEdit')}
              </p>
            </div>
          )}
          <p className="text-xs text-muted-foreground mt-4">
            {t('events.reports.details.timesShownIn')} {userTimezone}
          </p>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            {t('events.reports.details.eventInformation')}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="text-sm font-medium text-muted-foreground">{t('events.reports.details.eventName')}</label>
            <p className="text-sm font-medium">{report.eventId.name}</p>
          </div>

          <div>
            <label className="text-sm font-medium text-muted-foreground">{t('events.reports.details.actualEventTimesReported')}</label>
            {report.eventStartTime && report.eventEndTime ? (
              <p className="text-sm">
                {formatDateLocal(report.eventStartTime, 'PPP p', userTimezone)} - {formatDateLocal(report.eventEndTime, 'PPP p', userTimezone)}
              </p>
            ) : (
              <p className="text-sm text-muted-foreground italic">
                {t('events.reports.details.notYetReported')}
              </p>
            )}
            <p className="text-xs text-muted-foreground mt-1">
              {t('events.reports.details.timesShownIn')} {userTimezone}
            </p>
          </div>

          <div>
            <label className="text-sm font-medium text-muted-foreground">{t('events.reports.details.originalScheduledTimes')}</label>
            <p className="text-sm text-muted-foreground">
              {formatDateLocal(report.eventId.startDate, 'PPP p', userTimezone)} - {formatDateLocal(report.eventId.endDate, 'PPP p', userTimezone)}
            </p>
          </div>

          <div>
            <label className="text-sm font-medium text-muted-foreground">{t('events.reports.details.location')}</label>
            <p className="text-sm">{report.eventId.location}</p>
          </div>

          <div>
            <label className="text-sm font-medium text-muted-foreground">{t('events.reports.details.branch')}</label>
            <p className="text-sm">{report.eventId.branchId.name}</p>
          </div>

          <div>
            <label className="text-sm font-medium text-muted-foreground">{t('events.reports.details.partner')}</label>
            <p className="text-sm">{report.eventId.partnerId.name}</p>
          </div>
        </CardContent>
      </Card>

      {showCommissions && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              {t('events.reports.details.commissionSummary')}
            </CardTitle>
          </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-center">
            <div className="text-3xl font-bold text-green-600">
              ${report.totalCommissions.toFixed(2)}
            </div>
            <div className="text-sm text-muted-foreground">{t('events.reports.details.totalCommissions')}</div>
          </div>

          <div className="grid grid-cols-3 gap-4 text-center pt-4 border-t">
            <div>
              <div className="text-lg font-semibold text-blue-600">
                {report.personnelData.supervisors.length}
              </div>
              <div className="text-xs text-muted-foreground">{t('events.reports.details.supervisors')}</div>
              <div className="text-sm font-medium">
                ${report.personnelData.supervisors.reduce((sum, s) => sum + s.commission, 0).toFixed(2)}
              </div>
            </div>
            <div>
              <div className="text-lg font-semibold text-green-600">
                {report.personnelData.paps.length}
              </div>
              <div className="text-xs text-muted-foreground">{t('events.reports.details.paps')}</div>
              <div className="text-sm font-medium">
                ${report.personnelData.paps.reduce((sum, p) => sum + p.commission, 0).toFixed(2)}
              </div>
            </div>
            <div>
              <div className="text-lg font-semibold text-orange-600">
                {report.personnelData.cooks.length}
              </div>
              <div className="text-xs text-muted-foreground">{t('events.reports.details.cooks')}</div>
              <div className="text-sm font-medium">
                ${report.personnelData.cooks.reduce((sum, c) => sum + c.commission, 0).toFixed(2)}
              </div>
            </div>
          </div>

          <div className="pt-4 border-t">
            <div className="text-center">
              <div className="text-lg font-semibold text-purple-600">
                {report.reservationData.totalReservations}
              </div>
              <div className="text-sm text-muted-foreground">{t('events.reports.details.totalReservations')}</div>
            </div>
          </div>
        </CardContent>
        </Card>
      )}
      </div>
    </div>
  );
}

function PersonnelCommissions({ report, showCommissions, userTimezone }: { report: EventReport; showCommissions: boolean; userTimezone: string }) {
  const { t } = useLanguage();
  return (
    <div className="space-y-6">
      {/* Supervisors */}
      {report.personnelData.supervisors.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              {t('events.reports.details.supervisors')} ({report.personnelData.supervisors.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {report.personnelData.supervisors.map((supervisor) => (
                <div key={supervisor.userId} className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <h4 className="font-medium">{supervisor.name}</h4>
                    <p className="text-sm text-muted-foreground">{supervisor.email}</p>
                  </div>
                  {showCommissions && (
                    <div className="text-right">
                      <div className="font-semibold text-green-600">
                        ${supervisor.commission.toFixed(2)}
                      </div>
                     
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* PAPs */}
      {report.personnelData.paps.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              {t('events.reports.details.paps')} ({report.personnelData.paps.length})
            </CardTitle>
            <p className="text-sm text-muted-foreground">
              {t('events.reports.details.timeRangesShownIn')} {userTimezone}
            </p>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {report.personnelData.paps.map((pap) => (
                <div key={pap.userId} className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <h4 className="font-medium">{pap.name}</h4>
                    <p className="text-sm text-muted-foreground">{pap.email}</p>
                    <div className="flex items-center gap-4 text-xs text-muted-foreground mt-1">
                      <span className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        {pap.hoursWorked}{t('events.reports.details.hoursWorked')}
                      </span>
                      <span>
                        {formatDateLocal(pap.timeRange.startTime, 'p', userTimezone)} - {formatDateLocal(pap.timeRange.endTime, 'p', userTimezone)}
                      </span>
                    </div>
                  </div>
                  {showCommissions && (
                    <div className="text-right">
                      <div className="font-semibold text-green-600">
                        ${pap.commission.toFixed(2)}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {t('events.reports.details.commissionEarned')}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Cooks */}
      {report.personnelData.cooks.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              {t('events.reports.details.cooks')} ({report.personnelData.cooks.length})
            </CardTitle>
            <p className="text-sm text-muted-foreground">
              {t('events.reports.details.timeRangesShownIn')} {userTimezone}
            </p>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {report.personnelData.cooks.map((cook) => (
                <div key={cook.userId} className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <h4 className="font-medium">{cook.name}</h4>
                    <p className="text-sm text-muted-foreground">{cook.email}</p>
                    <div className="flex items-center gap-4 text-xs text-muted-foreground mt-1">
                      <span className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        {cook.hoursWorked}{t('events.reports.details.hoursWorked')}
                      </span>
                      <span>{cook.percentage}{t('events.reports.details.percentageShare')}</span>
                      <span>
                        {formatDateLocal(cook.timeRange.startTime, 'p', userTimezone)} - {formatDateLocal(cook.timeRange.endTime, 'p', userTimezone)}
                      </span>
                    </div>
                  </div>
                  {showCommissions && (
                    <div className="text-right">
                      <div className="font-semibold text-green-600">
                        ${cook.commission.toFixed(2)}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {t('events.reports.details.commissionEarned')}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

function ReservationsTab({ report, showCommissions, userTimezone }: { report: EventReport; showCommissions: boolean; userTimezone: string }) {
  const { t } = useLanguage();
  return (
    <Card>
      <CardHeader>
        <CardTitle>{t('events.reports.details.linkedReservations')}</CardTitle>
      </CardHeader>
      <CardContent>
        {report.reservationData.linkedReservations.length > 0 ? (
          <div className="space-y-3">
            {report.reservationData.linkedReservations.map((reservation) => (
              <div key={reservation._id} className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <h4 className="font-medium">{reservation.customerName}</h4>
                  <p className="text-sm text-muted-foreground">
                    {reservation.partySize} {t('events.reports.details.guests')} • {formatDateLocal(reservation.reservationTime, 'PPP p', userTimezone)}
                  </p>
                </div>
                {showCommissions && (
                  <div className="text-right">
                    <div className="font-semibold text-green-600">
                      ${reservation.papCommission.toFixed(2)}
                    </div>
                    <div className="text-xs text-muted-foreground">{t('events.reports.details.papCommission')}</div>
                  </div>
                )}
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <Calendar className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">{t('events.reports.details.noReservationsLinked')}</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

function ReportHistory({ report, userTimezone }: { report: EventReport; userTimezone: string }) {
  const { t } = useLanguage();
  const historyItems = [
    {
      date: report.createdAt,
      action: t('events.reports.details.reportCreated'),
      status: 'pending',
      icon: FileText
    },
    ...(report.status !== 'pending' ? [{
      date: report.updatedAt,
      action: t('events.reports.details.reportProcessingStarted'),
      status: 'processing',
      icon: Edit
    }] : []),
    ...(report.submittedAt ? [{
      date: report.submittedAt,
      action: t('events.reports.details.reportSubmittedForValidation'),
      status: 'submitted',
      icon: Send
    }] : []),
    ...(report.validatedAt ? [{
      date: report.validatedAt,
      action: t('events.reports.details.reportValidated'),
      status: 'validated',
      icon: CheckCircle
    }] : [])
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t('events.reports.details.reportHistory')}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {historyItems.map((item, index) => {
            const Icon = item.icon;
            return (
              <div key={index} className="flex items-center gap-4">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center">
                    <Icon className="h-4 w-4 text-blue-600" />
                  </div>
                </div>
                <div className="flex-1">
                  <p className="font-medium">{item.action}</p>
                  <p className="text-sm text-muted-foreground">
                    {formatDateLocal(item.date, 'PPP p', userTimezone)}
                  </p>
                </div>
              </div>
            );
          })}
        </div>

        {report.notes && (
          <div className="mt-6 pt-6 border-t">
            <h4 className="font-medium mb-2">{t('events.reports.details.notes')}</h4>
            <p className="text-sm text-muted-foreground">{report.notes}</p>
          </div>
        )}

        {report.validationNotes && (
          <div className="mt-4">
            <h4 className="font-medium mb-2">{t('events.reports.details.validationNotes')}</h4>
            <p className="text-sm text-muted-foreground">{report.validationNotes}</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// Loading skeleton
function ReportDetailsPageSkeleton() {
  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center gap-4">
        <Skeleton className="h-9 w-20" />
        <div className="flex-1">
          <div className="flex items-center gap-3 mb-2">
            <Skeleton className="h-8 w-48" />
            <Skeleton className="h-5 w-20" />
          </div>
          <Skeleton className="h-4 w-64" />
        </div>
        <Skeleton className="h-9 w-24" />
      </div>

      <div className="space-y-4">
        <Skeleton className="h-10 w-full" />
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-32" />
            </CardHeader>
            <CardContent className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i}>
                  <Skeleton className="h-4 w-24 mb-1" />
                  <Skeleton className="h-4 w-full" />
                </div>
              ))}
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-32" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-12 w-24 mx-auto mb-2" />
              <Skeleton className="h-4 w-32 mx-auto mb-4" />
              <div className="grid grid-cols-3 gap-4">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="text-center">
                    <Skeleton className="h-6 w-8 mx-auto mb-1" />
                    <Skeleton className="h-3 w-12 mx-auto mb-1" />
                    <Skeleton className="h-4 w-16 mx-auto" />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
