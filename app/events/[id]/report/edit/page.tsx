'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter, useParams } from 'next/navigation';

import { useAppSelector } from '@/lib/redux/hooks';
import { canUserSuperviseEvents, canUserEditEventReports } from '@/lib/utils/permissions-utils';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  ArrowLeft,
  FileText,
  AlertTriangle
} from 'lucide-react';
import { toast } from 'sonner';
import { EventReportForm } from '@/components/events/EventReportForm';
import { useLanguage } from '@/lib/contexts/language-context';

interface EventReport {
  _id: string;
  status: 'pending' | 'processing' | 'submitted' | 'validated';
  eventId: {
    _id: string;
    name: string;
    startDate: string;
    endDate: string;
    location: string;
    branchId: {
      _id: string;
      name: string;
    };
    partnerId: {
      _id: string;
      name: string;
    };
    supervisors: Array<{
      _id: string;
      name: string;
      email: string;
    }>;
  };
  eventStartTime?: string;
  eventEndTime?: string;
  personnelData?: {
    supervisors: Array<{
      userId: string;
      name: string;
      email: string;
    }>;
    paps: Array<{
      userId: string;
      name: string;
      email: string;
      timeRange: {
        startTime: string;
        endTime: string;
      };
    }>;
    cooks: Array<{
      userId: string;
      name: string;
      email: string;
      timeRange: {
        startTime: string;
        endTime: string;
      };
      percentage: number;
    }>;
  };
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export default function ReportEditPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const params = useParams();
  const permissions = useAppSelector(state => state.permissions);
  const { t } = useLanguage();

  const [report, setReport] = useState<EventReport | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [saving, setSaving] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [reportId, setReportId] = useState<string | null>(null);

  const eventId = params?.id as string;

  // Check permissions using Redux store
  const hasReportPermissions = permissions && (
    canUserSuperviseEvents(permissions) ||
    canUserEditEventReports(permissions)
  );

  // Redirect if not authorized
  useEffect(() => {
    if (status === 'loading') return;
    
    if (!session) {
      router.push('/auth/signin');
      return;
    }
    
    if (!hasReportPermissions) {
      toast.error(t('events.reports.edit.noPermissionEditReports'));
      router.push('/');
      return;
    }
  }, [session, status, hasReportPermissions, router]);

  // Fetch event data to get report ID
  const fetchEventData = async () => {
    try {
      const response = await fetch(`/api/events/${eventId}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch event: ${response.statusText}`);
      }

      const eventData = await response.json();

      if (!eventData.reportId) {
        throw new Error(t('events.reports.edit.noReportFound'));
      }

      // Handle both populated and non-populated reportId
      const reportIdValue = typeof eventData.reportId === 'string'
        ? eventData.reportId
        : eventData.reportId._id;

      setReportId(reportIdValue);
      return reportIdValue;
    } catch (err) {
      console.error('Error fetching event data:', err);
      setError(err instanceof Error ? err.message : t('events.reports.edit.failedLoadEventData'));
      toast.error(t('events.reports.edit.failedLoadEventData'));
      throw err;
    }
  };

  // Fetch report details using report ID
  const fetchReport = async (reportIdToFetch?: string) => {
    try {
      const idToUse = reportIdToFetch || reportId;
      if (!idToUse) {
        throw new Error(t('events.reports.edit.noReportIdAvailable'));
      }

      const response = await fetch(`/api/event-reports/${idToUse}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch report: ${response.statusText}`);
      }

      const data = await response.json();
      setReport(data);
      setError(null);
    } catch (err) {
      console.error('Error fetching report:', err);
      setError(err instanceof Error ? err.message : t('events.reports.edit.failedLoadReportDetails'));
      toast.error(t('events.reports.edit.failedLoadReportDetails'));
    } finally {
      setLoading(false);
    }
  };

  // Combined fetch function
  const fetchReportData = async () => {
    try {
      setLoading(true);
      const fetchedReportId = await fetchEventData();
      await fetchReport(fetchedReportId);
    } catch (err) {
      // Error handling is done in individual functions
      setLoading(false);
    }
  };

  useEffect(() => {
    if (hasReportPermissions && eventId) {
      fetchReportData();
    }
  }, [hasReportPermissions, eventId]);

  // Handle save
  const handleSave = async (formData: any) => {
    if (!report) return;

    setSaving(true);
    try {
      const payload = {
        eventStartTime: formData.eventStartTime,
        eventEndTime: formData.eventEndTime,
        paps: formData.paps,
        cooks: formData.cooks,
        notes: formData.notes,
      };

      console.log('Saving report data:', payload);

      const response = await fetch(`/api/event-reports/${report._id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Save error details:', errorData);

        if (errorData.details && Array.isArray(errorData.details)) {
          const errorMessage = `${t('events.reports.edit.saveFailed')}:\n${errorData.details.join('\n')}`;
          throw new Error(errorMessage);
        } else {
          throw new Error(errorData.error || t('events.reports.edit.failedSaveReport'));
        }
      }

      // Refresh report data
      await fetchReport();
      toast.success(t('events.reports.edit.reportSavedSuccessfully'));
    } catch (err) {
      console.error('Error saving report:', err);
      toast.error(err instanceof Error ? err.message : t('events.reports.edit.failedSaveReport'));
      throw err;
    } finally {
      setSaving(false);
    }
  };

  // Handle submit
  const handleSubmit = async () => {
    if (!report) return;

    setSubmitting(true);
    try {
      const response = await fetch(`/api/event-reports/${report._id}/submit`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Submit validation error:', errorData);

        // Show detailed validation errors if available
        if (errorData.details && Array.isArray(errorData.details)) {
          const errorMessage = `${t('events.reports.edit.validationFailed')}:\n${errorData.details.join('\n')}`;
          toast.error(errorMessage);
        } else {
          toast.error(errorData.error || t('events.reports.edit.failedSubmitReport'));
        }
        return;
      }

      toast.success(t('events.reports.edit.reportSubmittedValidation'));
      router.push(`/events/${eventId}/report`);
    } catch (err) {
      console.error('Error submitting report:', err);
      toast.error(err instanceof Error ? err.message : t('events.reports.edit.failedSubmitReport'));
    } finally {
      setSubmitting(false);
    }
  };

  // Check if user can edit this specific report
  const canEditReport = report && (
    canUserEditEventReports(permissions) ||
    (canUserSuperviseEvents(permissions) && 
     report.eventId.supervisors?.some(s => s._id === session?.user?.id))
  );

  // Check if report can be edited (not validated)
  const isEditable = report && report.status !== 'validated';

  if (status === 'loading' || loading) {
    return <ReportEditPageSkeleton />;
  }

  if (error) {
    return (
      <div className="container mx-auto py-6">
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  if (!report) {
    return (
      <div className="container mx-auto py-6">
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{t('events.reports.edit.reportNotFound')}</AlertDescription>
        </Alert>
      </div>
    );
  }

  if (!canEditReport || !isEditable) {
    return (
      <div className="container mx-auto py-6">
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {!canEditReport
              ? t('events.reports.edit.noPermissionEdit')
              : t('events.reports.edit.cannotEditValidated')
            }
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => router.push(`/events/${eventId}/report`)}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          {t('events.reports.edit.backToReport')}
        </Button>

        <div className="flex-1">
          <div className="flex items-center gap-3 mb-2">
            <h1 className="text-3xl font-bold tracking-tight">
              {t('events.reports.edit.title')}
            </h1>
            <Badge variant={
              report.status === 'pending' ? 'secondary' :
              report.status === 'processing' ? 'default' :
              report.status === 'submitted' ? 'outline' :
              'destructive'
            }>
              {report.status}
            </Badge>
          </div>
          <p className="text-muted-foreground">
            {t('events.reports.edit.description')}
          </p>
        </div>
      </div>

      {/* Event Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            {report.eventId.name}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <p className="text-muted-foreground">{t('events.reports.edit.location')}</p>
              <p className="font-medium">{report.eventId.location}</p>
            </div>
            <div>
              <p className="text-muted-foreground">{t('events.reports.edit.branch')}</p>
              <p className="font-medium">{report.eventId.branchId.name}</p>
            </div>
            <div>
              <p className="text-muted-foreground">{t('events.reports.edit.partner')}</p>
              <p className="font-medium">{report.eventId.partnerId.name}</p>
            </div>
            <div>
              <p className="text-muted-foreground">{t('events.reports.edit.reportStatus')}</p>
              <p className="font-medium">{report.status}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Report Form */}
      <EventReportForm
        event={report.eventId}
        report={report}
        canEdit={true}
        onSave={handleSave}
        onSubmit={handleSubmit}
      />
    </div>
  );
}

// Loading skeleton
function ReportEditPageSkeleton() {
  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center gap-4">
        <Skeleton className="h-9 w-20" />
        <div className="flex-1">
          <div className="flex items-center gap-3 mb-2">
            <Skeleton className="h-8 w-48" />
            <Skeleton className="h-5 w-20" />
          </div>
          <Skeleton className="h-4 w-64" />
        </div>
      </div>

      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-32" />
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i}>
                <Skeleton className="h-4 w-16 mb-1" />
                <Skeleton className="h-4 w-24" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <div className="space-y-6">
        {[...Array(3)].map((_, i) => (
          <Card key={i}>
            <CardHeader>
              <Skeleton className="h-6 w-32" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-32 w-full" />
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
