'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter, useParams } from 'next/navigation';
import { useAppSelector } from '@/lib/redux/hooks';
import { canUserViewEvents, canUserEditEvents, canUserValidateEventReports, canUserSuperviseEvents } from '@/lib/utils/permissions-utils';
import { useEventContext } from '@/lib/contexts/event-context';
import { WorkflowStatus } from '@/components/events/WorkflowStatus';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Calendar, 
  MapPin, 
  Users, 
  Clock, 
  ArrowLeft,
  Edit,
  FileText,
  AlertTriangle,
  Building,
  Handshake,
  Target
} from 'lucide-react';
import { EventStatusBadge } from '../components/EventStatusBadge';
import { toast } from 'sonner';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { useLanguage } from '@/lib/contexts/language-context';

interface Event {
  _id: string;
  name: string;
  startDate: string;
  endDate: string;
  location: string;
  status: 'new' | 'in_progress' | 'cancelled' | 'processing_report' | 'awaiting_validation' | 'done';
  branchId: {
    _id: string;
    name: string;
  };
  partnerId: {
    _id: string;
    name: string;
  };
  eventTypeId?: {
    _id: string;
    name: string;
    code: string;
  };
  supervisors: Array<{
    _id: string;
    name: string;
    email: string;
  }>;
  agents?: {
    requiredCount?: number;
    assignedAgents?: Array<{
      _id: string;
      name: string;
      email: string;
    }>;
  };
  cooks: Array<{
    _id: string;
    name: string;
    email: string;
  }>;
  reportId?: {
    _id: string;
    status: 'pending' | 'processing' | 'submitted' | 'validated';
  };
  clientGoal?: number;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export default function EventDetailsPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const params = useParams();
  const permissions = useAppSelector(state => state.permissions);
  const { setCurrentEvent, setCurrentReport } = useEventContext();
  const { language, t } = useLanguage();

  const [event, setEvent] = useState<Event | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const eventId = params?.id as string;

  // Check permissions using Redux store
  const hasEventAccess = permissions && canUserViewEvents(permissions);
  const canEditEvents = permissions && canUserEditEvents(permissions);
  const canValidateReports = permissions && canUserValidateEventReports(permissions);
  const canSuperviseEvents = permissions && canUserSuperviseEvents(permissions);

  // Redirect if not authorized
  useEffect(() => {
    if (status === 'loading') return;
    
    if (!session) {
      router.push('/auth/signin');
      return;
    }
    
    if (!hasEventAccess) {
      toast.error('You do not have permission to view events');
      router.push('/');
      return;
    }
  }, [session, status, hasEventAccess, router]);

  // Fetch event details
  useEffect(() => {
    if (hasEventAccess && eventId) {
      fetchEvent();
    }
  }, [hasEventAccess, eventId]);

  const fetchEvent = async () => {
    try {
      setLoading(true);
      
      const response = await fetch(`/api/events/${eventId}`);
      
      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('Event not found');
        }
        throw new Error(`Failed to fetch event: ${response.statusText}`);
      }
      
      const data = await response.json();
      setEvent(data);
      setCurrentEvent(data);
      if (data.reportId) {
        setCurrentReport(data.reportId);
      }

      // Redirect supervisors to report interface if they are assigned to this event
      if (canSuperviseEvents && session?.user?.id) {
        const isAssignedSupervisor = data.supervisors?.some((s: any) => s._id === session.user.id);
        if (isAssignedSupervisor && data.reportId) {
          router.replace(`/events/${eventId}/report`);
          return;
        }
      }

      setError(null);
    } catch (err) {
      console.error('Error fetching event:', err);
      setError(err instanceof Error ? err.message : 'Failed to load event');
      toast.error('Failed to load event details');
    } finally {
      setLoading(false);
    }
  };

  const formatEventDate = (startDate: string, endDate: string) => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const locale = language === 'fr' ? fr : undefined;

    if (format(start, 'yyyy-MM-dd') === format(end, 'yyyy-MM-dd')) {
      // Same day
      return `${format(start, 'EEEE, MMMM d, yyyy', { locale })} • ${format(start, 'HH:mm')} - ${format(end, 'HH:mm')}`;
    } else {
      // Different days
      return `${format(start, 'EEEE, MMMM d, yyyy HH:mm', { locale })} - ${format(end, 'EEEE, MMMM d, yyyy HH:mm', { locale })}`;
    }
  };

  if (status === 'loading' || loading) {
    return <EventDetailsSkeleton />;
  }

  if (!session || !hasEventAccess) {
    return null; // Will redirect
  }

  if (error) {
    return (
      <div className="container mx-auto py-6">
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {error}
            <Button
              variant="outline"
              size="sm"
              className="ml-4"
              onClick={() => router.push('/events')}
            >
              {t('events.details.backToEvents')}
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  if (!event) {
    return (
      <div className="container mx-auto py-6">
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {t('events.details.eventNotFound')}
            <Button
              variant="outline"
              size="sm"
              className="ml-4"
              onClick={() => router.push('/events')}
            >
              {t('events.details.backToEvents')}
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            {t('common.back')}
          </Button>
          <div>
            <div className="flex items-center gap-3 mb-2">
              <h1 className="text-3xl font-bold tracking-tight">{event.name}</h1>
              <EventStatusBadge status={event.status} />
            </div>
            <p className="text-muted-foreground">{t('events.details.title')}</p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          {canEditEvents && (
            <Button
              onClick={() => router.push(`/events/${event._id}/edit`)}
              size="sm"
            >
              <Edit className="h-4 w-4 mr-2" />
              {t('events.editEvent')}
            </Button>
          )}
          <Button
            variant="outline"
            onClick={() => router.push(`/events/${event._id}/report`)}
            size="sm"
          >
            <FileText className="h-4 w-4 mr-2" />
            {event.reportId ? t('events.card.viewReport') : t('events.card.createReport')}
          </Button>
        </div>
      </div>

      {/* Event Information */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Details */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                {t('events.details.eventInformation')}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center gap-3">
                  <MapPin className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="text-sm text-muted-foreground">{t('events.location')}</p>
                    <p className="font-medium">{event.location}</p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <Clock className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="text-sm text-muted-foreground">{t('events.details.dateTime')}</p>
                    <p className="font-medium">{formatEventDate(event.startDate, event.endDate)}</p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <Building className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="text-sm text-muted-foreground">{t('events.branch')}</p>
                    <p className="font-medium">{event.branchId.name}</p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <Handshake className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="text-sm text-muted-foreground">{t('events.partner')}</p>
                    <p className="font-medium">{event.partnerId.name}</p>
                  </div>
                </div>

                {event.eventTypeId && (
                  <div className="flex items-center gap-3">
                    <FileText className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <p className="text-sm text-muted-foreground">{t('events.eventType')}</p>
                      <p className="font-medium">{event.eventTypeId.name} ({event.eventTypeId.code})</p>
                    </div>
                  </div>
                )}

                {event.clientGoal && (
                  <div className="flex items-center gap-3">
                    <Target className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <p className="text-sm text-muted-foreground">{t('events.clientGoal')}</p>
                      <p className="font-medium">{event.clientGoal} {t('events.card.clients')}</p>
                    </div>
                  </div>
                )}
              </div>

              {event.notes && (
                <div className="pt-4 border-t">
                  <p className="text-sm text-muted-foreground mb-2">{t('events.notes')}</p>
                  <p className="text-sm bg-muted p-3 rounded">{event.notes}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Personnel */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                {t('events.card.personnel')}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="text-sm text-muted-foreground mb-2">{t('events.card.supervisors')}</p>
                {event.supervisors.length > 0 ? (
                  <div className="flex flex-wrap gap-2">
                    {event.supervisors.map((supervisor) => (
                      <Badge key={supervisor._id} variant="secondary">
                        {supervisor.name}
                      </Badge>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-muted-foreground italic">{t('events.card.noneAssigned')}</p>
                )}
              </div>

              <div>
                <p className="text-sm text-muted-foreground mb-2">{t('events.card.papAgents')}</p>
                {event.agents?.assignedAgents && event.agents.assignedAgents.length > 0 ? (
                  <div className="flex flex-wrap gap-2">
                    {event.agents.assignedAgents.map((papAgent) => (
                      <Badge key={papAgent._id} variant="outline">
                        {papAgent.name}
                      </Badge>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-muted-foreground italic">{t('events.card.noneAssigned')}</p>
                )}
              </div>

              <div>
                <p className="text-sm text-muted-foreground mb-2">{t('events.card.cooks')}</p>
                {event.cooks.length > 0 ? (
                  <div className="flex flex-wrap gap-2">
                    {event.cooks.map((cook) => (
                      <Badge key={cook._id} variant="outline">
                        {cook.name}
                      </Badge>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-muted-foreground italic">{t('events.card.noneAssigned')}</p>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Workflow Status */}
          <WorkflowStatus
            event={event}
            report={event.reportId ? {
              _id: event.reportId._id,
              status: event.reportId.status,
              eventId: {
                _id: event._id,
                name: event.name
              }
            } : undefined}
            onStatusUpdate={fetchEvent}
          />

          {/* Quick Access Panel 
          <QuickAccessPanel
            event={event}
            report={event.reportId ? {
              _id: event.reportId._id,
              status: event.reportId.status,
              eventId: {
                _id: event._id,
                name: event.name
              }
            } : undefined}
          />
        */}
          {/* Status & Report */}
          <Card>
            <CardHeader>
              <CardTitle>{t('events.details.statusReport')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="text-sm text-muted-foreground mb-2">{t('events.details.eventStatus')}</p>
                <EventStatusBadge status={event.status} />
              </div>

              {event.reportId && (
                <div>
                  <p className="text-sm text-muted-foreground mb-2">{t('events.details.reportStatus')}</p>
                  <EventStatusBadge status={event.reportId.status} />
                </div>
              )}

              <div className="pt-4 border-t">
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => router.push(`/events/${event._id}/report`)}
                >
                  <FileText className="h-4 w-4 mr-2" />
                  {event.reportId ? t('events.card.viewReport') : t('events.card.createReport')}
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Validation Section - Only show for users with validation permissions */}
          {canValidateReports && event.reportId && event.reportId.status === 'submitted' && (
            <Card className="border-orange-200 bg-orange-50">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-orange-800">
                  <AlertTriangle className="h-5 w-5" />
                  {t('events.details.reportValidationRequired')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-sm text-orange-700">
                  {t('events.details.reportValidationMessage')}
                </p>

                <div className="flex flex-col gap-2">
                  <Button
                    onClick={() => event.reportId && router.push(`/events/validation/${event.reportId._id}?return=${encodeURIComponent(`/events/${event._id}`)}`)}
                    className="w-full"
                  >
                    <FileText className="h-4 w-4 mr-2" />
                    {t('events.details.validateReport')}
                  </Button>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => router.push('/events/validation')}
                    className="w-full"
                  >
                    {t('events.details.viewAllPendingReports')}
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Metadata */}
          <Card>
            <CardHeader>
              <CardTitle>{t('events.details.metadata')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2 text-sm">
              <div>
                <span className="text-muted-foreground">{t('common.created')}:</span>
                <span className="ml-2">{format(new Date(event.createdAt), 'MMM dd, yyyy HH:mm', { locale: language === 'fr' ? fr : undefined })}</span>
              </div>
              <div>
                <span className="text-muted-foreground">{t('common.updated')}:</span>
                <span className="ml-2">{format(new Date(event.updatedAt), 'MMM dd, yyyy HH:mm', { locale: language === 'fr' ? fr : undefined })}</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

// Loading skeleton component
function EventDetailsSkeleton() {
  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-4">
          <div className="h-9 w-16 bg-gray-200 rounded animate-pulse" />
          <div className="space-y-2">
            <div className="h-8 w-64 bg-gray-200 rounded animate-pulse" />
            <div className="h-4 w-32 bg-gray-200 rounded animate-pulse" />
          </div>
        </div>
        <div className="flex gap-2">
          <div className="h-9 w-24 bg-gray-200 rounded animate-pulse" />
          <div className="h-9 w-28 bg-gray-200 rounded animate-pulse" />
        </div>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          <div className="h-64 bg-gray-200 rounded animate-pulse" />
          <div className="h-48 bg-gray-200 rounded animate-pulse" />
        </div>
        <div className="space-y-6">
          <div className="h-32 bg-gray-200 rounded animate-pulse" />
          <div className="h-24 bg-gray-200 rounded animate-pulse" />
        </div>
      </div>
    </div>
  );
}
