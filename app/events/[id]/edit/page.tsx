'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useAppSelector } from '@/lib/redux/hooks';
import { canUserEditEvents } from '@/lib/utils/permissions-utils';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Users, FileText, ArrowLeft, Save, AlertTriangle, Phone } from 'lucide-react';
import { toast } from 'sonner';
import { createLocalDateTime, convertTo24HourFormat } from '@/lib/utils/date-utils';
import { SimplePersonnelManager } from '../../components/SimplePersonnelManager';
import { EventStatusBadge } from '../../components/EventStatusBadge';
import { DateTimePicker } from '../../components/DateTimePicker';
import PhoneField from '../../components/form/PhoneField';
import UserSelect from '../../components/form/UserSelect';
import { normalizePhoneNumber } from '../../components/form/phone-utils';
import { useLanguage } from '@/lib/contexts/language-context';

interface Event {
  _id: string;
  name: string;
  branchId: {
    _id: string;
    name: string;
  };
  partnerId: {
    _id: string;
    name: string;
  };
  eventTypeId: {
    _id: string;
    name: string;
    code: string;
  };
  startDate: string;
  endDate: string;
  location: string;
  clientGoal: number;
  notes: string;
  status: 'new' | 'in_progress' | 'cancelled' | 'processing_report' | 'awaiting_validation' | 'done';
  resId?: string;
  contactInfo?: {
    name: string;
    role: string;
    phone: string;
  };
  agents?: {
    requiredCount?: number;
    assignedAgents?: Array<{
      _id: string;
      name: string;
      email: string;
    }>;
  };
  supervisors: Array<{
    _id: string;
    name: string;
    email: string;
  }>;
  cooks: Array<{
    _id: string;
    name: string;
    email: string;
  }>;
  reportId?: {
    _id: string;
    status: string;
  };
}

interface Branch {
  _id: string;
  name: string;
}

interface Partner {
  _id: string;
  name: string;
}

interface EventType {
  _id: string;
  name: string;
  code: string;
}

interface User {
  _id: string;
  name: string;
  email: string;
  roles: any[];
  directPermissions: any[];
  isActive: boolean;
}

interface EventFormData {
  name: string;
  branchId: string;
  partnerId: string;
  eventTypeId: string;
  startDate: Date;
  startTime: string;
  endTime: string;
  location: string;
  clientGoal: number;
  notes: string;
  status: string;
  supervisors: string[];
  cooks: string[];
  resId: string;
  contactInfo: {
    name: string;
    role: string;
    phone: string;
  };
}

interface Personnel {
  supervisors: Array<{
    userId: string;
    name: string;
    email: string;
  }>;
  paps: Array<{
    userId: string;
    name: string;
    email: string;
  }>;
  cooks: Array<{
    userId: string;
    name: string;
    email: string;
  }>;
}

export default function EditEventPage() {
  const router = useRouter();
  const params = useParams();
  const eventId = params?.id as string;
  const permissions = useAppSelector(state => state.permissions);
  const canEditEvents = canUserEditEvents(permissions);
  const { t } = useLanguage();

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [event, setEvent] = useState<Event | null>(null);
  const [branches, setBranches] = useState<Branch[]>([]);
  const [partners, setPartners] = useState<Partner[]>([]);
  const [eventTypes, setEventTypes] = useState<EventType[]>([]);
  const [availableSupervisors, setAvailableSupervisors] = useState<User[]>([]);
  const [availableCooks, setAvailableCooks] = useState<User[]>([]);
  const [allUsers, setAllUsers] = useState<User[]>([]);

  const [formData, setFormData] = useState<EventFormData>({
    name: '',
    branchId: '',
    partnerId: '',
    eventTypeId: '',
    startDate: new Date(),
    startTime: '09:00',
    endTime: '17:00',
    location: '',
    clientGoal: 0,
    notes: '',
    status: 'new',
    supervisors: [],
    cooks: [],
    resId: '',
    contactInfo: {
      name: '',
      role: '',
      phone: '',
    },
  });

  const [personnel, setPersonnel] = useState<Personnel>({
    supervisors: [],
    paps: [],
    cooks: []
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [hasChanges, setHasChanges] = useState(false);

  // Check permissions
  useEffect(() => {
    if (!canEditEvents) {
      router.push('/access-denied');
      return;
    }
  }, [canEditEvents, router]);

  // Load event and form data
  useEffect(() => {
    if (canEditEvents && eventId) {
      loadEventData();
      loadFormData();
    }
  }, [canEditEvents, eventId]);

  const loadEventData = async () => {
    try {
      const response = await fetch(`/api/events/${eventId}`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch event: ${response.statusText}`);
      }
      
      const data = await response.json();
      const eventData = data; // API returns event directly, not wrapped in { event: ... }

      if (!eventData) {
        throw new Error('Event data not found in response');
      }

      setEvent(eventData);

      // Extract date and time from the existing datetime fields
      const startDate = new Date(eventData.startDate);
      const endDate = new Date(eventData.endDate);

      // Format time as HH:MM
      const startTime = startDate.toLocaleTimeString('en-US', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit'
      });
      const endTime = endDate.toLocaleTimeString('en-US', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit'
      });

      // Populate form data
      setFormData({
        name: eventData.name,
        branchId: eventData.branchId._id,
        partnerId: eventData.partnerId._id,
        eventTypeId: eventData.eventTypeId._id,
        startDate: startDate,
        startTime: startTime,
        endTime: endTime,
        location: eventData.location,
        clientGoal: eventData.clientGoal || 0,
        notes: eventData.notes || '',
        status: eventData.status,
        supervisors: eventData.supervisors.map((s: any) => s._id),
        cooks: eventData.cooks.map((c: any) => c._id),
        resId: eventData.resId || '',
        contactInfo: {
          name: eventData.contactInfo?.name || '',
          role: eventData.contactInfo?.role || '',
          phone: eventData.contactInfo?.phone || '',
        },
      });

      // Set initial personnel (simplified without time ranges)
      setPersonnel({
        supervisors: eventData.supervisors.map((s: any) => ({
          userId: s._id,
          name: s.name,
          email: s.email
        })),
        paps: eventData.agents?.assignedAgents ? eventData.agents.assignedAgents.map((p: any) => ({
          userId: p._id,
          name: p.name,
          email: p.email
        })) : [],
        cooks: eventData.cooks.map((c: any) => ({
          userId: c._id,
          name: c.name,
          email: c.email
        }))
      });
      
    } catch (error) {
      console.error('Error loading event:', error);
      toast.error(t('events.editPage.failedToLoadEventDetails'));
      router.push('/events');
    }
  };

  // Function to fetch all users from paginated API
  const fetchAllUsers = async (): Promise<User[]> => {
    let allUsers: User[] = [];
    let page = 1;
    let hasMore = true;
    const limit = 100; // Fetch in batches of 100

    while (hasMore) {
      const response = await fetch(`/api/users/dev?page=${page}&limit=${limit}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch users: ${response.statusText}`);
      }

      const data = await response.json();
      const users = data.users || [];

      allUsers = [...allUsers, ...users];

      // Check if we have more pages
      hasMore = page < (data.pagination?.totalPages || 1);
      page++;

      // Safety check to prevent infinite loops
      if (page > 50) {
        console.warn('Stopped fetching users after 50 pages to prevent infinite loop');
        break;
      }
    }

    return allUsers;
  };

  const loadFormData = async () => {
    try {
      setLoading(true);

      const [branchesRes, partnersRes, eventTypesRes, allUsers] = await Promise.all([
        fetch('/api/events/branches'),
        fetch('/api/events/partners'),
        fetch('/api/events/types'),
        fetchAllUsers()
      ]);

      if (!branchesRes.ok || !partnersRes.ok || !eventTypesRes.ok) {
        throw new Error('Failed to load form data');
      }

      const [branchesData, partnersData, eventTypesData] = await Promise.all([
        branchesRes.json(),
        partnersRes.json(),
        eventTypesRes.json()
      ]);

      // Filter users by role client-side
      const supervisors = allUsers.filter(user =>
        user.roles?.some((role: any) =>
          typeof role === 'string' ? role.toLowerCase().includes('supervisor') : role.name?.toLowerCase().includes('supervisor')
        )
      );

      const cooks = allUsers.filter(user =>
        user.roles?.some((role: any) =>
          typeof role === 'string' ? role.toLowerCase().includes('cook') : role.name?.toLowerCase().includes('cook')
        )
      );

      setBranches(Array.isArray(branchesData) ? branchesData : branchesData.data || []);
      setPartners(Array.isArray(partnersData) ? partnersData : partnersData.data || []);
      setEventTypes(Array.isArray(eventTypesData) ? eventTypesData : eventTypesData.data || []);
      setAvailableSupervisors(supervisors);
      setAvailableCooks(cooks);
      setAllUsers(allUsers);
    } catch (error) {
      console.error('Error loading form data:', error);
      toast.error(t('events.editPage.failedToLoadFormData'));
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof EventFormData, value: string | number | string[] | Date | { name: string; role: string; phone: string }) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    setHasChanges(true);

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = t('events.validation.eventNameRequired');
    }

    if (!formData.branchId) {
      newErrors.branchId = t('events.validation.branchRequired');
    }

    if (!formData.partnerId) {
      newErrors.partnerId = t('events.validation.partnerRequired');
    }

    if (!formData.eventTypeId) {
      newErrors.eventTypeId = t('events.validation.eventTypeRequired');
    }

    if (!formData.startDate) {
      newErrors.startDate = t('events.validation.startDateRequired');
    }

    if (!formData.startTime) {
      newErrors.startTime = 'Start time is required';
    }

    if (!formData.endTime) {
      newErrors.endTime = 'End time is required';
    }

    // Validate that end time is after start time (similar to calendar form)
    if (formData.startTime && formData.endTime) {
      try {
        const startTime24h = convertTo24HourFormat(formData.startTime);
        const endTime24h = convertTo24HourFormat(formData.endTime);

        const [startHours, startMinutes] = startTime24h.split(':').map(Number);
        const [endHours, endMinutes] = endTime24h.split(':').map(Number);

        const startTotalMinutes = startHours * 60 + startMinutes;
        const endTotalMinutes = endHours * 60 + endMinutes;

        if (endTotalMinutes <= startTotalMinutes) {
          newErrors.endTime = 'End time must be after start time';
        }
      } catch (error) {
        // If time conversion fails, add validation error
        if (!newErrors.startTime && formData.startTime) {
          newErrors.startTime = 'Invalid time format. Use HH:MM or HH:MM AM/PM';
        }
        if (!newErrors.endTime && formData.endTime) {
          newErrors.endTime = 'Invalid time format. Use HH:MM or HH:MM AM/PM';
        }
      }
    }

    if (!formData.location.trim()) {
      newErrors.location = t('events.validation.locationRequired');
    }

    if (personnel.supervisors.length === 0) {
      newErrors.supervisors = 'Exactly one supervisor is required';
    }

    // Check if event can be edited based on status
    if (event && ['done', 'cancelled'].includes(event.status)) {
      newErrors.status = 'Cannot edit completed or cancelled events';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      toast.error(t('events.validation.fixErrorsBeforeSubmit'));
      return;
    }

    setSaving(true);
    try {
      // Create UTC dates from date and time values (similar to calendar form)
      // Convert time formats to 24-hour format first, then create UTC dates
      const startTime24h = convertTo24HourFormat(formData.startTime);
      const endTime24h = convertTo24HourFormat(formData.endTime);

      const startDate = createLocalDateTime(formData.startDate, startTime24h);
      const endDate = createLocalDateTime(formData.startDate, endTime24h); // Same date for both start and end

      // Prepare event data
      const eventData = {
        _id: eventId,
        ...formData,
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        supervisors: personnel.supervisors.map(s => s.userId),
        cooks: personnel.cooks.map(c => c.userId),
        agents: {
          assignedAgents: personnel.paps.map(p => p.userId)
        },
        contactInfo: formData.contactInfo ? {
          ...formData.contactInfo,
          phone: formData.contactInfo.phone ? normalizePhoneNumber(formData.contactInfo.phone) : undefined,
        } : undefined,
        resId: formData.resId || undefined
      };

      const response = await fetch('/api/events', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(eventData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update event');
      }

      await response.json();
      toast.success(t('events.updateSuccess'));
      setHasChanges(false);

      // Navigate to event details page
      router.push(`/events/${eventId}`);
    } catch (error) {
      console.error('Error updating event:', error);
      toast.error(error instanceof Error ? error.message : t('events.editPage.failedToUpdateEvent'));
    } finally {
      setSaving(false);
    }
  };

  const handlePersonnelChange = (newPersonnel: Personnel) => {
    setPersonnel(newPersonnel);
    setHasChanges(true);

    // Clear supervisor error if exactly one supervisor is assigned
    if (newPersonnel.supervisors.length === 1 && errors.supervisors) {
      setErrors(prev => ({
        ...prev,
        supervisors: ''
      }));
    }
  };

  const canEditEvent = () => {
    return event && !['done', 'cancelled'].includes(event.status);
  };

  if (!canEditEvents) {
    return null;
  }

  if (loading || !event) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading event details...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            {t('common.back')}
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{t('events.editPage.title')}</h1>
            <p className="text-muted-foreground">{t('events.editPage.description')}</p>
          </div>
        </div>

        <EventStatusBadge status={event.status} />
      </div>

      {/* Status Warning */}
      {!canEditEvent() && (
        <Card className="border-amber-200 bg-amber-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-amber-800">
              <AlertTriangle className="h-5 w-5" />
              <p className="font-medium">
                This event cannot be edited because it is {event.status === 'done' ? 'completed' : 'cancelled'}.
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Event Details Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            {t('events.form.eventDetails')}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">{t('events.form.eventName')} *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder={t('events.form.enterEventName')}
                className={errors.name ? 'border-red-500' : ''}
                disabled={!canEditEvent()}
              />
              {errors.name && (
                <p className="text-sm text-red-500 flex items-center gap-1">
                  <AlertTriangle className="h-3 w-3" />
                  {errors.name}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="location">{t('events.form.location')} *</Label>
              <Input
                id="location"
                value={formData.location}
                onChange={(e) => handleInputChange('location', e.target.value)}
                placeholder={t('events.form.enterEventLocation')}
                className={errors.location ? 'border-red-500' : ''}
                disabled={!canEditEvent()}
              />
              {errors.location && (
                <p className="text-sm text-red-500 flex items-center gap-1">
                  <AlertTriangle className="h-3 w-3" />
                  {errors.location}
                </p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="branch">{t('events.form.branch')} *</Label>
              <Select
                value={formData.branchId}
                onValueChange={(value) => handleInputChange('branchId', value)}
                disabled={!canEditEvent()}
              >
                <SelectTrigger className={errors.branchId ? 'border-red-500' : ''}>
                  <SelectValue placeholder={t('events.form.selectBranch')} />
                </SelectTrigger>
                <SelectContent>
                  {branches && branches.length > 0 ? branches.map((branch) => (
                    <SelectItem key={branch._id} value={branch._id}>
                      {branch.name}
                    </SelectItem>
                  )) : (
                    <SelectItem value="no-branches" disabled>{t('events.form.noBranchesAvailable')}</SelectItem>
                  )}
                </SelectContent>
              </Select>
              {errors.branchId && (
                <p className="text-sm text-red-500 flex items-center gap-1">
                  <AlertTriangle className="h-3 w-3" />
                  {errors.branchId}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="partner">{t('events.partner')} *</Label>
              <Select
                value={formData.partnerId}
                onValueChange={(value) => handleInputChange('partnerId', value)}
                disabled={!canEditEvent()}
              >
                <SelectTrigger className={errors.partnerId ? 'border-red-500' : ''}>
                  <SelectValue placeholder={t('events.selectPartner')} />
                </SelectTrigger>
                <SelectContent>
                  {partners && partners.length > 0 ? partners.map((partner) => (
                    <SelectItem key={partner._id} value={partner._id}>
                      {partner.name}
                    </SelectItem>
                  )) : (
                    <SelectItem value="no-partners" disabled>{t('events.form.noPartnersAvailable')}</SelectItem>
                  )}
                </SelectContent>
              </Select>
              {errors.partnerId && (
                <p className="text-sm text-red-500 flex items-center gap-1">
                  <AlertTriangle className="h-3 w-3" />
                  {errors.partnerId}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="eventType">{t('events.type')} *</Label>
              <Select
                value={formData.eventTypeId}
                onValueChange={(value) => handleInputChange('eventTypeId', value)}
                disabled={!canEditEvent()}
              >
                <SelectTrigger className={errors.eventTypeId ? 'border-red-500' : ''}>
                  <SelectValue placeholder={t('events.selectEventType')} />
                </SelectTrigger>
                <SelectContent>
                  {eventTypes && eventTypes.length > 0 ? eventTypes.map((eventType) => (
                    <SelectItem key={eventType._id} value={eventType._id}>
                      {eventType.name} ({eventType.code})
                    </SelectItem>
                  )) : (
                    <SelectItem value="no-event-types" disabled>{t('events.form.noEventTypesAvailable')}</SelectItem>
                  )}
                </SelectContent>
              </Select>
              {errors.eventTypeId && (
                <p className="text-sm text-red-500 flex items-center gap-1">
                  <AlertTriangle className="h-3 w-3" />
                  {errors.eventTypeId}
                </p>
              )}
            </div>
          </div>

          {/* Date and Time */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="startDate">{t('events.date')} *</Label>
              <DateTimePicker
                type="date"
                value={formData.startDate ? formData.startDate.toISOString().split('T')[0] : ''}
                onChange={(value) => {
                  if (value) {
                    handleInputChange('startDate', new Date(value));
                  }
                }}
                placeholder={t('events.pickDate')}
                error={!!errors.startDate}
                disabled={!canEditEvent()}
              />
              {errors.startDate && (
                <p className="text-sm text-red-500 flex items-center gap-1">
                  <AlertTriangle className="h-3 w-3" />
                  {errors.startDate}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="startTime">{t('events.startTime')} *</Label>
              <Input
                id="startTime"
                value={formData.startTime}
                onChange={(e) => handleInputChange('startTime', e.target.value)}
                placeholder="HH:MM or HH:MM AM/PM"
                className={errors.startTime ? 'border-red-500' : ''}
                disabled={!canEditEvent()}
              />
              {errors.startTime && (
                <p className="text-sm text-red-500 flex items-center gap-1">
                  <AlertTriangle className="h-3 w-3" />
                  {errors.startTime}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="endTime">{t('events.endTime')} *</Label>
              <Input
                id="endTime"
                value={formData.endTime}
                onChange={(e) => handleInputChange('endTime', e.target.value)}
                placeholder="HH:MM or HH:MM AM/PM"
                className={errors.endTime ? 'border-red-500' : ''}
                disabled={!canEditEvent()}
              />
              {errors.endTime && (
                <p className="text-sm text-red-500 flex items-center gap-1">
                  <AlertTriangle className="h-3 w-3" />
                  {errors.endTime}
                </p>
              )}
            </div>
          </div>


        </CardContent>
      </Card>

      {/* Personnel Assignment */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            {t('events.form.personnelAssignment')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <SimplePersonnelManager
            initialPersonnel={personnel}
            onPersonnelChange={handlePersonnelChange}
            availableSupervisors={availableSupervisors}
            availableCooks={availableCooks}
            readOnly={!canEditEvent()}
            maxSupervisors={1}
          />
          {errors.supervisors && (
            <p className="text-sm text-red-500 flex items-center gap-1 mt-2">
              <AlertTriangle className="h-3 w-3" />
              {errors.supervisors}
            </p>
          )}
        </CardContent>
      </Card>

      {/* Additional Details and Contact Information */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Additional Details */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              {t('events.form.additionalDetails')}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="clientGoal">{t('events.clientGoal')}</Label>
              <Input
                id="clientGoal"
                type="number"
                value={formData.clientGoal || ''}
                onChange={(e) => handleInputChange('clientGoal', parseInt(e.target.value) || 0)}
                placeholder={t('events.form.expectedClients')}
                min="0"
                disabled={!canEditEvent()}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="notes">{t('events.notes')}</Label>
              <Textarea
                id="notes"
                value={formData.notes}
                onChange={(e) => handleInputChange('notes', e.target.value)}
                placeholder={t('events.form.additionalNotes')}
                rows={4}
                disabled={!canEditEvent()}
              />
            </div>
          </CardContent>
        </Card>

        {/* Contact Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Phone className="h-5 w-5" />
              {t('events.contactInfo')}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="resId">{t('events.responsibleUser')}</Label>
              <UserSelect
                users={allUsers}
                value={formData.resId}
                onValueChange={(value) => handleInputChange('resId', value)}
                disabled={loading || !canEditEvent()}
              />
            </div>

            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="contactName">{t('events.contactName')}</Label>
                <Input
                  id="contactName"
                  value={formData.contactInfo.name}
                  onChange={(e) => handleInputChange('contactInfo', { ...formData.contactInfo, name: e.target.value })}
                  placeholder={t('events.form.enterContactName')}
                  disabled={!canEditEvent()}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="contactRole">{t('events.contactRole')}</Label>
                <Input
                  id="contactRole"
                  value={formData.contactInfo.role}
                  onChange={(e) => handleInputChange('contactInfo', { ...formData.contactInfo, role: e.target.value })}
                  placeholder={t('events.form.enterContactRole')}
                  disabled={!canEditEvent()}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="contactPhone">{t('events.contactPhone')}</Label>
                <PhoneField
                  value={formData.contactInfo.phone}
                  onChange={(value: string) => handleInputChange('contactInfo', { ...formData.contactInfo, phone: value })}
                  placeholder={t('events.contactPhonePlaceholder')}
                  disabled={!canEditEvent()}
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end gap-4">
        <Button
          variant="outline"
          onClick={() => router.back()}
          disabled={saving}
        >
          {hasChanges ? t('events.editPage.cancel') : t('events.editPage.back')}
        </Button>
        {canEditEvent() && (
          <Button
            onClick={handleSubmit}
            disabled={saving || !hasChanges}
            className="min-w-[120px]"
          >
            {saving ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                {t('events.editPage.saving')}
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                {t('events.editPage.saveChanges')}
              </>
            )}
          </Button>
        )}
      </div>
    </div>
  );
}
